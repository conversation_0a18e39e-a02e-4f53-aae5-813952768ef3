#!/usr/bin/env python3
"""
测试诊断端点

使用增强的配置管理器和通用测试工具进行诊断测试。
"""

import sys
import asyncio
from pathlib import Path

# 使用通用测试工具设置环境
sys.path.insert(0, str(Path(__file__).parent / "src"))
from ai_gen_hub.utils.test_utils import (
    setup_test_environment, TestRunner, AppTestUtils, ConfigTestUtils
)

def test_diagnostic_endpoint():
    """测试诊断端点

    Returns:
        测试结果字典
    """
    try:
        app = AppTestUtils.create_test_app()
        result = AppTestUtils.test_endpoint(app, "/diagnostic", "GET")

        if result['success'] and result['status_code'] == 200:
            data = result['response_data']

            # 提取关键诊断信息
            diagnostic_info = {
                'environment': data.get('app_settings', {}).get('environment'),
                'debug_mode': data.get('app_settings', {}).get('debug'),
                'app_name': data.get('app_settings', {}).get('app_name'),
                'total_routes': data.get('routes', {}).get('total'),
                'debug_routes_count': data.get('routes', {}).get('debug_routes'),
                'settings_available': data.get('app_state', {}).get('settings_available'),
                'should_register_debug': data.get('debug_route_condition', {}).get('should_register')
            }

            result['diagnostic_info'] = diagnostic_info

            # 验证关键信息
            if not diagnostic_info['environment']:
                result['success'] = False
                result['error'] = "缺少环境信息"
            elif diagnostic_info['debug_routes_count'] == 0:
                result['success'] = False
                result['error'] = "没有注册调试路由"

        return result

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

async def test_with_lifespan():
    """测试带有lifespan的应用

    Returns:
        测试结果字典
    """
    try:
        app, app_instance = await AppTestUtils.create_test_app_async()

        # 测试调试端点
        result = AppTestUtils.test_endpoint(app, "/debug/api/system/info", "GET")

        if result['success'] and result['status_code'] == 200:
            data = result['response_data']
            system_info = data.get('system', {})

            result['system_info'] = {
                'uptime': system_info.get('uptime'),
                'cpu_percent': system_info.get('cpu_percent'),
                'memory_percent': system_info.get('memory_percent')
            }
        elif result['status_code'] == 403:
            result['access_denied'] = True
            result['error'] = "调试端点访问被拒绝（权限检查）"

        # 清理资源
        await app_instance.cleanup()

        return result

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """主测试函数"""
    # 设置测试环境
    setup_test_environment(debug=True, load_env=True)

    # 创建测试运行器
    runner = TestRunner("诊断功能测试")
    runner.start()

    # 运行配置测试
    runner.run_test("配置加载测试", ConfigTestUtils.test_config_loading, True)

    # 运行诊断测试
    runner.run_test("诊断端点测试", test_diagnostic_endpoint)

    # 运行异步测试
    async def run_lifespan_test():
        return await test_with_lifespan()

    runner.run_test("Lifespan应用测试", run_lifespan_test)

    # 完成测试
    all_passed = runner.finish()

    if all_passed:
        print("\n💡 诊断功能总结:")
        print("1. ✅ 诊断端点正常工作")
        print("2. ✅ 调试路由正确注册")
        print("3. ✅ 应用状态管理正常")
        print("4. ✅ 异步应用初始化正常")

    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
