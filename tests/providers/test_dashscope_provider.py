"""
DashScope Provider 单元测试

测试阿里云百炼 DashScope Provider 的各项功能，确保与 DashScope API 的正确集成。

测试覆盖：
- API 认证和健康检查
- 文本生成（流式和非流式）
- 错误处理
- 模型映射
- 请求构建和响应解析
"""

import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    Message,
    MessageRole,
    TextGenerationRequest,
)
from ai_gen_hub.providers.dashscope_provider import DashScopeProvider
from ai_gen_hub.utils.key_manager import KeyManager, APIKey


class TestDashScopeProvider:
    """DashScope Provider 测试类"""

    @pytest.fixture
    def provider_config(self):
        """创建测试用的供应商配置"""
        return ProviderConfig(
            name="dashscope",
            api_key="sk-test-key",
            timeout=30.0,
            max_retries=3,
            retry_delay=1.0
        )

    @pytest.fixture
    def key_manager(self):
        """创建测试用的密钥管理器"""
        key_manager = MagicMock(spec=KeyManager)
        api_key = APIKey(
            key="sk-test-dashscope-key-12345",
            provider="dashscope",
            rate_limit=1000
        )
        key_manager.get_key = AsyncMock(return_value=api_key)
        key_manager.record_request = AsyncMock()
        return key_manager

    @pytest.fixture
    def provider(self, provider_config, key_manager):
        """创建测试用的 DashScope Provider 实例"""
        return DashScopeProvider(provider_config, key_manager)

    def test_initialization(self, provider):
        """测试供应商初始化"""
        assert provider.name == "dashscope"
        assert provider.base_url == "https://dashscope.aliyuncs.com/api/v1"
        assert "qwen-max" in provider._supported_models
        assert "qwen-plus" in provider._supported_models
        assert "qwen-turbo" in provider._supported_models
        assert "qwen3-235b-a22b" in provider._supported_models
        assert "qwen2.5-72b-instruct" in provider._supported_models

    def test_model_mapping(self, provider):
        """测试模型名称映射"""
        # 测试直接支持的模型名称
        assert provider.map_model_name("qwen-max") == "qwen-max"
        assert provider.map_model_name("qwen3-32b") == "qwen3-32b"
        
        # 测试旧模型名称映射
        assert provider.map_model_name("qwen-max-1201") == "qwen-max"
        assert provider.map_model_name("qwen-plus-0919") == "qwen-plus"
        
        # 测试别名映射
        assert provider.map_model_name("qwen-latest") == "qwen-max"
        assert provider.map_model_name("qwen-fast") == "qwen-turbo"
        assert provider.map_model_name("qwen-balanced") == "qwen-plus"
        assert provider.map_model_name("qwen3-latest") == "qwen3-235b-a22b"
        assert provider.map_model_name("qwen-coder") == "qwen2.5-coder-32b-instruct"
        
        # 测试未知模型名称
        assert provider.map_model_name("unknown-model") == "unknown-model"

    @pytest.mark.asyncio
    async def test_health_check_success(self, provider):
        """测试健康检查成功"""
        with patch.object(provider, '_make_request') as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "output": {
                    "text": "Hello"
                },
                "usage": {
                    "total_tokens": 5
                },
                "request_id": "test-request-id"
            }
            mock_request.return_value = mock_response
            
            result = await provider._perform_health_check("sk-test-key")
            assert result is True
            
            # 验证请求参数
            mock_request.assert_called_once()
            args, kwargs = mock_request.call_args
            assert args[0] == "POST"
            assert "text-generation/generation" in args[1]
            assert kwargs["json_data"]["model"] == "qwen-turbo"

    @pytest.mark.asyncio
    async def test_health_check_failure(self, provider):
        """测试健康检查失败"""
        with patch.object(provider, '_make_request') as mock_request:
            mock_request.side_effect = Exception("网络错误")
            
            result = await provider._perform_health_check("sk-test-key")
            assert result is False

    def test_build_text_request_basic(self, provider):
        """测试基础文本请求构建"""
        request = TextGenerationRequest(
            model="qwen-max",
            messages=[
                Message(role=MessageRole.USER, content="你好，世界！")
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        request_data = provider._build_text_request(request)
        
        # 验证基础结构
        assert request_data["model"] == "qwen-max"
        assert "input" in request_data
        assert "parameters" in request_data
        
        # 验证消息格式
        messages = request_data["input"]["messages"]
        assert len(messages) == 1
        assert messages[0]["role"] == "user"
        assert messages[0]["content"] == "你好，世界！"
        
        # 验证参数
        params = request_data["parameters"]
        assert params["result_format"] == "message"
        assert params["max_tokens"] == 100
        assert params["temperature"] == 0.7

    def test_build_text_request_with_system_message(self, provider):
        """测试包含系统消息的文本请求构建"""
        request = TextGenerationRequest(
            model="qwen-plus",
            messages=[
                Message(role=MessageRole.SYSTEM, content="你是一个有用的助手。"),
                Message(role=MessageRole.USER, content="介绍一下人工智能。")
            ]
        )
        
        request_data = provider._build_text_request(request)
        
        # 验证消息格式
        messages = request_data["input"]["messages"]
        assert len(messages) == 2
        assert messages[0]["role"] == "system"
        assert messages[0]["content"] == "你是一个有用的助手。"
        assert messages[1]["role"] == "user"
        assert messages[1]["content"] == "介绍一下人工智能。"

    def test_build_text_request_with_stream(self, provider):
        """测试流式请求构建"""
        request = TextGenerationRequest(
            model="qwen-turbo",
            messages=[
                Message(role=MessageRole.USER, content="写一个故事")
            ],
            stream=True
        )
        
        request_data = provider._build_text_request(request)
        
        # 验证流式参数
        assert request_data["parameters"]["incremental_output"] is True

    def test_build_text_request_with_stop_words(self, provider):
        """测试包含停止词的请求构建"""
        request = TextGenerationRequest(
            model="qwen-max",
            messages=[
                Message(role=MessageRole.USER, content="计数到10")
            ],
            stop=["10", "十"]
        )
        
        request_data = provider._build_text_request(request)
        
        # 验证停止词
        assert request_data["parameters"]["stop"] == ["10", "十"]

    def test_handle_dashscope_error_format1(self, provider):
        """测试 DashScope 错误格式1处理"""
        error_response = {
            "code": "InvalidApiKey",
            "message": "Invalid API key provided.",
            "request_id": "test-request-id"
        }
        
        error_msg = provider._handle_dashscope_error(error_response)
        
        assert "DashScope API 错误 [InvalidApiKey]" in error_msg
        assert "Invalid API key provided." in error_msg
        assert "请求ID: test-request-id" in error_msg

    def test_handle_dashscope_error_format2(self, provider):
        """测试 DashScope 错误格式2处理"""
        error_response = {
            "error": {
                "message": "Rate limit exceeded.",
                "type": "rate_limit_error",
                "code": "rate_limit_exceeded"
            }
        }
        
        error_msg = provider._handle_dashscope_error(error_response)
        
        assert "DashScope API 错误 [rate_limit_exceeded]" in error_msg
        assert "rate_limit_error" in error_msg
        assert "Rate limit exceeded." in error_msg

    @pytest.mark.asyncio
    async def test_text_generation_success(self, provider, key_manager):
        """测试文本生成成功"""
        request = TextGenerationRequest(
            model="qwen-max",
            messages=[
                Message(role=MessageRole.USER, content="你好")
            ]
        )
        
        # 模拟 API 响应
        mock_response_data = {
            "output": {
                "choices": [
                    {
                        "message": {
                            "role": "assistant",
                            "content": "你好！我是通义千问，很高兴为您服务。"
                        },
                        "finish_reason": "stop"
                    }
                ]
            },
            "usage": {
                "input_tokens": 2,
                "output_tokens": 15,
                "total_tokens": 17
            },
            "request_id": "test-request-id"
        }
        
        with patch.object(provider, '_make_request') as mock_request:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_response_data
            mock_request.return_value = mock_response
            
            response = await provider._generate_text_impl(request, "sk-test-key")
            
            # 验证响应
            assert response.model == "qwen-max"
            assert len(response.choices) == 1
            assert response.choices[0].message.content == "你好！我是通义千问，很高兴为您服务。"
            assert response.choices[0].finish_reason == "stop"
            assert response.usage.prompt_tokens == 2
            assert response.usage.completion_tokens == 15
            assert response.usage.total_tokens == 17

    def test_parse_text_response_old_format(self, provider):
        """测试解析旧格式的文本响应"""
        response_data = {
            "output": {
                "text": "这是一个测试响应"
            },
            "usage": {
                "prompt_tokens": 5,
                "completion_tokens": 8,
                "total_tokens": 13
            },
            "request_id": "test-request-id"
        }
        
        request = TextGenerationRequest(
            model="qwen-turbo",
            messages=[Message(role=MessageRole.USER, content="测试")]
        )
        
        response = provider._parse_text_response(response_data, request)
        
        # 验证解析结果
        assert response.model == "qwen-turbo"
        assert len(response.choices) == 1
        assert response.choices[0].message.content == "这是一个测试响应"
        assert response.choices[0].finish_reason == "stop"
        assert response.usage.prompt_tokens == 5
        assert response.usage.completion_tokens == 8
        assert response.usage.total_tokens == 13

    @pytest.mark.asyncio
    async def test_image_generation_not_supported(self, provider):
        """测试图像生成不支持"""
        request = ImageGenerationRequest(
            prompt="一只可爱的小猫",
            model="qwen-max"
        )
        
        with pytest.raises(NotImplementedError) as exc_info:
            await provider._generate_image_impl(request, "sk-test-key")
        
        assert "DashScope 暂不支持图像生成功能" in str(exc_info.value)

    def test_get_default_headers(self, provider):
        """测试获取默认请求头"""
        headers = provider._get_default_headers()
        
        assert "User-Agent" in headers
        assert "AI-Gen-Hub/1.0.0 (dashscope)" in headers["User-Agent"]
        assert headers["Content-Type"] == "application/json"
