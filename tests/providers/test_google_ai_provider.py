"""
Google AI Provider 单元测试

测试 Google AI Provider 的各项功能，确保与 Gemini API 的正确集成。

测试覆盖：
- API 认证和健康检查
- 文本生成（流式和非流式）
- 图像生成
- 错误处理
- 模型映射
- Thinking 功能
- 安全设置
- 工具集成
"""

import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    Message,
    MessageRole,
    TextGenerationRequest,
)
from ai_gen_hub.providers.google_ai_provider import GoogleAIProvider
from ai_gen_hub.utils.key_manager import KeyManager, APIKey


class TestGoogleAIProvider:
    """Google AI Provider 测试类"""

    @pytest.fixture
    def provider_config(self):
        """创建测试用的供应商配置"""
        return ProviderConfig(
            name="google_ai",
            api_key="test-api-key",
            timeout=30.0,
            max_retries=3,
            retry_delay=1.0
        )

    @pytest.fixture
    def key_manager(self):
        """创建测试用的密钥管理器"""
        key_manager = MagicMock(spec=KeyManager)
        api_key = APIKey(
            key="test-api-key-12345",
            provider="google_ai",
            rate_limit=1000
        )
        key_manager.get_key = AsyncMock(return_value=api_key)
        key_manager.record_request = AsyncMock()
        return key_manager

    @pytest.fixture
    def provider(self, provider_config, key_manager):
        """创建测试用的 Google AI Provider 实例"""
        return GoogleAIProvider(provider_config, key_manager)

    def test_initialization(self, provider):
        """测试供应商初始化"""
        assert provider.name == "google_ai"
        assert provider.base_url == "https://generativelanguage.googleapis.com/v1beta"
        assert "gemini-2.5-pro" in provider._supported_models
        assert "gemini-2.5-flash" in provider._supported_models
        assert "gemini-2.0-flash" in provider._supported_models

    def test_model_mapping(self, provider):
        """测试模型名称映射"""
        # 测试新模型名称（不需要映射）
        assert provider.map_model_name("gemini-2.5-flash") == "gemini-2.5-flash"
        
        # 测试旧模型名称映射
        assert provider.map_model_name("gemini-pro") == "gemini-2.5-pro"
        assert provider.map_model_name("gemini-pro-vision") == "gemini-2.5-pro"
        assert provider.map_model_name("gemini-1.0-pro") == "gemini-2.5-pro"
        
        # 测试别名映射
        assert provider.map_model_name("gemini-latest") == "gemini-2.5-flash"
        assert provider.map_model_name("gemini-fast") == "gemini-2.5-flash"
        assert provider.map_model_name("gemini-lite") == "gemini-2.5-flash-lite"

    @pytest.mark.asyncio
    async def test_health_check_success(self, provider, key_manager):
        """测试健康检查成功"""
        with patch.object(provider, '_make_request') as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_request.return_value = mock_response
            
            result = await provider._perform_health_check("test-api-key")
            assert result is True
            
            # 验证请求参数
            mock_request.assert_called_once_with(
                "GET",
                "https://generativelanguage.googleapis.com/v1beta/models?key=test-api-key"
            )

    @pytest.mark.asyncio
    async def test_health_check_failure(self, provider):
        """测试健康检查失败"""
        with patch.object(provider, '_make_request') as mock_request:
            mock_request.side_effect = Exception("网络错误")
            
            result = await provider._perform_health_check("test-api-key")
            assert result is False

    def test_build_text_request_basic(self, provider):
        """测试基础文本请求构建"""
        request = TextGenerationRequest(
            model="gemini-2.5-flash",
            messages=[
                Message(role=MessageRole.USER, content="你好，世界！")
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        request_data = provider._build_text_request(request)
        
        # 验证基础结构
        assert "contents" in request_data
        assert "generationConfig" in request_data
        
        # 验证内容格式
        contents = request_data["contents"]
        assert len(contents) == 1
        assert contents[0]["role"] == "user"
        assert contents[0]["parts"][0]["text"] == "你好，世界！"
        
        # 验证生成配置
        gen_config = request_data["generationConfig"]
        assert gen_config["maxOutputTokens"] == 100
        assert gen_config["temperature"] == 0.7

    def test_build_text_request_with_system_message(self, provider):
        """测试包含系统消息的文本请求构建"""
        request = TextGenerationRequest(
            model="gemini-2.5-pro",
            messages=[
                Message(role=MessageRole.SYSTEM, content="你是一个有用的助手。"),
                Message(role=MessageRole.USER, content="介绍一下人工智能。")
            ]
        )
        
        request_data = provider._build_text_request(request)
        
        # 验证系统指令
        assert "system_instruction" in request_data
        system_instruction = request_data["system_instruction"]
        assert system_instruction["parts"][0]["text"] == "你是一个有用的助手。"
        
        # 验证对话内容不包含系统消息
        contents = request_data["contents"]
        assert len(contents) == 1
        assert contents[0]["role"] == "user"
        assert contents[0]["parts"][0]["text"] == "介绍一下人工智能。"

    def test_build_text_request_with_thinking_config(self, provider):
        """测试 Thinking 配置"""
        request = TextGenerationRequest(
            model="gemini-2.5-pro",  # 支持 thinking 的模型
            messages=[
                Message(role=MessageRole.USER, content="解决这个数学问题：2+2=?")
            ],
            thinking_budget=1000
        )
        
        request_data = provider._build_text_request(request)
        
        # 验证 thinking 配置
        gen_config = request_data["generationConfig"]
        assert "thinkingConfig" in gen_config
        assert gen_config["thinkingConfig"]["thinkingBudget"] == 1000

    def test_build_text_request_non_thinking_model(self, provider):
        """测试非 Thinking 模型不添加 thinking 配置"""
        request = TextGenerationRequest(
            model="gemini-1.5-flash",  # 不支持 thinking 的模型
            messages=[
                Message(role=MessageRole.USER, content="你好")
            ],
            thinking_budget=1000
        )
        
        request_data = provider._build_text_request(request)
        
        # 验证没有 thinking 配置
        gen_config = request_data.get("generationConfig", {})
        assert "thinkingConfig" not in gen_config

    def test_validate_json_schema(self, provider):
        """测试 JSON Schema 验证"""
        # 有效的 schema
        valid_schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer"}
            }
        }
        assert provider._validate_json_schema(valid_schema) is True
        
        # 无效的 schema（缺少 type）
        invalid_schema = {
            "properties": {
                "name": {"type": "string"}
            }
        }
        assert provider._validate_json_schema(invalid_schema) is False
        
        # 无效的类型
        invalid_type_schema = {
            "type": "invalid_type"
        }
        assert provider._validate_json_schema(invalid_type_schema) is False

    def test_handle_gemini_error(self, provider):
        """测试 Gemini API 错误处理"""
        error_response = {
            "error": {
                "code": 400,
                "message": "Invalid model name provided.",
                "status": "INVALID_ARGUMENT",
                "details": [
                    {
                        "@type": "type.googleapis.com/google.rpc.BadRequest",
                        "fieldViolations": [
                            {
                                "field": "model",
                                "description": "Model not found"
                            }
                        ]
                    }
                ]
            }
        }
        
        error_msg = provider._handle_gemini_error(error_response)
        
        assert "Gemini API 错误 [400]" in error_msg
        assert "INVALID_ARGUMENT" in error_msg
        assert "Invalid model name provided." in error_msg
        assert "详细信息" in error_msg

    def test_build_image_request(self, provider):
        """测试图像生成请求构建"""
        request = ImageGenerationRequest(
            prompt="一只可爱的小猫",
            model="gemini-2.0-flash",
            response_modalities=["TEXT", "IMAGE"]
        )
        
        request_data = provider._build_image_request(request)
        
        # 验证基础结构
        assert "contents" in request_data
        assert "generationConfig" in request_data
        
        # 验证内容
        contents = request_data["contents"]
        assert len(contents) == 1
        assert contents[0]["role"] == "user"
        assert contents[0]["parts"][0]["text"] == "一只可爱的小猫"
        
        # 验证生成配置
        gen_config = request_data["generationConfig"]
        assert gen_config["responseModalities"] == ["TEXT", "IMAGE"]

    @pytest.mark.asyncio
    async def test_text_generation_success(self, provider, key_manager):
        """测试文本生成成功"""
        request = TextGenerationRequest(
            model="gemini-2.5-flash",
            messages=[
                Message(role=MessageRole.USER, content="你好")
            ]
        )
        
        # 模拟 API 响应
        mock_response_data = {
            "candidates": [
                {
                    "content": {
                        "parts": [
                            {"text": "你好！我是 Gemini，很高兴为您服务。"}
                        ]
                    },
                    "finishReason": "STOP"
                }
            ],
            "usageMetadata": {
                "promptTokenCount": 2,
                "candidatesTokenCount": 15,
                "totalTokenCount": 17
            }
        }
        
        with patch.object(provider, '_make_request') as mock_request:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_response_data
            mock_request.return_value = mock_response
            
            response = await provider._generate_text_impl(request, "test-api-key")
            
            # 验证响应
            assert response.model == "gemini-2.5-flash"
            assert len(response.choices) == 1
            assert response.choices[0].message.content == "你好！我是 Gemini，很高兴为您服务。"
            assert response.choices[0].finish_reason == "stop"
            assert response.usage.prompt_tokens == 2
            assert response.usage.completion_tokens == 15
            assert response.usage.total_tokens == 17

    def test_finish_reason_mapping(self, provider):
        """测试结束原因映射"""
        # 在 _parse_text_response 中测试映射
        response_data = {
            "candidates": [
                {"content": {"parts": [{"text": "测试"}]}, "finishReason": "MAX_TOKENS"},
                {"content": {"parts": [{"text": "测试"}]}, "finishReason": "SAFETY"},
                {"content": {"parts": [{"text": "测试"}]}, "finishReason": "RECITATION"},
                {"content": {"parts": [{"text": "测试"}]}, "finishReason": "SPII"},
            ],
            "usageMetadata": {"totalTokenCount": 10}
        }
        
        request = TextGenerationRequest(
            model="gemini-2.5-flash",
            messages=[Message(role=MessageRole.USER, content="测试")]
        )
        
        response = provider._parse_text_response(response_data, request)
        
        # 验证映射
        assert response.choices[0].finish_reason == "length"      # MAX_TOKENS -> length
        assert response.choices[1].finish_reason == "content_filter"  # SAFETY -> content_filter
        assert response.choices[2].finish_reason == "content_filter"  # RECITATION -> content_filter
        assert response.choices[3].finish_reason == "content_filter"  # SPII -> content_filter
