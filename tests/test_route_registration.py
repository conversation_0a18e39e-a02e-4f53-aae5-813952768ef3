"""
路由注册单元测试

验证调试路由在不同环境下的注册行为
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient


class TestRouteRegistration:
    """测试路由注册功能"""
    
    def setup_method(self):
        """测试前设置"""
        # 保存原始环境变量
        self.original_env = {
            'DEBUG': os.environ.get('DEBUG'),
            'ENVIRONMENT': os.environ.get('ENVIRONMENT'),
        }
    
    def teardown_method(self):
        """测试后清理"""
        # 恢复原始环境变量
        for key, value in self.original_env.items():
            if value is not None:
                os.environ[key] = value
            elif key in os.environ:
                del os.environ[key]
    
    def test_debug_routes_registered_in_development(self):
        """测试开发环境下调试路由被注册"""
        # 设置开发环境
        os.environ['DEBUG'] = 'true'
        os.environ['ENVIRONMENT'] = 'development'
        
        # 重新导入以应用新的环境变量
        import sys
        if 'ai_gen_hub.config.settings' in sys.modules:
            del sys.modules['ai_gen_hub.config.settings']
        if 'ai_gen_hub.api.app' in sys.modules:
            del sys.modules['ai_gen_hub.api.app']
        
        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        
        # 检查调试路由是否存在
        debug_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and '/debug' in route.path:
                debug_routes.append(route.path)
        
        assert len(debug_routes) > 0, "开发环境下应该注册调试路由"
        assert '/debug/' in debug_routes, "应该包含调试主页路由"
        assert '/debug/api/system/info' in debug_routes, "应该包含系统信息API路由"
    
    def test_debug_routes_not_registered_in_production(self):
        """测试生产环境下调试路由不被注册"""
        # 设置生产环境
        os.environ['DEBUG'] = 'false'
        os.environ['ENVIRONMENT'] = 'production'
        
        # 重新导入以应用新的环境变量
        import sys
        if 'ai_gen_hub.config.settings' in sys.modules:
            del sys.modules['ai_gen_hub.config.settings']
        if 'ai_gen_hub.api.app' in sys.modules:
            del sys.modules['ai_gen_hub.api.app']
        
        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        
        # 检查调试路由是否存在
        debug_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and '/debug' in route.path:
                debug_routes.append(route.path)
        
        assert len(debug_routes) == 0, "生产环境下不应该注册调试路由"
    
    def test_debug_routes_registered_when_debug_true(self):
        """测试DEBUG=true时调试路由被注册（即使不是development环境）"""
        # 设置调试模式但非开发环境
        os.environ['DEBUG'] = 'true'
        os.environ['ENVIRONMENT'] = 'staging'
        
        # 重新导入以应用新的环境变量
        import sys
        if 'ai_gen_hub.config.settings' in sys.modules:
            del sys.modules['ai_gen_hub.config.settings']
        if 'ai_gen_hub.api.app' in sys.modules:
            del sys.modules['ai_gen_hub.api.app']
        
        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        
        # 检查调试路由是否存在
        debug_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and '/debug' in route.path:
                debug_routes.append(route.path)
        
        assert len(debug_routes) > 0, "DEBUG=true时应该注册调试路由"
    
    def test_diagnostic_endpoint_always_available(self):
        """测试诊断端点在所有环境下都可用"""
        # 测试生产环境
        os.environ['DEBUG'] = 'false'
        os.environ['ENVIRONMENT'] = 'production'
        
        # 重新导入以应用新的环境变量
        import sys
        if 'ai_gen_hub.config.settings' in sys.modules:
            del sys.modules['ai_gen_hub.config.settings']
        if 'ai_gen_hub.api.app' in sys.modules:
            del sys.modules['ai_gen_hub.api.app']
        
        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        client = TestClient(app)
        
        # 测试诊断端点
        response = client.get("/diagnostic")
        assert response.status_code == 200, "诊断端点应该在所有环境下都可用"
        
        data = response.json()
        assert "app_settings" in data
        assert "routes" in data
        assert "debug_route_condition" in data
    
    def test_route_verification_logging(self):
        """测试路由验证日志功能"""
        os.environ['DEBUG'] = 'true'
        os.environ['ENVIRONMENT'] = 'development'
        
        # 重新导入以应用新的环境变量
        import sys
        if 'ai_gen_hub.config.settings' in sys.modules:
            del sys.modules['ai_gen_hub.config.settings']
        if 'ai_gen_hub.api.app' in sys.modules:
            del sys.modules['ai_gen_hub.api.app']
        
        with patch('ai_gen_hub.core.logging.get_logger') as mock_logger:
            mock_log = MagicMock()
            mock_logger.return_value = mock_log
            
            from ai_gen_hub.api.app import create_app
            
            app = create_app()
            
            # 验证日志调用
            mock_log.info.assert_any_call("开始注册路由")
            mock_log.info.assert_any_call("注册调试路由: /debug")
            mock_log.info.assert_any_call("✅ 调试路由注册成功")
            mock_log.info.assert_any_call("验证路由注册状态")
    
    def test_config_loading_with_debug_logging(self):
        """测试配置加载的调试日志功能"""
        os.environ['DEBUG'] = 'true'
        os.environ['ENVIRONMENT'] = 'development'
        
        # 重新导入以应用新的环境变量
        import sys
        if 'ai_gen_hub.config.settings' in sys.modules:
            del sys.modules['ai_gen_hub.config.settings']
        
        from ai_gen_hub.config.settings import get_settings
        
        # 测试带调试日志的配置加载
        settings = get_settings(debug_logging=True)
        
        assert settings.debug is True
        assert settings.environment == 'development'
        assert settings.app_name == 'AI Gen Hub'


class TestAuthenticationMiddleware:
    """测试认证中间件对调试路径的处理"""
    
    def test_debug_paths_public_in_development(self):
        """测试开发环境下调试路径是公开的"""
        os.environ['DEBUG'] = 'true'
        os.environ['ENVIRONMENT'] = 'development'
        
        from ai_gen_hub.api.middleware import AuthenticationMiddleware
        from fastapi import FastAPI
        
        app = FastAPI()
        middleware = AuthenticationMiddleware(app)
        
        # 测试调试路径
        assert middleware._is_public_path("/debug/") is True
        assert middleware._is_public_path("/debug/api/system/info") is True
        assert middleware._is_public_path("/diagnostic") is True
    
    def test_debug_paths_not_public_in_production(self):
        """测试生产环境下调试路径不是公开的"""
        os.environ['DEBUG'] = 'false'
        os.environ['ENVIRONMENT'] = 'production'
        
        from ai_gen_hub.api.middleware import AuthenticationMiddleware
        from fastapi import FastAPI
        
        app = FastAPI()
        middleware = AuthenticationMiddleware(app)
        
        # 测试调试路径
        assert middleware._is_public_path("/debug/") is False
        assert middleware._is_public_path("/debug/api/system/info") is False
        # 诊断端点在生产环境下仍然是公开的
        assert middleware._is_public_path("/diagnostic") is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
