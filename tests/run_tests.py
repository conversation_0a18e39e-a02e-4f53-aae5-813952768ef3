#!/usr/bin/env python3
"""
AI Gen Hub 测试运行器

提供便捷的测试运行和报告功能
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("警告:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        print("标准输出:", e.stdout)
        print("错误输出:", e.stderr)
        return False


def run_unit_tests(verbose=False, coverage=False):
    """运行单元测试"""
    cmd = ["python", "-m", "pytest", "tests/unit/"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend([
            "--cov=ai_gen_hub",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing"
        ])
    
    cmd.extend(["-m", "unit"])
    
    return run_command(cmd, "单元测试")


def run_integration_tests(verbose=False):
    """运行集成测试"""
    cmd = ["python", "-m", "pytest", "tests/integration/"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["-m", "integration"])
    
    return run_command(cmd, "集成测试")


def run_all_tests(verbose=False, coverage=False):
    """运行所有测试"""
    cmd = ["python", "-m", "pytest", "tests/"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend([
            "--cov=ai_gen_hub",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing"
        ])
    
    return run_command(cmd, "所有测试")


def run_linting():
    """运行代码检查"""
    success = True
    
    # Black 格式化检查
    if not run_command(
        ["python", "-m", "black", "--check", "--diff", "src/", "tests/"],
        "Black 代码格式检查"
    ):
        success = False
    
    # isort 导入排序检查
    if not run_command(
        ["python", "-m", "isort", "--check-only", "--diff", "src/", "tests/"],
        "isort 导入排序检查"
    ):
        success = False
    
    # flake8 代码风格检查
    if not run_command(
        ["python", "-m", "flake8", "src/", "tests/"],
        "flake8 代码风格检查"
    ):
        success = False
    
    # mypy 类型检查
    if not run_command(
        ["python", "-m", "mypy", "src/ai_gen_hub/"],
        "mypy 类型检查"
    ):
        success = False
    
    return success


def run_security_check():
    """运行安全检查"""
    success = True
    
    # bandit 安全检查
    if not run_command(
        ["python", "-m", "bandit", "-r", "src/", "-f", "json", "-o", "bandit-report.json"],
        "bandit 安全检查"
    ):
        success = False
    
    # safety 依赖安全检查
    if not run_command(
        ["python", "-m", "safety", "check", "--json", "--output", "safety-report.json"],
        "safety 依赖安全检查"
    ):
        success = False
    
    return success


def generate_test_report():
    """生成测试报告"""
    print("\n" + "="*60)
    print("生成测试报告")
    print("="*60)
    
    # 运行测试并生成报告
    cmd = [
        "python", "-m", "pytest",
        "tests/",
        "--cov=ai_gen_hub",
        "--cov-report=html:htmlcov",
        "--cov-report=xml:coverage.xml",
        "--junitxml=test-results.xml",
        "--html=test-report.html",
        "--self-contained-html"
    ]
    
    if run_command(cmd, "生成测试报告"):
        print("\n测试报告已生成:")
        print("- HTML覆盖率报告: htmlcov/index.html")
        print("- XML覆盖率报告: coverage.xml")
        print("- JUnit测试结果: test-results.xml")
        print("- HTML测试报告: test-report.html")
        return True
    
    return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI Gen Hub 测试运行器")
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "all", "lint", "security", "report"],
        help="测试类型"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="详细输出"
    )
    parser.add_argument(
        "-c", "--coverage",
        action="store_true",
        help="生成覆盖率报告"
    )
    parser.add_argument(
        "--no-install",
        action="store_true",
        help="跳过依赖安装"
    )
    
    args = parser.parse_args()
    
    # 检查是否在项目根目录
    if not Path("pyproject.toml").exists():
        print("错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 安装测试依赖
    if not args.no_install:
        print("安装测试依赖...")
        if not run_command(
            ["pip", "install", "-e", ".[test]"],
            "安装测试依赖"
        ):
            print("依赖安装失败")
            sys.exit(1)
    
    # 运行指定的测试
    success = True
    
    if args.test_type == "unit":
        success = run_unit_tests(args.verbose, args.coverage)
    elif args.test_type == "integration":
        success = run_integration_tests(args.verbose)
    elif args.test_type == "all":
        success = run_all_tests(args.verbose, args.coverage)
    elif args.test_type == "lint":
        success = run_linting()
    elif args.test_type == "security":
        success = run_security_check()
    elif args.test_type == "report":
        success = generate_test_report()
    
    # 输出结果
    print("\n" + "="*60)
    if success:
        print("✅ 测试完成，所有检查通过")
        sys.exit(0)
    else:
        print("❌ 测试失败，请检查上述错误")
        sys.exit(1)


if __name__ == "__main__":
    main()
