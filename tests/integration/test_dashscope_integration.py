"""
DashScope Provider 集成测试

测试 DashScope Provider 与整个系统的集成，包括：
- Provider Manager 集成
- 配置系统集成
- 服务层集成
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from ai_gen_hub.config.settings import Settings, ProviderConfig
from ai_gen_hub.core.interfaces import ModelType, TextGenerationRequest, Message, MessageRole
from ai_gen_hub.services.provider_manager import AIProviderManager
from ai_gen_hub.utils.key_manager import KeyManager, APIKey


class TestDashScopeIntegration:
    """DashScope Provider 集成测试类"""

    @pytest.fixture
    def settings_with_dashscope(self):
        """创建包含 DashScope 配置的设置"""
        settings = Settings()
        settings.dashscope = ProviderConfig(
            api_keys=["sk-test-dashscope-key"],
            enabled=True,
            timeout=30,
            max_retries=3
        )
        return settings

    @pytest.fixture
    def key_manager(self):
        """创建测试用的密钥管理器"""
        key_manager = MagicMock(spec=KeyManager)
        api_key = APIKey(
            key="sk-test-dashscope-key",
            provider="dashscope",
            rate_limit=1000
        )
        key_manager.get_key = AsyncMock(return_value=api_key)
        key_manager.record_request = AsyncMock()
        return key_manager

    @pytest.mark.asyncio
    async def test_provider_manager_initialization(self, settings_with_dashscope, key_manager):
        """测试 Provider Manager 初始化 DashScope Provider"""
        provider_manager = AIProviderManager(settings_with_dashscope, key_manager)
        
        # 模拟 Provider 初始化
        with patch('ai_gen_hub.providers.dashscope_provider.DashScopeProvider.initialize') as mock_init:
            mock_init.return_value = None
            
            await provider_manager.initialize()
            
            # 验证 DashScope Provider 被添加
            assert "dashscope" in provider_manager.providers
            
            # 验证 Provider 类型
            dashscope_provider = provider_manager.providers["dashscope"]
            assert dashscope_provider.name == "dashscope"
            
            # 验证初始化被调用
            mock_init.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_available_providers_for_text_generation(self, settings_with_dashscope, key_manager):
        """测试获取文本生成的可用供应商"""
        provider_manager = AIProviderManager(settings_with_dashscope, key_manager)
        
        with patch('ai_gen_hub.providers.dashscope_provider.DashScopeProvider.initialize'):
            await provider_manager.initialize()
            
            # 获取文本生成的可用供应商
            available_providers = await provider_manager.get_available_providers(
                ModelType.TEXT_GENERATION
            )
            
            # 验证 DashScope Provider 在可用列表中
            provider_names = [p.name for p in available_providers]
            assert "dashscope" in provider_names

    @pytest.mark.asyncio
    async def test_provider_supports_qwen_models(self, settings_with_dashscope, key_manager):
        """测试 Provider 支持通义千问模型"""
        provider_manager = AIProviderManager(settings_with_dashscope, key_manager)
        
        with patch('ai_gen_hub.providers.dashscope_provider.DashScopeProvider.initialize'):
            await provider_manager.initialize()
            
            dashscope_provider = await provider_manager.get_provider("dashscope")
            
            # 测试支持的模型
            assert dashscope_provider.supports_model("qwen-max")
            assert dashscope_provider.supports_model("qwen-plus")
            assert dashscope_provider.supports_model("qwen-turbo")
            assert dashscope_provider.supports_model("qwen3-235b-a22b")
            assert dashscope_provider.supports_model("qwen2.5-72b-instruct")
            assert dashscope_provider.supports_model("qwen2.5-coder-32b-instruct")
            
            # 测试模型映射
            assert dashscope_provider.map_model_name("qwen-latest") == "qwen-max"
            assert dashscope_provider.map_model_name("qwen-fast") == "qwen-turbo"
            assert dashscope_provider.map_model_name("qwen-coder") == "qwen2.5-coder-32b-instruct"

    @pytest.mark.asyncio
    async def test_provider_model_type_support(self, settings_with_dashscope, key_manager):
        """测试 Provider 模型类型支持"""
        provider_manager = AIProviderManager(settings_with_dashscope, key_manager)
        
        with patch('ai_gen_hub.providers.dashscope_provider.DashScopeProvider.initialize'):
            await provider_manager.initialize()
            
            dashscope_provider = await provider_manager.get_provider("dashscope")
            
            # 验证支持的模型类型
            assert dashscope_provider.supports_model_type(ModelType.TEXT_GENERATION)
            assert not dashscope_provider.supports_model_type(ModelType.IMAGE_GENERATION)

    @pytest.mark.asyncio
    async def test_provider_cleanup(self, settings_with_dashscope, key_manager):
        """测试 Provider 清理"""
        provider_manager = AIProviderManager(settings_with_dashscope, key_manager)
        
        with patch('ai_gen_hub.providers.dashscope_provider.DashScopeProvider.initialize'):
            with patch('ai_gen_hub.providers.dashscope_provider.DashScopeProvider.cleanup') as mock_cleanup:
                await provider_manager.initialize()
                await provider_manager.cleanup()
                
                # 验证清理被调用
                mock_cleanup.assert_called_once()

    def test_configuration_integration(self):
        """测试配置系统集成"""
        settings = Settings()
        
        # 验证 DashScope 配置存在
        assert hasattr(settings, 'dashscope')
        assert isinstance(settings.dashscope, ProviderConfig)
        
        # 验证配置获取
        dashscope_config = settings.get_provider_config('dashscope')
        assert dashscope_config is not None
        assert dashscope_config == settings.dashscope

    def test_enabled_providers_list(self):
        """测试启用的供应商列表"""
        settings = Settings()
        
        # 默认情况下 DashScope 未启用（没有 API 密钥）
        enabled_providers = settings.get_enabled_providers()
        assert "dashscope" not in enabled_providers
        
        # 设置 API 密钥后应该被启用
        settings.dashscope.api_keys = ["sk-test-key"]
        settings.dashscope.enabled = True
        
        enabled_providers = settings.get_enabled_providers()
        assert "dashscope" in enabled_providers

    @pytest.mark.asyncio
    async def test_health_check_integration(self, settings_with_dashscope, key_manager):
        """测试健康检查集成"""
        provider_manager = AIProviderManager(settings_with_dashscope, key_manager)
        
        with patch('ai_gen_hub.providers.dashscope_provider.DashScopeProvider.initialize'):
            with patch('ai_gen_hub.providers.dashscope_provider.DashScopeProvider.health_check') as mock_health_check:
                mock_health_check.return_value = True
                
                await provider_manager.initialize()
                
                # 执行健康检查
                dashscope_provider = await provider_manager.get_provider("dashscope")
                health_result = await dashscope_provider.health_check()
                
                assert health_result is True
                mock_health_check.assert_called_once()

    def test_model_mapping_comprehensive(self, settings_with_dashscope, key_manager):
        """测试全面的模型映射"""
        from ai_gen_hub.providers.dashscope_provider import DashScopeProvider
        
        provider = DashScopeProvider(settings_with_dashscope.dashscope, key_manager)
        
        # 测试所有映射
        mappings = {
            # 旧版本映射
            "qwen-max-1201": "qwen-max",
            "qwen-plus-0919": "qwen-plus",
            "qwen-turbo-1201": "qwen-turbo",
            
            # 别名映射
            "qwen-latest": "qwen-max",
            "qwen-best": "qwen-max",
            "qwen-fast": "qwen-turbo",
            "qwen-balanced": "qwen-plus",
            
            # Qwen3 别名
            "qwen3-latest": "qwen3-235b-a22b",
            "qwen3-large": "qwen3-32b",
            "qwen3-medium": "qwen3-8b",
            
            # 代码模型别名
            "qwen-coder": "qwen2.5-coder-32b-instruct",
            "qwen-coder-large": "qwen2.5-coder-32b-instruct",
            "qwen-coder-medium": "qwen2.5-coder-14b-instruct",
            "qwen-coder-small": "qwen2.5-coder-7b-instruct",
        }
        
        for input_model, expected_output in mappings.items():
            actual_output = provider.map_model_name(input_model)
            assert actual_output == expected_output, f"映射失败: {input_model} -> {actual_output}, 期望: {expected_output}"

    def test_supported_models_comprehensive(self, settings_with_dashscope, key_manager):
        """测试全面的模型支持"""
        from ai_gen_hub.providers.dashscope_provider import DashScopeProvider
        
        provider = DashScopeProvider(settings_with_dashscope.dashscope, key_manager)
        
        # 验证所有支持的模型
        expected_models = [
            # 通义千问最新系列
            "qwen-max", "qwen-plus", "qwen-turbo",
            
            # Qwen3 系列
            "qwen3-235b-a22b", "qwen3-32b", "qwen3-8b",
            
            # Qwen2.5 系列
            "qwen2.5-72b-instruct", "qwen2.5-32b-instruct", "qwen2.5-14b-instruct",
            "qwen2.5-7b-instruct", "qwen2.5-3b-instruct", "qwen2.5-1.5b-instruct",
            "qwen2.5-0.5b-instruct",
            
            # Qwen2.5-Coder 系列
            "qwen2.5-coder-32b-instruct", "qwen2.5-coder-14b-instruct",
            "qwen2.5-coder-7b-instruct", "qwen2.5-coder-1.5b-instruct",
            
            # Qwen2 系列
            "qwen2-72b-instruct", "qwen2-57b-a14b-instruct", "qwen2-7b-instruct",
            "qwen2-1.5b-instruct", "qwen2-0.5b-instruct",
        ]
        
        for model in expected_models:
            assert provider.supports_model(model), f"模型 {model} 应该被支持"
        
        # 验证模型总数
        assert len(provider._supported_models) == len(expected_models)
