"""
文本生成服务集成测试
"""

import pytest
from unittest.mock import AsyncMock, patch

from ai_gen_hub.services import TextGenerationService
from ai_gen_hub.core.interfaces import (
    TextGenerationRequest,
    Message,
    MessageRole,
)


@pytest.mark.integration
class TestTextGenerationService:
    """文本生成服务集成测试"""
    
    @pytest.mark.asyncio
    async def test_generate_text_success(
        self,
        test_settings,
        provider_manager,
        router,
        memory_cache,
        sample_text_request
    ):
        """测试成功的文本生成"""
        service = TextGenerationService(
            test_settings,
            provider_manager,
            router,
            memory_cache
        )
        
        response = await service.generate_text(
            sample_text_request,
            user_id="test_user",
            request_id="test_request"
        )
        
        assert response is not None
        assert response.provider == "test_provider"
        assert len(response.choices) > 0
        assert response.choices[0].message.content == "这是一个测试响应"
        assert response.usage.total_tokens == 15
    
    @pytest.mark.asyncio
    async def test_generate_text_with_cache(
        self,
        test_settings,
        provider_manager,
        router,
        memory_cache,
        sample_text_request
    ):
        """测试带缓存的文本生成"""
        service = TextGenerationService(
            test_settings,
            provider_manager,
            router,
            memory_cache
        )
        
        # 第一次请求
        response1 = await service.generate_text(
            sample_text_request,
            user_id="test_user"
        )
        
        # 第二次相同请求应该从缓存返回
        response2 = await service.generate_text(
            sample_text_request,
            user_id="test_user"
        )
        
        assert response1.id == response2.id
        assert response1.choices[0].message.content == response2.choices[0].message.content
    
    @pytest.mark.asyncio
    async def test_generate_text_stream(
        self,
        test_settings,
        provider_manager,
        router,
        memory_cache
    ):
        """测试流式文本生成"""
        service = TextGenerationService(
            test_settings,
            provider_manager,
            router,
            memory_cache
        )
        
        # 创建流式请求
        stream_request = TextGenerationRequest(
            model="test-model",
            messages=[
                Message(
                    role=MessageRole.USER,
                    content="请生成一段文本"
                )
            ],
            stream=True
        )
        
        # 模拟流式响应
        async def mock_stream():
            from ai_gen_hub.core.interfaces import TextGenerationStreamChunk
            
            chunks = [
                TextGenerationStreamChunk(
                    id="test-chunk-1",
                    object="chat.completion.chunk",
                    created=**********,
                    model="test-model",
                    choices=[{
                        "index": 0,
                        "delta": {"content": "这是"},
                        "finish_reason": None
                    }],
                    provider="test_provider",
                    request_id="test-request"
                ),
                TextGenerationStreamChunk(
                    id="test-chunk-2",
                    object="chat.completion.chunk",
                    created=**********,
                    model="test-model",
                    choices=[{
                        "index": 0,
                        "delta": {"content": "流式响应"},
                        "finish_reason": "stop"
                    }],
                    provider="test_provider",
                    request_id="test-request"
                )
            ]
            
            for chunk in chunks:
                yield chunk
        
        # 模拟供应商返回流式响应
        mock_provider = await provider_manager.get_provider("test_provider")
        mock_provider.generate_text.return_value = mock_stream()
        
        # 执行流式生成
        response_stream = await service.generate_text(
            stream_request,
            user_id="test_user"
        )
        
        # 收集所有块
        chunks = []
        async for chunk in response_stream:
            chunks.append(chunk)
        
        assert len(chunks) == 2
        assert chunks[0].choices[0]["delta"]["content"] == "这是"
        assert chunks[1].choices[0]["delta"]["content"] == "流式响应"
        assert chunks[1].choices[0]["finish_reason"] == "stop"
    
    @pytest.mark.asyncio
    async def test_generate_text_provider_failure(
        self,
        test_settings,
        provider_manager,
        router,
        memory_cache,
        sample_text_request
    ):
        """测试供应商失败的处理"""
        service = TextGenerationService(
            test_settings,
            provider_manager,
            router,
            memory_cache
        )
        
        # 模拟供应商失败
        mock_provider = await provider_manager.get_provider("test_provider")
        mock_provider.generate_text.side_effect = Exception("供应商错误")
        
        # 应该抛出异常
        with pytest.raises(Exception) as exc_info:
            await service.generate_text(
                sample_text_request,
                user_id="test_user"
            )
        
        assert "供应商错误" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_supported_models(
        self,
        test_settings,
        provider_manager,
        router,
        memory_cache
    ):
        """测试获取支持的模型"""
        service = TextGenerationService(
            test_settings,
            provider_manager,
            router,
            memory_cache
        )
        
        # 模拟供应商信息
        from ai_gen_hub.core.interfaces import ProviderInfo, ModelType
        
        mock_provider = await provider_manager.get_provider("test_provider")
        mock_provider.get_provider_info.return_value = ProviderInfo(
            name="test_provider",
            version="1.0.0",
            status="healthy",
            models=["gpt-4", "gpt-3.5-turbo"],
            capabilities=[ModelType.TEXT_GENERATION],
            rate_limits={},
            last_health_check=**********
        )
        
        models = await service.get_supported_models()
        
        assert "test_provider" in models
        assert "gpt-4" in models["test_provider"]
        assert "gpt-3.5-turbo" in models["test_provider"]
    
    @pytest.mark.asyncio
    async def test_service_stats(
        self,
        test_settings,
        provider_manager,
        router,
        memory_cache
    ):
        """测试获取服务统计"""
        service = TextGenerationService(
            test_settings,
            provider_manager,
            router,
            memory_cache
        )
        
        stats = await service.get_service_stats()
        
        assert stats["service"] == "text_generation"
        assert "features" in stats
        assert "supported_models" in stats
        assert "provider_stats" in stats
        
        # 验证功能配置
        assert stats["features"]["streaming"] is True
        assert stats["features"]["caching"] is True


@pytest.mark.integration
class TestTextGenerationWithRetry:
    """文本生成重试机制集成测试"""
    
    @pytest.mark.asyncio
    async def test_retry_on_failure(
        self,
        test_settings,
        provider_manager,
        router,
        memory_cache,
        sample_text_request
    ):
        """测试失败时的重试机制"""
        service = TextGenerationService(
            test_settings,
            provider_manager,
            router,
            memory_cache
        )
        
        # 模拟前两次失败，第三次成功
        mock_provider = await provider_manager.get_provider("test_provider")
        
        call_count = 0
        async def mock_generate_text(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            if call_count <= 2:
                raise Exception("临时错误")
            else:
                # 返回成功响应
                from ai_gen_hub.core.interfaces import (
                    TextGenerationResponse,
                    TextGenerationChoice,
                    Message,
                    MessageRole,
                    Usage
                )
                
                return TextGenerationResponse(
                    id="retry-success",
                    object="chat.completion",
                    created=**********,
                    model="test-model",
                    choices=[
                        TextGenerationChoice(
                            index=0,
                            message=Message(
                                role=MessageRole.ASSISTANT,
                                content="重试成功的响应"
                            ),
                            finish_reason="stop"
                        )
                    ],
                    usage=Usage(
                        prompt_tokens=10,
                        completion_tokens=5,
                        total_tokens=15
                    ),
                    provider="test_provider",
                    request_id="retry-test",
                    processing_time=0.5
                )
        
        mock_provider.generate_text.side_effect = mock_generate_text
        
        # 执行请求
        response = await service.generate_text(
            sample_text_request,
            user_id="test_user"
        )
        
        # 验证重试成功
        assert response.id == "retry-success"
        assert response.choices[0].message.content == "重试成功的响应"
        assert call_count == 3  # 确认重试了3次
