"""
API接口集成测试
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch

from ai_gen_hub.api import create_app


@pytest.fixture
def test_client():
    """测试客户端fixture"""
    app = create_app()
    return TestClient(app)


@pytest.mark.integration
class TestHealthAPI:
    """健康检查API测试"""
    
    def test_root_endpoint(self, test_client):
        """测试根路径"""
        response = test_client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == "AI Gen Hub"
        assert "version" in data
        assert data["status"] == "running"
    
    def test_liveness_check(self, test_client):
        """测试存活检查"""
        response = test_client.get("/health/live")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "alive"
        assert "timestamp" in data
    
    def test_readiness_check(self, test_client):
        """测试就绪检查"""
        with patch("ai_gen_hub.monitoring.HealthManager.check_health") as mock_check:
            from ai_gen_hub.monitoring import HealthReport, HealthStatus
            
            # 模拟健康状态
            mock_check.return_value = HealthReport(
                overall_status=HealthStatus.HEALTHY,
                checks=[],
                duration=0.1
            )
            
            response = test_client.get("/health/ready")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["status"] == "ready"
            assert data["overall_status"] == "healthy"
    
    def test_health_check_detailed(self, test_client):
        """测试详细健康检查"""
        with patch("ai_gen_hub.monitoring.HealthManager.check_health") as mock_check:
            from ai_gen_hub.monitoring import (
                HealthReport,
                HealthStatus,
                HealthCheckResult
            )
            
            # 模拟详细健康状态
            mock_check.return_value = HealthReport(
                overall_status=HealthStatus.HEALTHY,
                checks=[
                    HealthCheckResult(
                        name="system",
                        status=HealthStatus.HEALTHY,
                        message="系统状态正常",
                        duration=0.05
                    ),
                    HealthCheckResult(
                        name="providers",
                        status=HealthStatus.HEALTHY,
                        message="供应商状态良好",
                        duration=0.1
                    )
                ],
                duration=0.15
            )
            
            response = test_client.get("/health/")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["overall_status"] == "healthy"
            assert len(data["checks"]) == 2
            assert data["summary"]["total"] == 2
            assert data["summary"]["healthy"] == 2


@pytest.mark.integration
class TestTextGenerationAPI:
    """文本生成API测试"""
    
    def test_generate_text_success(self, test_client):
        """测试成功的文本生成"""
        with patch("ai_gen_hub.services.TextGenerationService.generate_text") as mock_generate:
            from ai_gen_hub.core.interfaces import (
                TextGenerationResponse,
                TextGenerationChoice,
                Message,
                MessageRole,
                Usage
            )
            
            # 模拟成功响应
            mock_generate.return_value = TextGenerationResponse(
                id="test-response",
                object="chat.completion",
                created=**********,
                model="gpt-4",
                choices=[
                    TextGenerationChoice(
                        index=0,
                        message=Message(
                            role=MessageRole.ASSISTANT,
                            content="这是API测试响应"
                        ),
                        finish_reason="stop"
                    )
                ],
                usage=Usage(
                    prompt_tokens=10,
                    completion_tokens=8,
                    total_tokens=18
                ),
                provider="openai",
                request_id="api-test",
                processing_time=1.2
            )
            
            # 发送请求
            request_data = {
                "model": "gpt-4",
                "messages": [
                    {
                        "role": "user",
                        "content": "你好，请介绍一下自己"
                    }
                ],
                "max_tokens": 100,
                "temperature": 0.7
            }
            
            response = test_client.post("/api/v1/text/generate", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["id"] == "test-response"
            assert data["model"] == "gpt-4"
            assert len(data["choices"]) == 1
            assert data["choices"][0]["message"]["content"] == "这是API测试响应"
            assert data["usage"]["total_tokens"] == 18
    
    def test_generate_text_validation_error(self, test_client):
        """测试请求验证错误"""
        # 发送无效请求（缺少必需字段）
        request_data = {
            "model": "gpt-4",
            # 缺少messages字段
            "max_tokens": 100
        }
        
        response = test_client.post("/api/v1/text/generate", json=request_data)
        
        assert response.status_code == 422  # Validation Error
    
    def test_get_supported_models(self, test_client):
        """测试获取支持的模型"""
        with patch("ai_gen_hub.services.TextGenerationService.get_supported_models") as mock_models:
            mock_models.return_value = {
                "openai": ["gpt-4", "gpt-3.5-turbo"],
                "anthropic": ["claude-3-sonnet", "claude-3-haiku"]
            }
            
            response = test_client.get("/api/v1/text/models")
            
            assert response.status_code == 200
            data = response.json()
            
            assert "openai" in data
            assert "anthropic" in data
            assert "gpt-4" in data["openai"]
            assert "claude-3-sonnet" in data["anthropic"]
    
    def test_openai_compatible_endpoint(self, test_client):
        """测试OpenAI兼容端点"""
        with patch("ai_gen_hub.services.TextGenerationService.generate_text") as mock_generate:
            from ai_gen_hub.core.interfaces import (
                TextGenerationResponse,
                TextGenerationChoice,
                Message,
                MessageRole,
                Usage
            )
            
            mock_generate.return_value = TextGenerationResponse(
                id="openai-compat-test",
                object="chat.completion",
                created=**********,
                model="gpt-4",
                choices=[
                    TextGenerationChoice(
                        index=0,
                        message=Message(
                            role=MessageRole.ASSISTANT,
                            content="OpenAI兼容响应"
                        ),
                        finish_reason="stop"
                    )
                ],
                usage=Usage(
                    prompt_tokens=15,
                    completion_tokens=10,
                    total_tokens=25
                ),
                provider="openai",
                request_id="openai-compat",
                processing_time=0.8
            )
            
            # 使用OpenAI兼容格式
            request_data = {
                "model": "gpt-4",
                "messages": [
                    {"role": "user", "content": "Hello"}
                ]
            }
            
            response = test_client.post("/api/v1/text/chat/completions", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["id"] == "openai-compat-test"
            assert data["choices"][0]["message"]["content"] == "OpenAI兼容响应"


@pytest.mark.integration
class TestImageGenerationAPI:
    """图像生成API测试"""
    
    def test_generate_image_success(self, test_client):
        """测试成功的图像生成"""
        with patch("ai_gen_hub.services.ImageGenerationService.generate_image") as mock_generate:
            from ai_gen_hub.core.interfaces import (
                ImageGenerationResponse,
                ImageData
            )
            
            # 模拟成功响应
            mock_generate.return_value = ImageGenerationResponse(
                created=**********,
                data=[
                    ImageData(
                        url="https://example.com/generated-image.png",
                        b64_json=None,
                        revised_prompt="一只可爱的小猫坐在花园里"
                    )
                ],
                provider="openai",
                request_id="image-test",
                processing_time=5.2
            )
            
            # 发送请求
            request_data = {
                "prompt": "一只可爱的小猫",
                "model": "dall-e-3",
                "n": 1,
                "size": "1024x1024",
                "quality": "standard",
                "response_format": "url"
            }
            
            response = test_client.post("/api/v1/image/generate", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["created"] == **********
            assert len(data["data"]) == 1
            assert data["data"][0]["url"] == "https://example.com/generated-image.png"
            assert data["provider"] == "openai"
    
    def test_generate_image_validation_error(self, test_client):
        """测试图像生成验证错误"""
        # 发送无效请求（空提示词）
        request_data = {
            "prompt": "",  # 空提示词
            "model": "dall-e-3",
            "n": 1
        }
        
        response = test_client.post("/api/v1/image/generate", json=request_data)
        
        assert response.status_code == 400
    
    def test_batch_image_generation(self, test_client):
        """测试批量图像生成"""
        with patch("ai_gen_hub.services.ImageGenerationService.generate_images_batch") as mock_batch:
            from ai_gen_hub.core.interfaces import (
                ImageGenerationResponse,
                ImageData
            )
            
            # 模拟批量响应
            mock_batch.return_value = [
                ImageGenerationResponse(
                    created=**********,
                    data=[ImageData(url="https://example.com/image1.png")],
                    provider="openai",
                    request_id="batch-1",
                    processing_time=3.0
                ),
                ImageGenerationResponse(
                    created=**********,
                    data=[ImageData(url="https://example.com/image2.png")],
                    provider="openai",
                    request_id="batch-2",
                    processing_time=3.2
                )
            ]
            
            # 发送批量请求
            request_data = [
                {
                    "prompt": "一只猫",
                    "model": "dall-e-3",
                    "n": 1
                },
                {
                    "prompt": "一只狗",
                    "model": "dall-e-3",
                    "n": 1
                }
            ]
            
            response = test_client.post("/api/v1/image/generate/batch", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            
            assert len(data) == 2
            assert data[0]["data"][0]["url"] == "https://example.com/image1.png"
            assert data[1]["data"][0]["url"] == "https://example.com/image2.png"


@pytest.mark.integration
class TestMetricsAPI:
    """监控指标API测试"""
    
    def test_prometheus_metrics(self, test_client):
        """测试Prometheus指标端点"""
        response = test_client.get("/metrics/prometheus")
        
        assert response.status_code == 200
        assert response.headers["content-type"].startswith("text/plain")
        
        # 检查是否包含一些基本指标
        content = response.text
        assert "ai_gen_hub" in content
    
    def test_system_stats(self, test_client):
        """测试系统统计信息"""
        response = test_client.get("/metrics/stats")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "timestamp" in data
        # 其他字段可能因为模拟而不存在，但不应该报错


@pytest.mark.integration
class TestAuthentication:
    """认证测试"""
    
    def test_api_key_authentication(self, test_client):
        """测试API密钥认证"""
        # 模拟需要认证的环境
        with patch.dict("os.environ", {"API_KEY": "test-api-key"}):
            # 不带API密钥的请求应该被拒绝
            response = test_client.post("/api/v1/text/generate", json={
                "model": "gpt-4",
                "messages": [{"role": "user", "content": "test"}]
            })
            
            # 注意：由于测试环境可能没有启用认证，这里可能返回其他状态码
            # 实际的认证测试需要在完整的应用环境中进行
    
    def test_rate_limiting(self, test_client):
        """测试速率限制"""
        # 模拟启用速率限制的环境
        with patch.dict("os.environ", {"ENABLE_RATE_LIMITING": "true"}):
            # 发送多个快速请求
            responses = []
            for i in range(5):
                response = test_client.get("/health/live")
                responses.append(response)
            
            # 检查是否有请求被限制
            # 注意：实际的速率限制测试需要配置具体的限制参数
