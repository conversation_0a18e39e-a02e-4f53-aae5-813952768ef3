"""
缓存系统单元测试
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock

from ai_gen_hub.cache import (
    MemoryCache,
    RedisCache,
    MultiLevelCache,
    CacheKeyGenerator,
    CacheSerializer,
)
from ai_gen_hub.config import RedisConfig, CacheConfig


@pytest.mark.unit
class TestCacheKeyGenerator:
    """缓存键生成器测试"""
    
    def test_generate_key(self):
        """测试生成缓存键"""
        generator = CacheKeyGenerator("test_app")
        
        key = generator.generate_key(
            "text_generation",
            "gpt-4",
            {"temperature": 0.7, "max_tokens": 100},
            "user123"
        )
        
        assert key.startswith("test_app:text_generation:gpt-4:user:user123:")
        assert len(key.split(":")) == 6  # prefix:type:model:user:user_id:hash
    
    def test_generate_key_without_user(self):
        """测试不带用户ID的缓存键生成"""
        generator = CacheKeyGenerator("test_app")
        
        key = generator.generate_key(
            "text_generation",
            "gpt-4",
            {"temperature": 0.7, "max_tokens": 100}
        )
        
        assert key.startswith("test_app:text_generation:gpt-4:")
        assert "user:" not in key
    
    def test_parameter_normalization(self):
        """测试参数标准化"""
        generator = CacheKeyGenerator("test")
        
        # 相同参数应该生成相同的键
        key1 = generator.generate_key("test", "model", {"temp": 0.7, "tokens": 100})
        key2 = generator.generate_key("test", "model", {"tokens": 100, "temp": 0.7})
        
        assert key1 == key2
    
    def test_parameter_filtering(self):
        """测试参数过滤"""
        generator = CacheKeyGenerator("test")
        
        # 包含不相关参数的请求应该生成相同的键
        params1 = {"temperature": 0.7, "max_tokens": 100}
        params2 = {"temperature": 0.7, "max_tokens": 100, "stream": True, "user": "test"}
        
        key1 = generator.generate_key("test", "model", params1)
        key2 = generator.generate_key("test", "model", params2)
        
        assert key1 == key2


@pytest.mark.unit
class TestCacheSerializer:
    """缓存序列化器测试"""
    
    def test_serialize_deserialize(self):
        """测试序列化和反序列化"""
        data = {"message": "Hello", "number": 42, "list": [1, 2, 3]}
        
        serialized = CacheSerializer.serialize(data)
        assert isinstance(serialized, bytes)
        
        deserialized = CacheSerializer.deserialize(serialized)
        assert deserialized == data
    
    def test_serialize_complex_object(self):
        """测试复杂对象序列化"""
        from ai_gen_hub.core.interfaces import Message, MessageRole
        
        message = Message(role=MessageRole.USER, content="Test message")
        data = {"message": message.dict(), "timestamp": 1234567890}
        
        serialized = CacheSerializer.serialize(data)
        deserialized = CacheSerializer.deserialize(serialized)
        
        assert deserialized["message"]["role"] == "user"
        assert deserialized["message"]["content"] == "Test message"
        assert deserialized["timestamp"] == 1234567890
    
    def test_get_size(self):
        """测试获取大小"""
        data = {"test": "data"}
        size = CacheSerializer.get_size(data)
        
        assert isinstance(size, int)
        assert size > 0


@pytest.mark.unit
class TestMemoryCache:
    """内存缓存测试"""
    
    @pytest.mark.asyncio
    async def test_basic_operations(self):
        """测试基本操作"""
        cache = MemoryCache(max_size=10, default_ttl=60)
        
        # 测试设置和获取
        await cache.set("key1", "value1")
        value = await cache.get("key1")
        assert value == "value1"
        
        # 测试不存在的键
        value = await cache.get("nonexistent")
        assert value is None
        
        # 测试删除
        await cache.delete("key1")
        value = await cache.get("key1")
        assert value is None
        
        await cache.cleanup()
    
    @pytest.mark.asyncio
    async def test_ttl_expiration(self):
        """测试TTL过期"""
        cache = MemoryCache(max_size=10, default_ttl=1)
        
        await cache.set("key1", "value1", ttl=1)
        
        # 立即获取应该成功
        value = await cache.get("key1")
        assert value == "value1"
        
        # 等待过期
        await asyncio.sleep(1.1)
        
        # 过期后应该返回None
        value = await cache.get("key1")
        assert value is None
        
        await cache.cleanup()
    
    @pytest.mark.asyncio
    async def test_lru_eviction(self):
        """测试LRU淘汰"""
        cache = MemoryCache(max_size=3, default_ttl=60)
        
        # 填满缓存
        await cache.set("key1", "value1")
        await cache.set("key2", "value2")
        await cache.set("key3", "value3")
        
        # 访问key1使其成为最近使用
        await cache.get("key1")
        
        # 添加新键，应该淘汰key2
        await cache.set("key4", "value4")
        
        assert await cache.get("key1") == "value1"  # 最近使用，保留
        assert await cache.get("key2") is None      # 最旧，被淘汰
        assert await cache.get("key3") == "value3"  # 保留
        assert await cache.get("key4") == "value4"  # 新添加
        
        await cache.cleanup()
    
    @pytest.mark.asyncio
    async def test_cache_stats(self):
        """测试缓存统计"""
        cache = MemoryCache(max_size=10, default_ttl=60)
        
        # 执行一些操作
        await cache.set("key1", "value1")
        await cache.get("key1")  # 命中
        await cache.get("key2")  # 未命中
        
        stats = await cache.get_stats()
        
        assert stats.hits >= 1
        assert stats.misses >= 1
        assert stats.sets >= 1
        
        await cache.cleanup()


@pytest.mark.unit
class TestRedisCache:
    """Redis缓存测试"""
    
    @pytest.mark.asyncio
    async def test_basic_operations(self, mock_redis):
        """测试基本操作"""
        redis_config = RedisConfig(url="redis://localhost:6379/0")
        cache = RedisCache(redis_config)
        
        # 模拟Redis客户端
        cache._redis = mock_redis
        
        # 测试设置
        await cache.set("key1", "value1")
        mock_redis.setex.assert_called()
        
        # 测试获取
        mock_redis.get.return_value = CacheSerializer.serialize("value1")
        value = await cache.get("key1")
        assert value == "value1"
        
        # 测试删除
        await cache.delete("key1")
        mock_redis.delete.assert_called()
        
        await cache.cleanup()
    
    @pytest.mark.asyncio
    async def test_compression(self, mock_redis):
        """测试压缩功能"""
        redis_config = RedisConfig(url="redis://localhost:6379/0")
        cache = RedisCache(
            redis_config,
            compression_enabled=True,
            compression_threshold=10
        )
        cache._redis = mock_redis
        
        # 大数据应该被压缩
        large_data = "x" * 100
        await cache.set("large_key", large_data)
        
        # 验证调用了setex
        mock_redis.setex.assert_called()
        
        await cache.cleanup()
    
    @pytest.mark.asyncio
    async def test_batch_operations(self, mock_redis):
        """测试批量操作"""
        redis_config = RedisConfig(url="redis://localhost:6379/0")
        cache = RedisCache(redis_config)
        cache._redis = mock_redis
        
        # 测试批量获取
        mock_redis.mget.return_value = [
            CacheSerializer.serialize("value1"),
            CacheSerializer.serialize("value2"),
            None
        ]
        
        values = await cache.mget(["key1", "key2", "key3"])
        assert values == ["value1", "value2", None]
        
        # 测试批量设置
        await cache.mset({"key1": "value1", "key2": "value2"})
        mock_redis.mset.assert_called()
        
        await cache.cleanup()


@pytest.mark.unit
class TestMultiLevelCache:
    """多级缓存测试"""
    
    @pytest.mark.asyncio
    async def test_l1_hit(self):
        """测试L1缓存命中"""
        cache_config = CacheConfig(
            enable_memory_cache=True,
            enable_redis_cache=False,
            memory_cache_size=10
        )
        redis_config = RedisConfig(url="redis://localhost:6379/0")
        
        cache = MultiLevelCache(cache_config, redis_config)
        
        # 设置数据
        await cache.set("key1", "value1")
        
        # 获取数据（应该从L1缓存命中）
        value = await cache.get("key1")
        assert value == "value1"
        
        await cache.cleanup()
    
    @pytest.mark.asyncio
    async def test_l1_miss_l2_hit(self, mock_redis):
        """测试L1未命中，L2命中"""
        cache_config = CacheConfig(
            enable_memory_cache=True,
            enable_redis_cache=True,
            memory_cache_size=10
        )
        redis_config = RedisConfig(url="redis://localhost:6379/0")
        
        cache = MultiLevelCache(cache_config, redis_config)
        
        # 模拟L2缓存
        if cache.l2_cache:
            cache.l2_cache._redis = mock_redis
            mock_redis.get.return_value = CacheSerializer.serialize("value1")
        
        # 获取数据（L1未命中，L2命中）
        value = await cache.get("key1")
        assert value == "value1"
        
        # 验证数据被回填到L1
        if cache.l1_cache:
            l1_value = await cache.l1_cache.get("key1")
            assert l1_value == "value1"
        
        await cache.cleanup()
    
    @pytest.mark.asyncio
    async def test_cache_invalidation(self):
        """测试缓存失效"""
        cache_config = CacheConfig(
            enable_memory_cache=True,
            enable_redis_cache=False
        )
        redis_config = RedisConfig(url="redis://localhost:6379/0")
        
        cache = MultiLevelCache(cache_config, redis_config)
        
        # 设置数据
        await cache.set("key1", "value1")
        
        # 验证数据存在
        value = await cache.get("key1")
        assert value == "value1"
        
        # 删除数据
        await cache.delete("key1")
        
        # 验证数据被删除
        value = await cache.get("key1")
        assert value is None
        
        await cache.cleanup()
    
    @pytest.mark.asyncio
    async def test_cache_stats(self):
        """测试缓存统计"""
        cache_config = CacheConfig(
            enable_memory_cache=True,
            enable_redis_cache=False
        )
        redis_config = RedisConfig(url="redis://localhost:6379/0")
        
        cache = MultiLevelCache(cache_config, redis_config)
        
        # 执行一些操作
        await cache.set("key1", "value1")
        await cache.get("key1")  # 命中
        await cache.get("key2")  # 未命中
        
        stats = await cache.get_stats()
        assert stats.hits >= 1
        assert stats.misses >= 1
        assert stats.sets >= 1
        
        await cache.cleanup()
