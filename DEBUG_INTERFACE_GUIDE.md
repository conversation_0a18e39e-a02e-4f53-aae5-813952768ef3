
# AI Gen Hub 调试界面使用指南

## 修复后的访问方式

### 1. 主要访问地址
- 调试主页: http://localhost:8001/debug/
- 系统信息: http://localhost:8001/debug/api/system/info
- 配置信息: http://localhost:8001/debug/api/config
- API测试: http://localhost:8001/debug/api/test-endpoint-fixed

### 2. 启动方式
```bash
# 方式1：使用统一启动脚本（推荐）
python start_with_debug.py --ai-port 8001

# 方式2：直接启动主服务
python run_server.py

# 方式3：使用uvicorn
uvicorn ai_gen_hub.api.app:create_app --factory --host 0.0.0.0 --port 8001 --reload
```

### 3. 问题解决
- 如果遇到404错误，确保访问的是端口8001而不是8000
- 如果test-endpoint挂起，使用修复后的端点：/debug/api/test-endpoint-fixed
- 如果认证错误，确保环境变量 DEBUG=true 和 ENVIRONMENT=development

### 4. 端口说明
- 端口8001：AI Gen Hub主服务 + 完整调试界面（推荐使用）
- 端口8000：独立调试页面（已停用，避免混淆）
- 端口8002：备用端口（未使用）

### 5. 修复内容
- ✅ 统一端口配置，避免8000/8001端口混淆
- ✅ 修复404错误，所有调试路由在8001端口可用
- ✅ 修复test-endpoint挂起，使用同步HTTP客户端
- ✅ 优化system/info接口性能
- ✅ 完善错误处理和超时机制
