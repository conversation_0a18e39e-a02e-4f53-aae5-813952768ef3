#!/usr/bin/env python3
"""
Redis兼容性修复脚本

解决aioredis在Python 3.11中的TimeoutError冲突问题
"""

import sys
import subprocess
import importlib.util
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 11):
        print("⚠️  检测到Python 3.11+，可能存在aioredis兼容性问题")
        return True
    else:
        print("✅ Python版本兼容")
        return False

def check_aioredis_issue():
    """检查aioredis是否存在TimeoutError问题"""
    try:
        import aioredis
        print("📦 aioredis已安装")
        
        # 尝试导入可能有问题的模块
        try:
            from aioredis.exceptions import TimeoutError
            print("✅ aioredis.exceptions.TimeoutError导入成功")
            return False
        except TypeError as e:
            if "duplicate base class TimeoutError" in str(e):
                print("❌ 检测到aioredis TimeoutError冲突问题")
                return True
            else:
                print(f"❌ aioredis导入错误: {e}")
                return True
        except Exception as e:
            print(f"❌ aioredis导入错误: {e}")
            return True
            
    except ImportError:
        print("📦 aioredis未安装")
        return False

def check_redis_py():
    """检查redis-py是否可用"""
    try:
        import redis
        print("📦 redis-py已安装")
        
        # 检查是否支持异步
        if hasattr(redis, 'asyncio'):
            print("✅ redis-py支持异步操作")
            return True
        else:
            print("⚠️  redis-py版本较旧，不支持异步操作")
            return False
            
    except ImportError:
        print("📦 redis-py未安装")
        return False

def install_compatible_redis():
    """安装兼容的Redis库"""
    print("🔧 安装兼容的Redis库...")
    
    try:
        # 卸载有问题的aioredis
        print("📤 卸载aioredis...")
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "aioredis", "-y"], 
                      check=False, capture_output=True)
        
        # 安装或升级redis-py
        print("📥 安装/升级redis-py...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "redis>=5.0.1"
        ], check=True, capture_output=True, text=True)
        
        print("✅ redis-py安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def test_redis_connection():
    """测试Redis连接"""
    print("🧪 测试Redis连接...")
    
    try:
        import redis.asyncio as redis
        import asyncio
        
        async def test_connection():
            try:
                # 尝试连接到本地Redis
                client = redis.Redis.from_url("redis://localhost:6379/0")
                await client.ping()
                await client.aclose()
                print("✅ Redis连接测试成功")
                return True
            except Exception as e:
                print(f"⚠️  Redis连接测试失败: {e}")
                print("💡 这是正常的，如果没有运行Redis服务")
                return False
        
        return asyncio.run(test_connection())
        
    except Exception as e:
        print(f"❌ Redis测试失败: {e}")
        return False

def update_cache_imports():
    """更新缓存模块导入"""
    print("🔧 更新缓存模块导入...")
    
    cache_init_file = Path("src/ai_gen_hub/cache/__init__.py")
    
    if cache_init_file.exists():
        try:
            content = cache_init_file.read_text()
            
            # 检查是否需要更新
            if "redis_cache_compat" not in content:
                # 添加兼容导入
                updated_content = content.replace(
                    "from ai_gen_hub.cache.redis_cache import RedisCache",
                    """# 使用兼容的Redis缓存实现
try:
    from ai_gen_hub.cache.redis_cache_compat import RedisCache
except ImportError:
    try:
        from ai_gen_hub.cache.redis_cache import RedisCache
    except ImportError:
        RedisCache = None"""
                )
                
                if updated_content != content:
                    cache_init_file.write_text(updated_content)
                    print("✅ 缓存模块导入已更新")
                else:
                    print("ℹ️  缓存模块导入无需更新")
            else:
                print("✅ 缓存模块已使用兼容导入")
                
        except Exception as e:
            print(f"❌ 更新缓存模块失败: {e}")
            return False
    else:
        print("⚠️  缓存模块文件不存在")
        return False
    
    return True

def create_test_script():
    """创建Redis兼容性测试脚本"""
    test_script = Path("test_redis_compat.py")
    
    test_code = '''#!/usr/bin/env python3
"""Redis兼容性测试脚本"""

import asyncio
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_redis_compat():
    """测试Redis兼容性"""
    print("🧪 测试Redis兼容性...")
    
    try:
        from ai_gen_hub.cache.redis_cache_compat import CompatRedisCache
        from ai_gen_hub.config.settings import RedisConfig
        
        # 创建Redis配置
        redis_config = RedisConfig(url="redis://localhost:6379/0")
        
        # 创建缓存实例
        cache = CompatRedisCache(redis_config)
        
        print("✅ Redis缓存实例创建成功")
        
        # 测试基本操作（不需要实际Redis连接）
        try:
            await cache.set("test_key", "test_value")
            value = await cache.get("test_key")
            print(f"✅ 缓存操作测试: {value}")
        except Exception as e:
            print(f"⚠️  缓存操作测试失败（可能是Redis未运行）: {e}")
        
        await cache.cleanup()
        print("✅ Redis兼容性测试完成")
        
    except Exception as e:
        print(f"❌ Redis兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_redis_compat())
'''
    
    test_script.write_text(test_code)
    test_script.chmod(0o755)
    print(f"✅ 测试脚本已创建: {test_script}")

def main():
    """主函数"""
    print("🔧 Redis兼容性修复工具")
    print("=" * 50)
    
    # 检查Python版本
    has_version_issue = check_python_version()
    
    # 检查aioredis问题
    has_aioredis_issue = check_aioredis_issue()
    
    # 检查redis-py
    has_redis_py = check_redis_py()
    
    print("\n📋 问题诊断:")
    print(f"   Python 3.11+: {'是' if has_version_issue else '否'}")
    print(f"   aioredis问题: {'是' if has_aioredis_issue else '否'}")
    print(f"   redis-py可用: {'是' if has_redis_py else '否'}")
    
    # 决定修复策略
    if has_aioredis_issue or (has_version_issue and not has_redis_py):
        print("\n🔧 开始修复...")
        
        # 安装兼容的Redis库
        if install_compatible_redis():
            print("✅ Redis库修复成功")
        else:
            print("❌ Redis库修复失败")
            return 1
        
        # 更新缓存模块导入
        if update_cache_imports():
            print("✅ 缓存模块更新成功")
        else:
            print("❌ 缓存模块更新失败")
            return 1
        
        # 创建测试脚本
        create_test_script()
        
        # 测试Redis连接
        test_redis_connection()
        
        print("\n🎉 修复完成！")
        print("💡 建议:")
        print("   1. 运行 python test_redis_compat.py 测试兼容性")
        print("   2. 如果需要Redis功能，请启动Redis服务")
        print("   3. 应用会在Redis不可用时自动降级到内存缓存")
        
    else:
        print("\n✅ 未检测到兼容性问题")
        print("💡 如果仍有问题，请手动运行修复步骤")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
