#!/usr/bin/env python3
"""
AI Gen Hub 统一测试脚本

整合所有分散的测试脚本，使用增强的配置管理器和通用测试工具。
提供完整的系统测试，包括配置、功能、性能等各个方面。
"""

import sys
import argparse
from pathlib import Path

# 使用通用测试工具设置环境
sys.path.insert(0, str(Path(__file__).parent / "src"))
from ai_gen_hub.utils.test_utils import (
    setup_test_environment, TestRunner, AppTestUtils, ConfigTestUtils
)


def test_enhanced_config_system():
    """测试增强的配置系统
    
    Returns:
        测试结果字典
    """
    try:
        from ai_gen_hub.config import get_config_manager, get_enhanced_settings
        
        # 测试配置管理器
        config_manager = get_config_manager(debug_logging=True)
        settings = config_manager.get_config()
        
        # 测试配置验证
        validation_result = settings.validate_all_configs()
        
        # 测试配置来源摘要
        sources_summary = config_manager.get_config_sources_summary()
        
        # 测试安全摘要
        security_summary = settings.get_config_security_summary()
        
        return {
            'success': True,
            'environment': settings.environment,
            'debug_mode': settings.debug,
            'enabled_providers': settings.get_enabled_providers(),
            'validation_valid': validation_result['valid'],
            'warnings_count': len(validation_result['warnings']),
            'errors_count': len(validation_result['errors']),
            'env_configs_count': sources_summary['from_environment'],
            'file_configs_count': sources_summary['from_file_or_default']
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def test_provider_configurations():
    """测试AI供应商配置
    
    Returns:
        测试结果字典
    """
    try:
        from ai_gen_hub.config import get_enhanced_settings
        
        settings = get_enhanced_settings()
        
        provider_results = {}
        total_providers = 0
        enabled_providers = 0
        
        for provider_name in ['openai', 'google_ai', 'anthropic', 'dashscope', 'azure']:
            config = settings.get_provider_config(provider_name)
            if config:
                total_providers += 1
                if config.enabled:
                    enabled_providers += 1
                
                provider_results[provider_name] = {
                    'enabled': config.enabled,
                    'api_keys_count': len(config.api_keys) if config.api_keys else 0,
                    'has_base_url': bool(config.base_url),
                    'timeout': config.timeout,
                    'max_retries': config.max_retries
                }
        
        return {
            'success': True,
            'total_providers': total_providers,
            'enabled_providers': enabled_providers,
            'provider_details': provider_results,
            'has_enabled_providers': enabled_providers > 0
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def test_application_creation():
    """测试应用创建和初始化
    
    Returns:
        测试结果字典
    """
    try:
        # 测试同步应用创建
        app = AppTestUtils.create_test_app()
        
        # 检查路由注册
        total_routes = len(app.routes)
        debug_routes = [route for route in app.routes if hasattr(route, 'path') and '/debug' in route.path]
        
        return {
            'success': True,
            'total_routes': total_routes,
            'debug_routes_count': len(debug_routes),
            'has_debug_routes': len(debug_routes) > 0,
            'app_title': app.title,
            'app_version': app.version
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


async def test_async_application():
    """测试异步应用创建和初始化
    
    Returns:
        测试结果字典
    """
    try:
        app, app_instance = await AppTestUtils.create_test_app_async()
        
        # 检查应用状态
        has_settings = hasattr(app.state, 'settings') and app.state.settings is not None
        has_key_manager = hasattr(app.state, 'key_manager') and app.state.key_manager is not None
        has_provider_manager = hasattr(app.state, 'provider_manager') and app.state.provider_manager is not None
        
        # 清理资源
        await app_instance.cleanup()
        
        return {
            'success': True,
            'has_settings': has_settings,
            'has_key_manager': has_key_manager,
            'has_provider_manager': has_provider_manager,
            'all_components_initialized': has_settings and has_key_manager and has_provider_manager
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def test_critical_endpoints():
    """测试关键端点
    
    Returns:
        测试结果字典
    """
    try:
        app = AppTestUtils.create_test_app()
        
        # 测试关键端点
        endpoints_to_test = [
            ('/', 'GET'),
            ('/health', 'GET'),
            ('/diagnostic', 'GET'),
            ('/debug/', 'GET'),
            ('/debug/api/system/info', 'GET')
        ]
        
        results = {}
        successful_endpoints = 0
        
        for endpoint, method in endpoints_to_test:
            result = AppTestUtils.test_endpoint(app, endpoint, method)
            results[endpoint] = {
                'success': result['success'],
                'status_code': result['status_code'],
                'duration': result['duration']
            }
            
            if result['success']:
                successful_endpoints += 1
        
        return {
            'success': successful_endpoints == len(endpoints_to_test),
            'total_endpoints': len(endpoints_to_test),
            'successful_endpoints': successful_endpoints,
            'endpoint_results': results,
            'all_endpoints_working': successful_endpoints == len(endpoints_to_test)
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def run_comprehensive_tests():
    """运行综合测试"""
    # 设置测试环境
    setup_test_environment(debug=True, load_env=True)
    
    # 创建测试运行器
    runner = TestRunner("AI Gen Hub 综合测试")
    runner.start()
    
    # 1. 配置系统测试
    runner.run_test("增强配置系统测试", test_enhanced_config_system)
    runner.run_test("AI供应商配置测试", test_provider_configurations)
    
    # 2. 应用创建测试
    runner.run_test("同步应用创建测试", test_application_creation)
    runner.run_test("异步应用创建测试", test_async_application)
    
    # 3. 端点功能测试
    runner.run_test("关键端点测试", test_critical_endpoints)
    
    # 4. 基础功能测试
    runner.run_test("基础配置加载测试", ConfigTestUtils.test_config_loading, True)
    runner.run_test("供应商配置验证测试", ConfigTestUtils.test_provider_configs)
    
    # 完成测试
    all_passed = runner.finish()
    
    if all_passed:
        print("\n🎉 综合测试总结:")
        print("1. ✅ 增强配置系统正常工作")
        print("2. ✅ AI供应商配置管理优化")
        print("3. ✅ 应用创建和初始化正常")
        print("4. ✅ 关键端点功能正常")
        print("5. ✅ 测试工具统一整合完成")
        print("\n💡 系统已准备就绪，可以正常使用！")
    else:
        print("\n⚠️ 部分测试失败，请检查上述错误信息")
    
    return all_passed


def run_quick_tests():
    """运行快速测试"""
    # 设置测试环境
    setup_test_environment(debug=False, load_env=True)
    
    # 创建测试运行器
    runner = TestRunner("快速功能测试")
    runner.start()
    
    # 运行关键测试
    runner.run_test("配置加载测试", ConfigTestUtils.test_config_loading, False)
    runner.run_test("应用创建测试", test_application_creation)

    # 测试诊断端点而不是健康检查端点（避免API调用）
    def test_diagnostic_endpoint():
        try:
            app = AppTestUtils.create_test_app()
            result = AppTestUtils.test_endpoint(app, "/diagnostic", "GET")
            return result
        except Exception as e:
            return {'success': False, 'error': str(e)}

    runner.run_test("诊断端点测试", test_diagnostic_endpoint)
    
    return runner.finish()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI Gen Hub 统一测试脚本")
    parser.add_argument(
        "test_type",
        choices=["comprehensive", "quick", "config", "endpoints"],
        nargs="?",
        default="comprehensive",
        help="测试类型 (默认: comprehensive)"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )
    
    args = parser.parse_args()
    
    if args.test_type == "comprehensive":
        success = run_comprehensive_tests()
    elif args.test_type == "quick":
        success = run_quick_tests()
    elif args.test_type == "config":
        setup_test_environment(debug=args.debug, load_env=True)
        runner = TestRunner("配置系统测试")
        runner.start()
        runner.run_test("增强配置系统", test_enhanced_config_system)
        runner.run_test("供应商配置", test_provider_configurations)
        success = runner.finish()
    elif args.test_type == "endpoints":
        setup_test_environment(debug=args.debug, load_env=True)
        runner = TestRunner("端点功能测试")
        runner.start()
        runner.run_test("应用创建", test_application_creation)
        runner.run_test("关键端点", test_critical_endpoints)
        success = runner.finish()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
