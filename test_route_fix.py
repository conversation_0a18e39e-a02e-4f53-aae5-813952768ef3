#!/usr/bin/env python3
"""
测试路由修复效果

验证调试路由是否能够正确注册和访问
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def load_env_file():
    """手动加载 .env 文件"""
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        print(f"✅ 已加载 .env 文件: {env_file}")
    else:
        print(f"❌ .env 文件不存在: {env_file}")

# 在导入其他模块之前加载环境变量
load_env_file()

def test_config_loading():
    """测试配置加载"""
    print("🔍 测试配置加载...")
    
    try:
        from ai_gen_hub.config import get_settings, reload_settings
        
        # 重新加载配置以启用调试日志
        print("重新加载配置（启用调试日志）:")
        settings = reload_settings(debug_logging=True)
        
        print(f"✅ 配置加载成功")
        print(f"   环境: {settings.environment}")
        print(f"   调试模式: {settings.debug}")
        print(f"   应该注册调试路由: {settings.debug or settings.environment.lower() != 'production'}")
        
        return settings
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_app_creation():
    """测试应用创建"""
    print("\n🔍 测试应用创建...")
    
    try:
        # 启用配置调试日志
        os.environ['DEBUG_CONFIG_LOADING'] = 'true'
        
        from ai_gen_hub.api.app import AIGenHubApp
        
        print("创建应用实例（启用配置调试）:")
        app_instance = AIGenHubApp(debug_config_loading=True)
        
        print("创建FastAPI应用:")
        app = app_instance.create_app()
        
        print("✅ 应用创建成功")
        
        # 检查路由
        routes = []
        debug_routes = []
        
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                for method in route.methods:
                    if method != 'HEAD':
                        route_str = f"{method} {route.path}"
                        routes.append(route_str)
                        if '/debug' in route.path:
                            debug_routes.append(route_str)
        
        print(f"   总路由数量: {len(routes)}")
        print(f"   调试路由数量: {len(debug_routes)}")
        
        if debug_routes:
            print("   调试路由列表:")
            for route in sorted(debug_routes)[:10]:  # 只显示前10个
                print(f"     {route}")
            if len(debug_routes) > 10:
                print(f"     ... 还有 {len(debug_routes) - 10} 个路由")
        
        return app, debug_routes
        
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None, []

def test_debug_endpoints():
    """测试调试端点访问"""
    print("\n🔍 测试调试端点访问...")
    
    try:
        from fastapi.testclient import TestClient
        
        # 重新创建应用
        os.environ['DEBUG_CONFIG_LOADING'] = 'true'
        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        client = TestClient(app)
        
        # 测试的端点列表
        test_endpoints = [
            "/debug/",
            "/debug/api/system/info",
            "/debug/system",
            "/debug/api-test",
            "/debug/logs",
            "/debug/config",
            "/debug/metrics",
            "/debug/tools",
            "/debug/api/system/detailed",
            "/debug/api/system/processes",
            "/debug/api/database/status",
            "/debug/api/cache/status",
            "/debug/api/endpoints/detailed"
        ]
        
        success_count = 0
        failed_endpoints = []
        
        for endpoint in test_endpoints:
            try:
                response = client.get(endpoint)
                if response.status_code == 200:
                    print(f"   ✅ {endpoint} - 200 OK")
                    success_count += 1
                elif response.status_code == 403:
                    print(f"   🔒 {endpoint} - 403 Forbidden (权限检查)")
                elif response.status_code == 404:
                    print(f"   ❌ {endpoint} - 404 Not Found")
                    failed_endpoints.append(endpoint)
                else:
                    print(f"   ⚠️ {endpoint} - {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {endpoint} - 异常: {e}")
                failed_endpoints.append(endpoint)
        
        print(f"\n📊 测试结果:")
        print(f"   成功: {success_count}/{len(test_endpoints)}")
        print(f"   失败: {len(failed_endpoints)}")
        
        if failed_endpoints:
            print(f"   失败的端点: {failed_endpoints}")
        
        return success_count == len(test_endpoints)
        
    except Exception as e:
        print(f"❌ 端点测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 测试路由修复效果")
    print("=" * 50)
    
    # 测试配置加载
    settings = test_config_loading()
    
    # 测试应用创建
    app, debug_routes = test_app_creation()
    
    # 测试端点访问
    endpoints_working = test_debug_endpoints()
    
    print("\n" + "=" * 50)
    print("📊 总体测试结果:")
    
    if settings:
        print("✅ 配置加载正常")
    else:
        print("❌ 配置加载异常")
    
    if app and debug_routes:
        print("✅ 调试路由注册正常")
    else:
        print("❌ 调试路由注册异常")
    
    if endpoints_working:
        print("✅ 调试端点访问正常")
    else:
        print("❌ 部分调试端点访问异常")
    
    if settings and debug_routes and endpoints_working:
        print("\n🎉 路由修复成功！所有测试通过")
        return True
    else:
        print("\n⚠️ 路由修复可能存在问题，请检查上述错误")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
