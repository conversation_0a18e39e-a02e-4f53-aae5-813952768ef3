<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Gen Hub 调试仪表板演示</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
        }
        
        body {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 4px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }
        
        .metric-card {
            text-align: center;
            padding: 20px;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: var(--secondary-color);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
        }
        
        .status-healthy {
            background-color: var(--success-color);
            color: white;
        }
        
        .status-warning {
            background-color: var(--warning-color);
            color: white;
        }
        
        .status-error {
            background-color: var(--danger-color);
            color: white;
        }
        
        .progress-custom {
            height: 8px;
            border-radius: 4px;
            background-color: #e2e8f0;
        }
        
        .progress-bar-custom {
            border-radius: 4px;
            transition: width 0.6s ease;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .demo-section {
            margin-bottom: 2rem;
        }
        
        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .code-preview {
            background-color: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-bug"></i>
                            调试仪表板
                        </h4>
                        <small class="text-light opacity-75">AI Gen Hub Debug Console</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#system">
                                <i class="fas fa-server"></i>
                                系统监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#api-test">
                                <i class="fas fa-code"></i>
                                API测试
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#logs">
                                <i class="fas fa-file-alt"></i>
                                日志查看
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#config">
                                <i class="fas fa-cog"></i>
                                配置信息
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#metrics">
                                <i class="fas fa-chart-line"></i>
                                性能指标
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#tools">
                                <i class="fas fa-tools"></i>
                                开发工具
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                    
                    <div class="text-center">
                        <small class="text-light opacity-75">
                            <i class="fas fa-shield-alt"></i>
                            仅开发环境可用
                        </small>
                    </div>
                </div>
            </nav>
            
            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-tachometer-alt"></i> 
                        AI Gen Hub 调试仪表板演示
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="simulateRefresh()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 功能演示 -->
                <div class="demo-section">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>演示说明：</strong> 这是AI Gen Hub调试页面的功能演示。实际部署时，这些功能将集成到FastAPI应用中，提供完整的调试和监控能力。
                    </div>
                </div>
                
                <!-- 系统状态概览 -->
                <div class="demo-section">
                    <h3>系统状态监控</h3>
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="metric-value text-primary" id="cpu-demo">45.2%</div>
                                    <div class="metric-label">CPU 使用率</div>
                                    <div class="progress progress-custom mt-2">
                                        <div class="progress-bar progress-bar-custom bg-primary" style="width: 45%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="metric-value text-success" id="memory-demo">62.8%</div>
                                    <div class="metric-label">内存使用率</div>
                                    <div class="progress progress-custom mt-2">
                                        <div class="progress-bar progress-bar-custom bg-success" style="width: 63%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="metric-value text-warning" id="disk-demo">78.5%</div>
                                    <div class="metric-label">磁盘使用率</div>
                                    <div class="progress progress-custom mt-2">
                                        <div class="progress-bar progress-bar-custom bg-warning" style="width: 78%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="metric-value text-info" id="uptime-demo">2天 14小时</div>
                                    <div class="metric-label">运行时间</div>
                                    <div class="mt-2">
                                        <small class="text-muted">156 个进程</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 功能特性展示 -->
                <div class="demo-section">
                    <h3>主要功能特性</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-server"></i>
                                </div>
                                <h5>系统状态监控</h5>
                                <p>实时监控系统资源使用情况，包括CPU、内存、磁盘、网络等关键指标。支持历史趋势图表和告警功能。</p>
                                <div class="code-preview">
GET /debug/api/system/info
{
  "system": {
    "cpu_percent": 45.2,
    "memory_percent": 62.8,
    "disk_percent": 78.5
  }
}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-code"></i>
                                </div>
                                <h5>API接口测试</h5>
                                <p>交互式API测试工具，自动发现所有端点，提供参数输入表单，显示详细的请求响应信息。</p>
                                <div class="code-preview">
POST /debug/api/test-endpoint
{
  "url": "/api/v1/text/generate",
  "method": "POST",
  "headers": {"Content-Type": "application/json"},
  "body": "{\"prompt\": \"Hello\"}"
}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <h5>实时日志查看</h5>
                                <p>实时日志流显示，支持按级别过滤、关键词搜索、时间范围筛选。提供日志统计和分析功能。</p>
                                <div class="code-preview">
GET /debug/api/logs?level=ERROR&search=timeout
{
  "logs": [
    {
      "timestamp": "2024-01-15T10:30:00Z",
      "level": "ERROR",
      "message": "Request timeout"
    }
  ]
}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <h5>配置信息展示</h5>
                                <p>安全地展示应用配置信息，自动脱敏敏感数据。支持环境变量、运行时信息查看。</p>
                                <div class="code-preview">
GET /debug/api/config
{
  "config": {
    "database": {
      "host": "localhost",
      "password": "***word123"
    }
  }
}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 安全特性 -->
                <div class="demo-section">
                    <h3>安全特性</h3>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-shield-alt"></i> 环境控制
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p>调试页面仅在开发和测试环境中可用，生产环境自动禁用。</p>
                                    <span class="status-badge status-healthy">开发环境</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-eye-slash"></i> 数据脱敏
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p>自动识别并脱敏敏感信息，如密码、API密钥等。</p>
                                    <code>password: ***word123</code>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-lock"></i> 访问控制
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p>支持基于角色的访问控制，确保只有授权用户可以访问。</p>
                                    <span class="status-badge status-healthy">已授权</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 技术实现 -->
                <div class="demo-section">
                    <h3>技术实现</h3>
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-code"></i> 核心技术栈
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>后端技术</h6>
                                    <ul>
                                        <li><strong>FastAPI</strong> - 高性能Web框架</li>
                                        <li><strong>Pydantic</strong> - 数据验证和序列化</li>
                                        <li><strong>psutil</strong> - 系统信息获取</li>
                                        <li><strong>structlog</strong> - 结构化日志</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>前端技术</h6>
                                    <ul>
                                        <li><strong>Bootstrap 5</strong> - 响应式UI框架</li>
                                        <li><strong>Chart.js</strong> - 数据可视化</li>
                                        <li><strong>Font Awesome</strong> - 图标库</li>
                                        <li><strong>Jinja2</strong> - 模板引擎</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 模拟数据刷新
        function simulateRefresh() {
            // 模拟CPU使用率变化
            const cpuElement = document.getElementById('cpu-demo');
            const newCpu = (Math.random() * 100).toFixed(1);
            cpuElement.textContent = newCpu + '%';
            
            // 模拟内存使用率变化
            const memoryElement = document.getElementById('memory-demo');
            const newMemory = (Math.random() * 100).toFixed(1);
            memoryElement.textContent = newMemory + '%';
            
            // 显示刷新动画
            const refreshBtn = document.querySelector('.btn-outline-secondary');
            const icon = refreshBtn.querySelector('i');
            icon.classList.add('fa-spin');
            
            setTimeout(() => {
                icon.classList.remove('fa-spin');
            }, 1000);
        }
        
        // 侧边栏导航
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有active类
                document.querySelectorAll('.sidebar .nav-link').forEach(l => {
                    l.classList.remove('active');
                });
                
                // 添加active类到当前链接
                this.classList.add('active');
            });
        });
        
        // 自动刷新演示数据
        setInterval(simulateRefresh, 5000);
    </script>
</body>
</html>
