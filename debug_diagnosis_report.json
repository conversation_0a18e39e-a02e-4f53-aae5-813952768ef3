{"timestamp": "2025-08-13 18:57:38", "issues": ["端口8000上的/debug/返回404", "端口8000上的/debug/api/system/info返回404", "端口8000上的/debug/api/config返回404", "端口8000上的/debug/api/endpoints返回404", "端口8000上的/debug/api/test-endpoint返回404"], "recommendations": ["端口8000的调试路由注册可能有问题，检查路由配置", "端口[8001, 8001, 8001, 8001]的test-endpoint接口存在挂起问题，需要修复异步处理"], "detailed_results": {"port_configuration": {"running_processes": ["root      997797  0.0  0.6 116744 26112 pts/27   Sl+  18:50   0:00 python start_with_debug.py", "root      997819  0.0  1.3 255776 57728 pts/27   Sl+  18:50   0:00 /root/workspace/git.atjog.com/aier/ai-gen-hub/venv/bin/python debug_standalone.py"], "port_conflicts": [], "expected_ports": {"8000": "调试界面端口", "8001": "AI Gen Hub主服务端口", "8002": "备用/测试端口"}}, "debug_routes_404": {"accessible_routes": [[8001, "GET", "/debug/", "调试主页"], [8001, "GET", "/debug/api/system/info", "系统信息"], [8001, "GET", "/debug/api/config", "配置信息"], [8001, "GET", "/debug/api/endpoints", "端点列表"]], "failed_routes": [[8000, "GET", "/debug/", "调试主页", "404"], [8000, "GET", "/debug/api/system/info", "系统信息", "404"], [8000, "GET", "/debug/api/config", "配置信息", "404"], [8000, "GET", "/debug/api/endpoints", "端点列表", "404"], [8000, "POST", "/debug/api/test-endpoint", "API测试", "404"]], "route_registration_status": null}, "test_execution_hang": {"test_endpoint_status": null, "timeout_test_results": [{"port": 8000, "timeout": 1, "duration": 0.001201629638671875, "status_code": 404, "success": true}, {"port": 8001, "timeout": 1, "duration": 1, "status_code": null, "success": false, "error": "timeout"}, {"port": 8001, "timeout": 3, "duration": 3, "status_code": null, "success": false, "error": "timeout"}, {"port": 8001, "timeout": 5, "duration": 5, "status_code": null, "success": false, "error": "timeout"}, {"port": 8001, "timeout": 10, "duration": 10, "status_code": null, "success": false, "error": "timeout"}], "async_issues": []}}}