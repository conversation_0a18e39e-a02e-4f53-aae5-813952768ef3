# AI Gen Hub - 高性能AI服务聚合平台

[![Python Version](https://img.shields.io/badge/python-3.9%2B-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104%2B-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

AI Gen Hub 是一个企业级的AI服务聚合平台，提供统一的接口来访问多个AI供应商的服务，包括文本生成、图像生成等功能。平台具备高可用性、负载均衡、缓存、监控等企业级特性。

## ✨ 核心特性

### 🔌 统一接口层
- **多供应商支持**: OpenAI、Google AI、Anthropic、Azure OpenAI等
- **标准化API**: 统一的请求/响应格式，简化集成
- **参数映射**: 自动转换不同供应商的参数格式

### ⚡ 高性能架构
- **智能负载均衡**: 基于响应时间和成功率的动态路由
- **密钥池管理**: 支持多密钥轮询、加权分配、故障转移
- **多级缓存**: 内存缓存 + Redis缓存，显著提升响应速度
- **异步处理**: 基于FastAPI的高性能异步架构

### 🛡️ 企业级可靠性
- **错误处理**: 指数退避重试策略和熔断器模式
- **监控告警**: 完整的性能指标收集和健康检查
- **日志系统**: 结构化日志记录，支持分布式追踪
- **安全认证**: JWT认证和API密钥管理

### 🚀 业务功能
- **文本生成**: 对话、补全、代码生成、翻译等
- **图像生成**: 支持多种尺寸、风格和质量参数
- **流式输出**: Server-Sent Events和WebSocket支持
- **批量处理**: 支持批量请求和进度查询

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   Mobile App    │    │   API Client    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      AI Gen Hub API       │
                    │    (FastAPI + Uvicorn)    │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     Service Layer         │
                    │  ┌─────────┬─────────┐    │
                    │  │ Router  │ Cache   │    │
                    │  │ Manager │ Manager │    │
                    │  └─────────┴─────────┘    │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │    Provider Adapters      │
                    │ ┌────────┬────────┬─────┐ │
                    │ │OpenAI  │Google  │Anth │ │
                    │ │        │   AI   │ropic│ │
                    │ └────────┴────────┴─────┘ │
                    └───────────────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.9+
- Poetry (推荐) 或 pip
- Redis (用于缓存)
- PostgreSQL (可选，用于持久化存储)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-org/ai-gen-hub.git
cd ai-gen-hub
```

2. **安装依赖**
```bash
# 使用Poetry (推荐)
poetry install

# 或使用pip
pip install -e .
```

3. **配置环境**
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入你的API密钥
vim .env
```

4. **启动服务**
```bash
# 使用Poetry
poetry run ai-gen-hub serve

# 或直接运行
python -m ai_gen_hub.main serve
```

5. **访问API文档**
打开浏览器访问 http://localhost:8000/docs 查看交互式API文档

### Docker 部署

```bash
# 启动完整的开发环境
docker-compose up -d

# 仅启动AI Gen Hub服务
docker-compose up ai-gen-hub
```

## 📖 使用示例

### 文本生成

```python
import httpx

# 发送文本生成请求
response = httpx.post("http://localhost:8000/api/v1/text/generate", json={
    "messages": [
        {"role": "user", "content": "你好，请介绍一下人工智能"}
    ],
    "model": "gpt-3.5-turbo",
    "max_tokens": 1000,
    "temperature": 0.7
})

result = response.json()
print(result["choices"][0]["message"]["content"])
```

### 流式文本生成

```python
import httpx

# 流式请求
with httpx.stream("POST", "http://localhost:8000/api/v1/text/stream", json={
    "messages": [{"role": "user", "content": "写一首关于春天的诗"}],
    "model": "gpt-3.5-turbo"
}) as response:
    for chunk in response.iter_text():
        if chunk:
            print(chunk, end="", flush=True)
```

### 图像生成

```python
import httpx

# 图像生成请求
response = httpx.post("http://localhost:8000/api/v1/image/generate", json={
    "prompt": "一只可爱的小猫在花园里玩耍",
    "size": "1024x1024",
    "quality": "hd",
    "n": 1
})

result = response.json()
image_url = result["data"][0]["url"]
print(f"生成的图像URL: {image_url}")
```

## 🔧 配置说明

### 环境变量配置

主要的环境变量配置项：

```bash
# AI供应商配置
OPENAI_API_KEYS=sk-xxx,sk-yyy,sk-zzz
GOOGLE_AI_API_KEYS=AIzaSyXXX,AIzaSyYYY
ANTHROPIC_API_KEYS=sk-ant-xxx,sk-ant-yyy

# 缓存配置
REDIS_URL=redis://localhost:6379/0
MEMORY_CACHE_SIZE=1000

# 监控配置
PROMETHEUS_ENABLED=true
LOG_LEVEL=INFO
```

### 供应商配置

支持的AI供应商及其配置：

| 供应商 | 支持的服务 | 配置前缀 |
|--------|------------|----------|
| OpenAI | 文本生成、图像生成 | `OPENAI_` |
| Google AI | 文本生成 | `GOOGLE_AI_` |
| Anthropic | 文本生成 | `ANTHROPIC_` |
| Azure OpenAI | 文本生成、图像生成 | `AZURE_` |

## 📊 监控和运维

### 健康检查

```bash
# 检查所有供应商状态
ai-gen-hub health-check

# 检查特定供应商
ai-gen-hub health-check --provider openai
```

### 监控指标

平台提供以下监控指标：

- **请求指标**: QPS、响应时间、错误率
- **供应商指标**: 可用性、延迟、成功率
- **缓存指标**: 命中率、内存使用
- **系统指标**: CPU、内存、网络

### 日志系统

结构化日志记录，支持：

- 请求/响应日志
- 错误和异常日志
- 性能指标日志
- 安全审计日志

## 🧪 测试

```bash
# 运行所有测试
poetry run pytest

# 运行单元测试
poetry run pytest tests/unit/

# 运行集成测试
poetry run pytest tests/integration/

# 生成覆盖率报告
poetry run pytest --cov=ai_gen_hub --cov-report=html
```

## 📚 API文档

详细的API文档可以通过以下方式访问：

- **交互式文档**: http://localhost:8000/docs (Swagger UI)
- **ReDoc文档**: http://localhost:8000/redoc
- **OpenAPI规范**: http://localhost:8000/openapi.json

## 🤝 贡献指南

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细的贡献指南。

### 开发环境设置

```bash
# 安装开发依赖
poetry install --with dev

# 安装pre-commit钩子
pre-commit install

# 运行代码格式化
poetry run black src/ tests/
poetry run isort src/ tests/

# 运行类型检查
poetry run mypy src/
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果你遇到问题或有疑问：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索 [Issues](https://github.com/your-org/ai-gen-hub/issues)
3. 创建新的 [Issue](https://github.com/your-org/ai-gen-hub/issues/new)
4. 联系我们: <EMAIL>

## 🗺️ 路线图

- [ ] 支持更多AI供应商 (Claude-3, Gemini Pro等)
- [ ] 实现A/B测试功能
- [ ] 添加成本分析和预算控制
- [ ] 支持自定义模型微调
- [ ] 实现多租户架构
- [ ] 添加GraphQL API支持

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！
