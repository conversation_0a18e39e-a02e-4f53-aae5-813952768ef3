#!/usr/bin/env python3
"""
简单的路由修复测试

验证路由注册修复是否成功
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def load_env_file():
    """手动加载 .env 文件"""
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

# 在导入其他模块之前加载环境变量
load_env_file()

def test_development_environment():
    """测试开发环境下的路由注册"""
    print("🔍 测试开发环境下的路由注册...")
    
    # 设置开发环境
    os.environ['DEBUG'] = 'true'
    os.environ['ENVIRONMENT'] = 'development'
    
    try:
        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        
        # 收集调试路由
        debug_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and '/debug' in route.path:
                debug_routes.append(route.path)
        
        print(f"   找到 {len(debug_routes)} 个调试路由")
        
        # 验证关键路由存在
        expected_routes = [
            '/debug/',
            '/debug/api/system/info',
            '/debug/system',
            '/debug/api-test',
            '/debug/logs',
            '/debug/config',
            '/debug/metrics',
            '/debug/tools'
        ]
        
        missing_routes = []
        for expected in expected_routes:
            if expected not in debug_routes:
                missing_routes.append(expected)
        
        if missing_routes:
            print(f"   ❌ 缺少路由: {missing_routes}")
            return False
        else:
            print("   ✅ 所有预期的调试路由都已注册")
            return True
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_production_environment():
    """测试生产环境下的路由注册"""
    print("\n🔍 测试生产环境下的路由注册...")

    # 设置生产环境
    os.environ['DEBUG'] = 'false'
    os.environ['ENVIRONMENT'] = 'production'

    try:
        # 清除模块缓存以应用新的环境变量
        import sys
        modules_to_clear = [
            'ai_gen_hub.config.settings',
            'ai_gen_hub.api.app'
        ]
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]

        # 强制重新加载配置
        from ai_gen_hub.config.settings import reload_settings
        reload_settings(debug_logging=True)

        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        
        # 收集调试路由
        debug_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and '/debug' in route.path:
                debug_routes.append(route.path)
        
        print(f"   找到 {len(debug_routes)} 个调试路由")
        
        if len(debug_routes) == 0:
            print("   ✅ 生产环境下正确地没有注册调试路由")
            return True
        else:
            print(f"   ❌ 生产环境下不应该有调试路由: {debug_routes}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_debug_mode_override():
    """测试DEBUG=true时的路由注册（即使在非开发环境）"""
    print("\n🔍 测试DEBUG=true时的路由注册...")

    # 设置调试模式但非开发环境
    os.environ['DEBUG'] = 'true'
    os.environ['ENVIRONMENT'] = 'staging'

    try:
        # 清除模块缓存
        import sys
        modules_to_clear = [
            'ai_gen_hub.config.settings',
            'ai_gen_hub.api.app'
        ]
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]

        # 强制重新加载配置
        from ai_gen_hub.config.settings import reload_settings
        reload_settings(debug_logging=True)

        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        
        # 收集调试路由
        debug_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and '/debug' in route.path:
                debug_routes.append(route.path)
        
        print(f"   找到 {len(debug_routes)} 个调试路由")
        
        if len(debug_routes) > 0:
            print("   ✅ DEBUG=true时正确注册了调试路由")
            return True
        else:
            print("   ❌ DEBUG=true时应该注册调试路由")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_diagnostic_endpoint():
    """测试诊断端点"""
    print("\n🔍 测试诊断端点...")
    
    # 恢复开发环境设置
    os.environ['DEBUG'] = 'true'
    os.environ['ENVIRONMENT'] = 'development'
    
    try:
        # 清除模块缓存
        import sys
        modules_to_clear = [
            'ai_gen_hub.config.settings',
            'ai_gen_hub.api.app'
        ]
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        from ai_gen_hub.api.app import create_app
        from fastapi.testclient import TestClient
        
        app = create_app()
        client = TestClient(app)
        
        response = client.get("/diagnostic")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 诊断端点正常工作")
            print(f"      调试模式: {data['app_settings']['debug']}")
            print(f"      环境: {data['app_settings']['environment']}")
            print(f"      调试路由数量: {data['routes']['debug_routes']}")
            return True
        else:
            print(f"   ❌ 诊断端点返回错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_authentication_middleware():
    """测试认证中间件对调试路径的处理"""
    print("\n🔍 测试认证中间件...")
    
    try:
        from ai_gen_hub.api.middleware import AuthenticationMiddleware
        from fastapi import FastAPI
        
        # 测试开发环境
        os.environ['DEBUG'] = 'true'
        os.environ['ENVIRONMENT'] = 'development'
        
        app = FastAPI()
        middleware = AuthenticationMiddleware(app)
        
        debug_paths_public = [
            middleware._is_public_path("/debug/"),
            middleware._is_public_path("/debug/api/system/info"),
            middleware._is_public_path("/diagnostic")
        ]
        
        if all(debug_paths_public):
            print("   ✅ 开发环境下调试路径正确设置为公开")
        else:
            print("   ❌ 开发环境下调试路径应该是公开的")
            return False
        
        # 测试生产环境
        os.environ['DEBUG'] = 'false'
        os.environ['ENVIRONMENT'] = 'production'
        
        middleware = AuthenticationMiddleware(app)
        
        debug_not_public = not middleware._is_public_path("/debug/")
        diagnostic_public = middleware._is_public_path("/diagnostic")
        
        if debug_not_public and diagnostic_public:
            print("   ✅ 生产环境下调试路径正确设置为非公开，诊断端点仍为公开")
            return True
        else:
            print("   ❌ 生产环境下路径权限设置不正确")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 路由修复验证测试")
    print("=" * 50)
    
    tests = [
        test_development_environment,
        test_production_environment,
        test_debug_mode_override,
        test_diagnostic_endpoint,
        test_authentication_middleware
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！路由修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
