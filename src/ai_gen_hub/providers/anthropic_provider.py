"""
Anthropic 供应商适配器

实现Anthropic Claude API的完整适配，包括：
- 文本生成（Claude 3系列）
- 流式输出支持
- 错误处理和重试
"""

import json
import time
from typing import Any, AsyncIterator, Dict, List, Union
from uuid import uuid4

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    MessageRole,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
)
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.utils.key_manager import KeyManager


class AnthropicProvider(BaseProvider):
    """Anthropic供应商适配器"""
    
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        """初始化Anthropic适配器
        
        Args:
            config: Anthropic配置
            key_manager: 密钥管理器
        """
        super().__init__("anthropic", config, key_manager)
        
        # 设置基础URL
        self.base_url = config.base_url or "https://api.anthropic.com"
        
        # 支持的模型类型
        self._supported_model_types = [ModelType.TEXT_GENERATION]
        
        # 支持的模型列表
        self._supported_models = [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            "claude-2.1",
            "claude-2.0",
            "claude-instant-1.2",
        ]
        
        # 模型映射
        self._model_mapping = {
            "claude-3-opus": "claude-3-opus-20240229",
            "claude-3-sonnet": "claude-3-sonnet-20240229",
            "claude-3-haiku": "claude-3-haiku-20240307",
            "claude-2": "claude-2.1",
            "claude-instant": "claude-instant-1.2",
            "claude-latest": "claude-3-sonnet-20240229",
        }
        
        # Anthropic API版本
        self.api_version = "2023-06-01"
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """执行健康检查"""
        try:
            # 发送一个简单的消息请求来测试API
            headers = {
                "x-api-key": api_key,
                "anthropic-version": self.api_version,
            }
            
            test_data = {
                "model": "claude-3-haiku-20240307",
                "max_tokens": 10,
                "messages": [
                    {"role": "user", "content": "Hello"}
                ]
            }
            
            response = await self._make_request(
                "POST",
                f"{self.base_url}/v1/messages",
                headers=headers,
                json_data=test_data
            )
            return response.status_code == 200
        except Exception:
            return False
    
    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """实现文本生成"""
        # 构建请求数据
        request_data = self._build_text_request(request)
        
        # 设置请求头
        headers = {
            "x-api-key": api_key,
            "anthropic-version": self.api_version,
        }
        
        # 构建URL
        url = f"{self.base_url}/v1/messages"
        
        if request.stream:
            # 流式响应
            return self._handle_stream_response(request, headers, url, request_data)
        else:
            # 普通响应
            response = await self._make_request(
                "POST",
                url,
                headers=headers,
                json_data=request_data
            )
            
            response_data = response.json()
            return self._parse_text_response(response_data, request)
    
    def _build_text_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建文本生成请求数据"""
        # 转换消息格式
        messages = []
        system_message = None
        
        for msg in request.messages:
            if msg.role == MessageRole.SYSTEM:
                # Anthropic将system消息单独处理
                system_message = msg.content
            elif msg.role in [MessageRole.USER, MessageRole.ASSISTANT]:
                message_dict = {
                    "role": msg.role,
                    "content": msg.content
                }
                messages.append(message_dict)
            # 跳过其他角色的消息
        
        # 构建基础请求
        request_data = {
            "model": self.map_model_name(request.model),
            "messages": messages,
            "max_tokens": request.max_tokens or 4096,  # Anthropic要求必须指定max_tokens
            "stream": request.stream,
        }
        
        # 添加system消息
        if system_message:
            request_data["system"] = system_message
        
        # 添加可选参数
        if request.temperature is not None:
            request_data["temperature"] = request.temperature
        if request.top_p is not None:
            request_data["top_p"] = request.top_p
        if request.top_k is not None:
            request_data["top_k"] = request.top_k
        if request.stop is not None:
            request_data["stop_sequences"] = (
                [request.stop] if isinstance(request.stop, str) else request.stop
            )
        
        # 供应商特定参数
        if request.provider_params:
            request_data.update(request.provider_params)
        
        return request_data
    
    def _parse_text_response(
        self,
        response_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> TextGenerationResponse:
        """解析文本生成响应"""
        # 解析内容
        content_blocks = response_data.get("content", [])
        content_text = ""
        
        for block in content_blocks:
            if block.get("type") == "text":
                content_text += block.get("text", "")
        
        message = Message(
            role=MessageRole.ASSISTANT,
            content=content_text
        )
        
        # 获取结束原因
        stop_reason = response_data.get("stop_reason", "stop")
        if stop_reason == "end_turn":
            finish_reason = "stop"
        elif stop_reason == "max_tokens":
            finish_reason = "length"
        elif stop_reason == "stop_sequence":
            finish_reason = "stop"
        else:
            finish_reason = stop_reason
        
        choice = TextGenerationChoice(
            index=0,
            message=message,
            finish_reason=finish_reason
        )
        
        # 解析使用量
        usage_data = response_data.get("usage", {})
        usage = Usage(
            prompt_tokens=usage_data.get("input_tokens", 0),
            completion_tokens=usage_data.get("output_tokens", 0),
            total_tokens=usage_data.get("input_tokens", 0) + usage_data.get("output_tokens", 0)
        )
        
        return TextGenerationResponse(
            id=response_data.get("id", str(uuid4())),
            object="chat.completion",
            created=int(time.time()),
            model=response_data.get("model", request.model),
            choices=[choice],
            usage=usage,
            provider=self.name,
            request_id=str(uuid4()),
            processing_time=0.0
        )
    
    async def _handle_stream_response(
        self,
        request: TextGenerationRequest,
        headers: Dict[str, str],
        url: str,
        request_data: Dict[str, Any]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理流式响应"""
        async for chunk in self._make_request(
            "POST",
            url,
            headers=headers,
            json_data=request_data,
            stream=True
        ):
            # 解析SSE数据
            chunk_str = chunk.decode('utf-8')
            for line in chunk_str.split('\n'):
                line = line.strip()
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    try:
                        data = json.loads(data_str)
                        
                        # 处理不同类型的事件
                        event_type = data.get("type")
                        
                        if event_type == "content_block_delta":
                            # 内容增量
                            delta = data.get("delta", {})
                            if delta.get("type") == "text_delta":
                                text = delta.get("text", "")
                                
                                choice_data = {
                                    "index": 0,
                                    "delta": {
                                        "role": "assistant",
                                        "content": text
                                    }
                                }
                                
                                chunk_response = TextGenerationStreamChunk(
                                    id=str(uuid4()),
                                    object="chat.completion.chunk",
                                    created=int(time.time()),
                                    model=request.model,
                                    choices=[choice_data],
                                    provider=self.name,
                                    request_id=str(uuid4())
                                )
                                
                                yield chunk_response
                        
                        elif event_type == "message_stop":
                            # 消息结束
                            choice_data = {
                                "index": 0,
                                "delta": {},
                                "finish_reason": "stop"
                            }
                            
                            chunk_response = TextGenerationStreamChunk(
                                id=str(uuid4()),
                                object="chat.completion.chunk",
                                created=int(time.time()),
                                model=request.model,
                                choices=[choice_data],
                                provider=self.name,
                                request_id=str(uuid4())
                            )
                            
                            yield chunk_response
                            return
                        
                    except json.JSONDecodeError:
                        continue
    
    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """Anthropic暂不支持图像生成"""
        raise NotImplementedError("Anthropic暂不支持图像生成功能")
    
    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认的HTTP头"""
        return {
            "User-Agent": f"AI-Gen-Hub/1.0.0 (anthropic)",
            "Content-Type": "application/json",
            "anthropic-version": self.api_version,
        }
