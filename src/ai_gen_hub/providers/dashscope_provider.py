"""
阿里云百炼 DashScope AI Provider

基于阿里云百炼 DashScope API 的 AI 服务提供商实现。
支持通义千问系列模型的文本生成功能，包括流式和非流式响应。

主要功能：
- 文本生成（流式和非流式）
- 健康检查和连接验证
- 模型名称映射和向后兼容
- 完善的错误处理和重试机制
- 支持多轮对话和系统指令

支持的模型：
- 通义千问 Max/Plus/Turbo 系列
- Qwen3 系列（235B, 32B, 8B）
- Qwen2.5 系列（72B, 32B, 14B, 7B）
- Qwen2.5-Coder 系列

作者：AI Gen Hub Team
版本：1.0.0
"""

import json
import logging
import time
from typing import Any, AsyncGenerator, Dict, List, Optional
from uuid import uuid4

from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    MessageRole,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
)


class DashScopeProvider(BaseProvider):
    """阿里云百炼 DashScope AI Provider 实现类
    
    基于 DashScope API 提供文本生成服务，支持通义千问系列模型。
    实现了完整的流式和非流式文本生成功能。
    """

    def __init__(self, config, key_manager):
        """初始化 DashScope Provider

        Args:
            config: 供应商配置对象
            key_manager: API 密钥管理器
        """
        super().__init__("dashscope", config, key_manager)
        self.base_url = "https://dashscope.aliyuncs.com/api/v1"
        
        # 支持的模型类型
        self._supported_model_types = [ModelType.TEXT_GENERATION]

        # 支持的模型列表（按性能和发布时间排序）
        self._supported_models = [
            # 通义千问最新系列（推荐使用）
            "qwen-max",           # 最强性能模型
            "qwen-plus",          # 平衡性能和成本
            "qwen-turbo",         # 快速响应模型

            # Qwen3 系列（最新一代）
            "qwen3-235b-a22b",    # 超大规模模型
            "qwen3-32b",          # 大规模模型
            "qwen3-8b",           # 中等规模模型

            # Qwen2.5 系列（稳定版本）
            "qwen2.5-72b-instruct",
            "qwen2.5-32b-instruct",
            "qwen2.5-14b-instruct",
            "qwen2.5-7b-instruct",
            "qwen2.5-3b-instruct",
            "qwen2.5-1.5b-instruct",
            "qwen2.5-0.5b-instruct",

            # Qwen2.5-Coder 系列（代码专用）
            "qwen2.5-coder-32b-instruct",
            "qwen2.5-coder-14b-instruct",
            "qwen2.5-coder-7b-instruct",
            "qwen2.5-coder-1.5b-instruct",

            # Qwen2 系列（向后兼容）
            "qwen2-72b-instruct",
            "qwen2-57b-a14b-instruct",
            "qwen2-7b-instruct",
            "qwen2-1.5b-instruct",
            "qwen2-0.5b-instruct",
        ]
        
        # 模型名称映射（向后兼容和别名支持）
        self._model_mapping = {
            # 旧版本模型名称映射
            "qwen-max-1201": "qwen-max",
            "qwen-max-0919": "qwen-max",
            "qwen-plus-1201": "qwen-plus",
            "qwen-plus-0919": "qwen-plus",
            "qwen-turbo-1201": "qwen-turbo",
            "qwen-turbo-0919": "qwen-turbo",
            
            # 通用别名
            "qwen-latest": "qwen-max",
            "qwen-best": "qwen-max",
            "qwen-fast": "qwen-turbo",
            "qwen-balanced": "qwen-plus",
            
            # Qwen3 别名
            "qwen3-latest": "qwen3-235b-a22b",
            "qwen3-large": "qwen3-32b",
            "qwen3-medium": "qwen3-8b",
            
            # Qwen2.5 别名
            "qwen2.5-latest": "qwen2.5-72b-instruct",
            "qwen2.5-large": "qwen2.5-32b-instruct",
            "qwen2.5-medium": "qwen2.5-14b-instruct",
            "qwen2.5-small": "qwen2.5-7b-instruct",
            
            # 代码模型别名
            "qwen-coder": "qwen2.5-coder-32b-instruct",
            "qwen-coder-large": "qwen2.5-coder-32b-instruct",
            "qwen-coder-medium": "qwen2.5-coder-14b-instruct",
            "qwen-coder-small": "qwen2.5-coder-7b-instruct",
        }

    def map_model_name(self, model_name: str) -> str:
        """映射模型名称到 DashScope 支持的格式
        
        Args:
            model_name: 输入的模型名称
            
        Returns:
            str: 映射后的模型名称
        """
        # 如果是直接支持的模型名称，直接返回
        if model_name in self._supported_models:
            return model_name
            
        # 如果有映射关系，返回映射后的名称
        if model_name in self._model_mapping:
            mapped_name = self._model_mapping[model_name]
            self.logger.info(f"模型名称映射: {model_name} -> {mapped_name}")
            return mapped_name
            
        # 如果没有找到映射，记录警告并返回原名称
        self.logger.warning(f"未知的模型名称: {model_name}，将尝试直接使用")
        return model_name

    async def _perform_health_check(self, api_key: str) -> bool:
        """执行健康检查
        
        通过调用模型列表 API 来验证 API 密钥的有效性和服务可用性。
        
        Args:
            api_key: DashScope API 密钥
            
        Returns:
            bool: 健康检查是否成功
        """
        try:
            # 使用简单的文本生成请求来验证 API 密钥
            url = f"{self.base_url}/services/aigc/text-generation/generation"
            
            # 构建最小的测试请求
            test_data = {
                "model": "qwen-turbo",  # 使用最快的模型进行健康检查
                "input": {
                    "messages": [
                        {"role": "user", "content": "Hello"}
                    ]
                },
                "parameters": {
                    "max_tokens": 10,
                    "result_format": "message"
                }
            }
            
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
                "User-Agent": f"AI-Gen-Hub/1.0.0 (dashscope)"
            }
            
            response = await self._make_request(
                "POST", 
                url, 
                json_data=test_data,
                headers=headers
            )
            
            # 检查响应状态
            if response.status_code == 200:
                response_data = response.json()
                # 检查是否有错误
                if "output" in response_data:
                    self.logger.info("DashScope API 健康检查成功")
                    return True
                else:
                    self.logger.warning(f"DashScope API 响应异常: {response_data}")
                    return False
            else:
                self.logger.error(f"DashScope API 健康检查失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"DashScope API 健康检查异常: {str(e)}")
            return False

    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认的 HTTP 请求头
        
        Returns:
            Dict[str, str]: 默认请求头字典
        """
        return {
            "User-Agent": f"AI-Gen-Hub/1.0.0 (dashscope)",
            "Content-Type": "application/json",
        }

    def _handle_dashscope_error(self, response_data: Dict[str, Any]) -> str:
        """处理 DashScope API 特定的错误格式
        
        DashScope API 的错误响应可能有两种格式：
        1. {"code": "InvalidApiKey", "message": "...", "request_id": "..."}
        2. {"error": {"message": "...", "type": "...", "code": "..."}}
        
        Args:
            response_data: API 响应数据
            
        Returns:
            str: 格式化的错误消息
        """
        # 格式1：直接的错误字段
        if "code" in response_data and "message" in response_data:
            code = response_data.get("code", "未知错误")
            message = response_data.get("message", "未知错误消息")
            request_id = response_data.get("request_id", "")
            
            error_msg = f"DashScope API 错误 [{code}]: {message}"
            if request_id:
                error_msg += f" (请求ID: {request_id})"
            
            return error_msg
        
        # 格式2：嵌套的错误对象
        if "error" in response_data:
            error = response_data["error"]
            message = error.get("message", "未知错误")
            error_type = error.get("type", "")
            code = error.get("code", "")
            
            error_msg = f"DashScope API 错误"
            if code:
                error_msg += f" [{code}]"
            if error_type:
                error_msg += f" ({error_type})"
            error_msg += f": {message}"
            
            return error_msg
        
        # 如果不是标准的错误格式，返回原始数据
        return f"DashScope API 未知错误: {str(response_data)}"

    def _build_text_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建 DashScope 文本生成请求数据

        将标准的文本生成请求转换为 DashScope API 格式。

        Args:
            request: 标准文本生成请求对象

        Returns:
            Dict[str, Any]: DashScope API 请求数据
        """
        # 映射模型名称
        model_name = self.map_model_name(request.model)

        # 转换消息格式
        messages = []
        for message in request.messages:
            # DashScope 支持标准的 OpenAI 消息格式
            msg_dict = {
                "role": message.role.value,
                "content": message.content
            }
            messages.append(msg_dict)

        # 构建基础请求数据
        request_data = {
            "model": model_name,
            "input": {
                "messages": messages
            },
            "parameters": {
                "result_format": "message"  # 使用消息格式返回
            }
        }

        # 添加生成参数
        if request.max_tokens is not None:
            request_data["parameters"]["max_tokens"] = request.max_tokens

        if request.temperature is not None:
            request_data["parameters"]["temperature"] = request.temperature

        if request.top_p is not None:
            request_data["parameters"]["top_p"] = request.top_p

        if request.frequency_penalty is not None:
            request_data["parameters"]["repetition_penalty"] = 1.0 + request.frequency_penalty

        if request.presence_penalty is not None:
            # DashScope 没有直接的 presence_penalty，可以通过其他参数模拟
            pass

        # 流式输出配置
        if request.stream:
            request_data["parameters"]["incremental_output"] = True

        # 添加停止词
        if request.stop:
            if isinstance(request.stop, str):
                request_data["parameters"]["stop"] = [request.stop]
            else:
                request_data["parameters"]["stop"] = request.stop

        # 添加种子（如果支持）
        if hasattr(request, 'seed') and request.seed is not None:
            request_data["parameters"]["seed"] = request.seed

        # 添加 DashScope 特有的参数
        if hasattr(request, 'enable_search') and request.enable_search:
            request_data["parameters"]["enable_search"] = True

        return request_data

    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> TextGenerationResponse:
        """实现文本生成的核心逻辑

        Args:
            request: 文本生成请求对象
            api_key: DashScope API 密钥

        Returns:
            TextGenerationResponse: 文本生成响应对象
        """
        # 构建请求数据
        request_data = self._build_text_request(request)
        model_name = request_data["model"]

        # 构建请求头
        headers = self._get_default_headers()
        headers["Authorization"] = f"Bearer {api_key}"

        if request.stream:
            # 流式生成
            headers["X-DashScope-SSE"] = "enable"

            url = f"{self.base_url}/services/aigc/text-generation/generation"

            # 返回异步生成器
            return self._handle_stream_response(
                url, request_data, headers, request
            )
        else:
            # 非流式生成
            url = f"{self.base_url}/services/aigc/text-generation/generation"

            response = await self._make_request(
                "POST",
                url,
                json_data=request_data,
                headers=headers
            )

            response_data = response.json()

            # 检查响应中是否有错误
            if "code" in response_data or "error" in response_data:
                error_msg = self._handle_dashscope_error(response_data)
                self.logger.error(f"DashScope API 返回错误: {error_msg}")
                from ai_gen_hub.core.exceptions import APIError
                raise APIError(error_msg)

            return self._parse_text_response(response_data, request)

    def _parse_text_response(
        self,
        response_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> TextGenerationResponse:
        """解析 DashScope 文本生成响应

        Args:
            response_data: DashScope API 响应数据
            request: 原始请求对象

        Returns:
            TextGenerationResponse: 标准文本生成响应对象
        """
        # 解析输出内容
        output = response_data.get("output", {})

        if "choices" in output:
            # 新格式：choices 数组
            choices_data = output["choices"]
            choices = []

            for i, choice_data in enumerate(choices_data):
                message_data = choice_data.get("message", {})
                content = message_data.get("content", "")

                # 创建消息对象
                message = Message(
                    role=MessageRole.ASSISTANT,
                    content=content
                )

                # 映射结束原因
                finish_reason = choice_data.get("finish_reason", "stop")
                finish_reason_mapping = {
                    "stop": "stop",
                    "length": "length",
                    "null": None,
                    "tool_calls": "tool_calls"
                }
                mapped_finish_reason = finish_reason_mapping.get(finish_reason, "stop")

                choice = TextGenerationChoice(
                    index=i,
                    message=message,
                    finish_reason=mapped_finish_reason
                )
                choices.append(choice)

        else:
            # 旧格式：直接的 text 字段
            text_content = output.get("text", "")

            message = Message(
                role=MessageRole.ASSISTANT,
                content=text_content
            )

            choice = TextGenerationChoice(
                index=0,
                message=message,
                finish_reason="stop"
            )
            choices = [choice]

        # 解析使用量信息
        usage_data = response_data.get("usage", {})

        # DashScope 的使用量格式可能不同，尝试多种格式
        prompt_tokens = (
            usage_data.get("input_tokens") or
            usage_data.get("prompt_tokens") or
            0
        )
        completion_tokens = (
            usage_data.get("output_tokens") or
            usage_data.get("completion_tokens") or
            0
        )
        total_tokens = (
            usage_data.get("total_tokens") or
            prompt_tokens + completion_tokens
        )

        usage = Usage(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens
        )

        return TextGenerationResponse(
            id=response_data.get("request_id", str(uuid4())),
            object="chat.completion",
            created=int(time.time()),
            model=request.model,
            choices=choices,
            usage=usage,
            provider=self.name,
            request_id=response_data.get("request_id", str(uuid4())),
            processing_time=0.0  # 将在调用层计算
        )

    async def _handle_stream_response(
        self,
        url: str,
        request_data: Dict[str, Any],
        headers: Dict[str, str],
        request: TextGenerationRequest
    ) -> AsyncGenerator[TextGenerationStreamChunk, None]:
        """处理流式响应

        Args:
            url: 请求 URL
            request_data: 请求数据
            headers: 请求头
            request: 原始请求对象

        Yields:
            TextGenerationStreamChunk: 流式响应块
        """
        response = await self._make_request(
            "POST",
            url,
            json_data=request_data,
            headers=headers,
            stream=True
        )

        async for line in response.aiter_lines():
            line = line.strip()

            # 跳过空行和非数据行
            if not line or not line.startswith("data:"):
                continue

            # 提取数据部分
            data_str = line[5:].strip()  # 移除 "data:" 前缀

            # 跳过结束标志
            if data_str == "[DONE]":
                break

            try:
                data = json.loads(data_str)

                # 检查是否有错误
                if "code" in data or "error" in data:
                    error_msg = self._handle_dashscope_error(data)
                    self.logger.error(f"流式响应中收到错误: {error_msg}")
                    continue

                # 解析输出内容
                output = data.get("output", {})

                if "choices" in output:
                    # 新格式：choices 数组
                    choices_data = output["choices"]
                    choices = []

                    for i, choice_data in enumerate(choices_data):
                        message_data = choice_data.get("message", {})
                        content = message_data.get("content", "")

                        choice_data_dict = {
                            "index": i,
                            "delta": {
                                "role": "assistant",
                                "content": content
                            }
                        }

                        # 检查是否结束
                        finish_reason = choice_data.get("finish_reason")
                        if finish_reason and finish_reason != "null":
                            finish_reason_mapping = {
                                "stop": "stop",
                                "length": "length",
                                "tool_calls": "tool_calls"
                            }
                            choice_data_dict["finish_reason"] = finish_reason_mapping.get(
                                finish_reason, "stop"
                            )

                        choices.append(choice_data_dict)

                else:
                    # 旧格式：直接的 text 字段
                    text_content = output.get("text", "")

                    choices = [{
                        "index": 0,
                        "delta": {
                            "role": "assistant",
                            "content": text_content
                        }
                    }]

                    # 检查结束原因
                    finish_reason = output.get("finish_reason")
                    if finish_reason and finish_reason != "null":
                        choices[0]["finish_reason"] = "stop"

                # 解析使用量信息（如果有）
                usage_data = data.get("usage", {})
                usage = None
                if usage_data:
                    prompt_tokens = usage_data.get("input_tokens", 0) or usage_data.get("prompt_tokens", 0)
                    completion_tokens = usage_data.get("output_tokens", 0) or usage_data.get("completion_tokens", 0)
                    total_tokens = usage_data.get("total_tokens", prompt_tokens + completion_tokens)

                    usage = Usage(
                        prompt_tokens=prompt_tokens,
                        completion_tokens=completion_tokens,
                        total_tokens=total_tokens
                    )

                # 创建流式响应块
                chunk = TextGenerationStreamChunk(
                    id=data.get("request_id", str(uuid4())),
                    object="chat.completion.chunk",
                    created=int(time.time()),
                    model=request.model,
                    choices=choices,
                    provider=self.name,
                    request_id=data.get("request_id", str(uuid4()))
                )

                yield chunk

            except json.JSONDecodeError as e:
                self.logger.warning(f"解析流式响应 JSON 失败: {e}, 数据: {data_str}")
                continue
            except Exception as e:
                self.logger.error(f"处理流式响应时发生错误: {e}")
                continue

    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """实现图像生成（DashScope 暂不支持图像生成）

        Args:
            request: 图像生成请求对象
            api_key: DashScope API 密钥

        Returns:
            ImageGenerationResponse: 图像生成响应对象

        Raises:
            NotImplementedError: DashScope 暂不支持图像生成功能
        """
        raise NotImplementedError("DashScope 暂不支持图像生成功能，请使用其他支持图像生成的模型服务商")
