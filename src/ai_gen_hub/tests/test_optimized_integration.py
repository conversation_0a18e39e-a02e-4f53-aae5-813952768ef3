"""
优化版本集成测试

测试优化版本文本生成请求在整个系统中的集成情况，
包括服务层、API层和供应商适配器的协同工作。
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from ai_gen_hub.core.interfaces import (
    OptimizedTextGenerationRequest,
    TextGenerationRequest,
    TextGenerationResponse,
    Message,
    MessageRole,
    GenerationConfig,
    StreamConfig,
    SafetyConfig,
    RequestAdapter,
)
from ai_gen_hub.services.text_generation import TextGenerationService
from ai_gen_hub.providers.openai_provider import OpenAIProvider


class TestOptimizedIntegration:
    """优化版本集成测试类"""
    
    @pytest.fixture
    def sample_optimized_request(self):
        """创建示例优化版本请求"""
        return OptimizedTextGenerationRequest(
            messages=[
                Message(role=MessageRole.SYSTEM, content="你是一个有用的AI助手。"),
                Message(role=MessageRole.USER, content="请介绍一下人工智能。")
            ],
            model="gpt-4",
            generation=GenerationConfig(
                temperature=0.8,
                max_tokens=1000,
                top_p=0.9
            ),
            stream=StreamConfig(enabled=False),
            safety=SafetyConfig(
                content_filter=True,
                safety_level="medium"
            )
        )
    
    @pytest.fixture
    def sample_legacy_request(self):
        """创建示例传统请求"""
        return TextGenerationRequest(
            messages=[
                Message(role=MessageRole.USER, content="Hello, world!")
            ],
            model="gpt-3.5-turbo",
            temperature=0.7,
            max_tokens=500,
            stream=False
        )
    
    @pytest.fixture
    def sample_dict_request(self):
        """创建示例字典格式请求"""
        return {
            "messages": [
                {"role": "user", "content": "Test message"}
            ],
            "model": "gpt-4",
            "temperature": 0.8,
            "max_tokens": 1000,
            "stream": True
        }
    
    def test_request_adapter_optimized_request(self, sample_optimized_request):
        """测试请求适配器处理优化版本请求"""
        adapted = RequestAdapter.adapt_request(sample_optimized_request)
        
        assert isinstance(adapted, OptimizedTextGenerationRequest)
        assert adapted == sample_optimized_request
        assert adapted.model == "gpt-4"
        assert adapted.generation.temperature == 0.8
    
    def test_request_adapter_legacy_request(self, sample_legacy_request):
        """测试请求适配器处理传统请求"""
        adapted = RequestAdapter.adapt_request(sample_legacy_request)
        
        assert isinstance(adapted, OptimizedTextGenerationRequest)
        assert adapted.model == "gpt-3.5-turbo"
        assert adapted.generation.temperature == 0.7
        assert adapted.generation.max_tokens == 500
        assert not adapted.stream.enabled
    
    def test_request_adapter_dict_request(self, sample_dict_request):
        """测试请求适配器处理字典格式请求"""
        adapted = RequestAdapter.adapt_request(sample_dict_request)
        
        assert isinstance(adapted, OptimizedTextGenerationRequest)
        assert adapted.model == "gpt-4"
        assert adapted.generation.temperature == 0.8
        assert adapted.generation.max_tokens == 1000
        assert adapted.stream.enabled
    
    def test_legacy_to_optimized_conversion(self, sample_legacy_request):
        """测试传统格式到优化版本的转换"""
        optimized = OptimizedTextGenerationRequest.from_legacy_request(
            sample_legacy_request.dict()
        )
        
        assert isinstance(optimized, OptimizedTextGenerationRequest)
        assert len(optimized.messages) == 1
        assert optimized.messages[0].content == "Hello, world!"
        assert optimized.model == "gpt-3.5-turbo"
        assert optimized.generation.temperature == 0.7
        assert optimized.generation.max_tokens == 500
        assert not optimized.stream.enabled
    
    def test_optimized_to_legacy_conversion(self, sample_optimized_request):
        """测试优化版本到传统格式的转换"""
        legacy = sample_optimized_request.to_legacy_format()
        
        assert isinstance(legacy, dict)
        assert legacy["model"] == "gpt-4"
        assert legacy["temperature"] == 0.8
        assert legacy["max_tokens"] == 1000
        assert not legacy["stream"]
        assert len(legacy["messages"]) == 2
    
    def test_provider_compatibility_validation(self, sample_optimized_request):
        """测试供应商兼容性验证"""
        # 测试OpenAI兼容性
        openai_validation = sample_optimized_request.validate_for_provider("openai")
        assert len(openai_validation["errors"]) == 0
        assert any("不支持详细安全设置" in warning for warning in openai_validation["warnings"])
        
        # 测试Anthropic兼容性
        anthropic_validation = sample_optimized_request.validate_for_provider("anthropic")
        assert len(anthropic_validation["errors"]) == 0  # max_tokens已设置
        assert len(anthropic_validation["warnings"]) > 0  # 应该有不支持功能的警告
    
    def test_provider_specific_params_openai(self, sample_optimized_request):
        """测试OpenAI供应商特定参数生成"""
        params = sample_optimized_request._get_openai_params()
        
        assert params["model"] == "gpt-4"
        assert params["temperature"] == 0.8
        assert params["max_tokens"] == 1000
        assert params["top_p"] == 0.9
        assert not params["stream"]
        assert len(params["messages"]) == 2
        
        # 检查消息格式
        assert params["messages"][0]["role"] == "system"
        assert params["messages"][1]["role"] == "user"
    
    def test_provider_specific_params_anthropic(self, sample_optimized_request):
        """测试Anthropic供应商特定参数生成"""
        params = sample_optimized_request._get_anthropic_params()
        
        assert params["model"] == "gpt-4"
        assert params["temperature"] == 0.8
        assert params["max_tokens"] == 1000
        assert "system" in params  # system消息应该被分离
        assert len(params["messages"]) == 1  # 只有user消息
        assert not params["stream"]
    
    def test_provider_specific_params_google(self, sample_optimized_request):
        """测试Google AI供应商特定参数生成"""
        params = sample_optimized_request._get_google_params()
        
        assert "contents" in params
        assert "systemInstruction" in params
        assert "generationConfig" in params
        assert "safetySettings" in params
        
        # 检查生成配置
        gen_config = params["generationConfig"]
        assert gen_config["temperature"] == 0.8
        assert gen_config["maxOutputTokens"] == 1000
        assert gen_config["topP"] == 0.9
        
        # 检查安全设置
        safety_settings = params["safetySettings"]
        assert len(safety_settings) == 4  # 四个安全类别
    
    def test_provider_specific_params_dashscope(self, sample_optimized_request):
        """测试DashScope供应商特定参数生成"""
        params = sample_optimized_request._get_dashscope_params()
        
        assert params["model"] == "gpt-4"
        assert "input" in params
        assert "parameters" in params
        
        # 检查输入格式
        assert len(params["input"]["messages"]) == 2
        
        # 检查参数格式
        parameters = params["parameters"]
        assert parameters["temperature"] == 0.8
        assert parameters["max_tokens"] == 1000
        assert parameters["top_p"] == 0.9
        assert parameters["result_format"] == "message"
    
    @pytest.mark.asyncio
    async def test_text_service_optimized_method(self, sample_optimized_request):
        """测试文本生成服务的优化版本方法"""
        # 创建模拟的文本生成服务
        with patch('ai_gen_hub.services.text_generation.TextGenerationService') as MockService:
            service = MockService.return_value
            
            # 模拟依赖
            service.router = AsyncMock()
            service.router.route_request = AsyncMock()
            service.router.record_request_start = AsyncMock()
            service.router.record_request_end = AsyncMock()
            
            # 模拟供应商
            mock_provider = AsyncMock()
            mock_provider.name = "openai"
            service.router.route_request.return_value = mock_provider
            
            # 模拟响应
            mock_response = TextGenerationResponse(
                id="test-id",
                object="chat.completion",
                created=**********,
                model="gpt-4",
                choices=[],
                provider="openai",
                request_id="test-request-id",
                processing_time=1.0
            )
            mock_provider.generate_text_optimized = AsyncMock(return_value=mock_response)
            
            # 设置服务属性
            service.features = {"caching": False, "circuit_breaker": False}
            service.logger = MagicMock()
            service._validate_optimized_request = MagicMock()
            service._generate_text_with_provider_optimized = AsyncMock(return_value=mock_response)
            service._process_response_optimized = AsyncMock(return_value=mock_response)
            
            # 调用优化版本方法
            result = await service.generate_text_optimized(sample_optimized_request)
            
            # 验证结果
            assert isinstance(result, TextGenerationResponse)
            assert result.model == "gpt-4"
    
    def test_is_optimized_request_detection(self, sample_optimized_request, sample_legacy_request):
        """测试优化版本请求检测"""
        assert RequestAdapter.is_optimized_request(sample_optimized_request)
        assert not RequestAdapter.is_optimized_request(sample_legacy_request)
        assert not RequestAdapter.is_optimized_request({"test": "dict"})
    
    def test_provider_capabilities_retrieval(self):
        """测试供应商能力信息获取"""
        openai_caps = OptimizedTextGenerationRequest.get_provider_capabilities("openai")
        assert openai_caps["supports_streaming"]
        assert openai_caps["supports_functions"]
        assert openai_caps["max_tokens_limit"] == 128000
        
        anthropic_caps = OptimizedTextGenerationRequest.get_provider_capabilities("anthropic")
        assert anthropic_caps["supports_streaming"]
        assert not anthropic_caps["supports_functions"]
        assert anthropic_caps["max_tokens_limit"] == 200000
        
        unknown_caps = OptimizedTextGenerationRequest.get_provider_capabilities("unknown")
        assert unknown_caps == {}
    
    def test_edge_cases_and_error_handling(self):
        """测试边界情况和错误处理"""
        # 测试空消息列表
        with pytest.raises(Exception):
            OptimizedTextGenerationRequest(
                messages=[],
                model="gpt-4"
            )
        
        # 测试无效供应商
        request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="test")],
            model="gpt-4"
        )
        
        with pytest.raises(ValueError):
            request.get_provider_specific_params("invalid_provider")
        
        # 测试不支持的请求类型
        with pytest.raises(ValueError):
            RequestAdapter.adapt_request("invalid_request")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
