"""
AI Gen Hub Redis缓存实现

提供基于Redis的分布式缓存功能，包括：
- Redis连接管理
- 序列化和压缩
- 批量操作
- 统计和监控
"""

import asyncio
import gzip
import json
import time
from typing import Any, List, Optional

import aioredis
from aioredis import Redis

from ai_gen_hub.cache.base import BaseCacheManager, CacheBackend, CacheStats
from ai_gen_hub.config.settings import RedisConfig
from ai_gen_hub.core.exceptions import CacheConnectionError


class RedisCache(BaseCacheManager):
    """Redis缓存实现"""
    
    def __init__(
        self,
        redis_config: RedisConfig,
        default_ttl: int = 3600,
        key_prefix: str = "ai_gen_hub",
        compression_enabled: bool = True,
        compression_threshold: int = 1024  # 1KB
    ):
        """初始化Redis缓存
        
        Args:
            redis_config: Redis配置
            default_ttl: 默认TTL（秒）
            key_prefix: 键前缀
            compression_enabled: 是否启用压缩
            compression_threshold: 压缩阈值（字节）
        """
        super().__init__(CacheBackend.REDIS, default_ttl, key_prefix)
        
        self.redis_config = redis_config
        self.compression_enabled = compression_enabled
        self.compression_threshold = compression_threshold
        
        self._redis: Optional[Redis] = None
        self._connection_lock = asyncio.Lock()
        
        # 统计信息
        self._local_stats = CacheStats()
    
    async def _get_redis(self) -> Redis:
        """获取Redis连接"""
        if self._redis is None:
            async with self._connection_lock:
                if self._redis is None:
                    await self._connect()
        
        return self._redis
    
    async def _connect(self) -> None:
        """连接到Redis"""
        try:
            self._redis = aioredis.from_url(
                self.redis_config.url,
                password=self.redis_config.password,
                db=self.redis_config.db,
                decode_responses=False,  # 我们需要处理字节数据
                max_connections=self.redis_config.pool_size,
                socket_timeout=self.redis_config.pool_timeout,
                socket_connect_timeout=self.redis_config.pool_timeout,
            )
            
            # 测试连接
            await self._redis.ping()
            
            self.logger.info(
                "Redis连接成功",
                url=self.redis_config.url,
                db=self.redis_config.db
            )
            
        except Exception as e:
            self.logger.error("Redis连接失败", error=str(e))
            raise CacheConnectionError(f"Redis连接失败: {e}")
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            redis = await self._get_redis()
            
            # 获取原始数据
            raw_data = await redis.get(self._make_key(key))
            
            if raw_data is None:
                self._local_stats.misses += 1
                return None
            
            # 反序列化
            value = self._deserialize(raw_data)
            self._local_stats.hits += 1
            
            return value
            
        except Exception as e:
            self.logger.error("Redis获取失败", key=key, error=str(e))
            self._local_stats.misses += 1
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        try:
            redis = await self._get_redis()
            
            # 序列化
            serialized_data = self._serialize(value)
            
            # 设置TTL
            expire_time = ttl or self.default_ttl
            
            # 存储到Redis
            result = await redis.setex(
                self._make_key(key),
                expire_time,
                serialized_data
            )
            
            if result:
                self._local_stats.sets += 1
                return True
            
            return False
            
        except Exception as e:
            self.logger.error("Redis设置失败", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            redis = await self._get_redis()
            
            result = await redis.delete(self._make_key(key))
            
            if result > 0:
                self._local_stats.deletes += 1
                return True
            
            return False
            
        except Exception as e:
            self.logger.error("Redis删除失败", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存键是否存在"""
        try:
            redis = await self._get_redis()
            result = await redis.exists(self._make_key(key))
            return result > 0
            
        except Exception as e:
            self.logger.error("Redis存在检查失败", key=key, error=str(e))
            return False
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        try:
            redis = await self._get_redis()
            
            # 获取所有匹配的键
            pattern = self._make_key("*")
            keys = await redis.keys(pattern)
            
            if keys:
                await redis.delete(*keys)
            
            self._local_stats = CacheStats()
            return True
            
        except Exception as e:
            self.logger.error("Redis清空失败", error=str(e))
            return False
    
    async def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        try:
            redis = await self._get_redis()
            
            # 获取Redis信息
            info = await redis.info("memory")
            
            # 更新统计信息
            self._local_stats.total_size = info.get("used_memory", 0)
            
            return self._local_stats
            
        except Exception as e:
            self.logger.error("获取Redis统计失败", error=str(e))
            return self._local_stats
    
    async def get_size(self) -> int:
        """获取缓存大小"""
        try:
            redis = await self._get_redis()
            
            # 获取所有匹配的键
            pattern = self._make_key("*")
            keys = await redis.keys(pattern)
            
            total_size = 0
            if keys:
                # 批量获取内存使用
                pipe = redis.pipeline()
                for key in keys:
                    pipe.memory_usage(key)
                
                results = await pipe.execute()
                total_size = sum(size for size in results if size is not None)
            
            return total_size
            
        except Exception as e:
            self.logger.error("获取Redis大小失败", error=str(e))
            return 0
    
    async def get_keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的缓存键列表"""
        try:
            redis = await self._get_redis()
            
            # 构建完整模式
            full_pattern = self._make_key(pattern)
            keys = await redis.keys(full_pattern)
            
            # 移除前缀
            prefix_len = len(self._make_key(""))
            return [key.decode('utf-8')[prefix_len:] for key in keys]
            
        except Exception as e:
            self.logger.error("获取Redis键列表失败", pattern=pattern, error=str(e))
            return []
    
    async def delete_pattern(self, pattern: str) -> int:
        """删除匹配模式的缓存键"""
        try:
            redis = await self._get_redis()
            
            # 获取匹配的键
            full_pattern = self._make_key(pattern)
            keys = await redis.keys(full_pattern)
            
            if keys:
                deleted_count = await redis.delete(*keys)
                self._local_stats.deletes += deleted_count
                return deleted_count
            
            return 0
            
        except Exception as e:
            self.logger.error("Redis模式删除失败", pattern=pattern, error=str(e))
            return 0
    
    async def mget(self, keys: List[str]) -> List[Optional[Any]]:
        """批量获取缓存值"""
        try:
            redis = await self._get_redis()
            
            # 构建完整键名
            full_keys = [self._make_key(key) for key in keys]
            
            # 批量获取
            raw_values = await redis.mget(full_keys)
            
            # 反序列化
            results = []
            for raw_value in raw_values:
                if raw_value is None:
                    results.append(None)
                    self._local_stats.misses += 1
                else:
                    try:
                        value = self._deserialize(raw_value)
                        results.append(value)
                        self._local_stats.hits += 1
                    except Exception:
                        results.append(None)
                        self._local_stats.misses += 1
            
            return results
            
        except Exception as e:
            self.logger.error("Redis批量获取失败", keys=keys, error=str(e))
            return [None] * len(keys)
    
    async def mset(
        self,
        mapping: dict,
        ttl: Optional[int] = None
    ) -> bool:
        """批量设置缓存值"""
        try:
            redis = await self._get_redis()
            
            # 序列化所有值
            serialized_mapping = {}
            for key, value in mapping.items():
                full_key = self._make_key(key)
                serialized_value = self._serialize(value)
                serialized_mapping[full_key] = serialized_value
            
            # 批量设置
            pipe = redis.pipeline()
            
            if ttl:
                # 如果有TTL，使用pipeline逐个设置
                for key, value in serialized_mapping.items():
                    pipe.setex(key, ttl or self.default_ttl, value)
            else:
                # 没有TTL，使用mset
                pipe.mset(serialized_mapping)
            
            results = await pipe.execute()
            
            # 更新统计
            success_count = sum(1 for result in results if result)
            self._local_stats.sets += success_count
            
            return success_count == len(mapping)
            
        except Exception as e:
            self.logger.error("Redis批量设置失败", error=str(e))
            return False
    
    def _make_key(self, key: str) -> str:
        """构建完整的缓存键"""
        if key == "*":
            return f"{self.key_generator.prefix}:*"
        return f"{self.key_generator.prefix}:{key}"
    
    def _serialize(self, value: Any) -> bytes:
        """序列化值"""
        # 使用基类的序列化器
        data = self.serializer.serialize(value)
        
        # 如果启用压缩且数据大于阈值，进行压缩
        if (self.compression_enabled and 
            len(data) > self.compression_threshold):
            
            compressed_data = gzip.compress(data)
            
            # 添加压缩标记
            return b"GZIP:" + compressed_data
        
        return data
    
    def _deserialize(self, data: bytes) -> Any:
        """反序列化值"""
        # 检查是否是压缩数据
        if data.startswith(b"GZIP:"):
            # 解压缩
            compressed_data = data[5:]  # 移除"GZIP:"前缀
            data = gzip.decompress(compressed_data)
        
        # 使用基类的序列化器反序列化
        return self.serializer.deserialize(data)
    
    async def get_redis_info(self) -> dict:
        """获取Redis详细信息"""
        try:
            redis = await self._get_redis()
            
            # 获取Redis信息
            info = await redis.info()
            
            # 获取我们的键统计
            pattern = self._make_key("*")
            keys = await redis.keys(pattern)
            
            return {
                "backend": self.backend,
                "redis_version": info.get("redis_version"),
                "used_memory": info.get("used_memory"),
                "used_memory_human": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands_processed": info.get("total_commands_processed"),
                "keyspace_hits": info.get("keyspace_hits"),
                "keyspace_misses": info.get("keyspace_misses"),
                "our_keys_count": len(keys),
                "compression_enabled": self.compression_enabled,
                "compression_threshold": self.compression_threshold,
                "local_stats": self._local_stats,
            }
            
        except Exception as e:
            self.logger.error("获取Redis信息失败", error=str(e))
            return {"error": str(e)}
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self._redis:
            await self._redis.close()
            self._redis = None
        
        self.logger.info("Redis缓存连接已关闭")
