"""
AI Gen Hub 缓存模块

提供多种缓存实现和管理功能，包括：
- 内存缓存
- Redis缓存
- 多级缓存
- 缓存接口和工具
"""

from ai_gen_hub.cache.base import (
    CacheBackend,
    CacheInterface,
    CacheKeyGenerator,
    CacheSerializer,
    CacheStats,
)
from ai_gen_hub.cache.memory_cache import MemoryCache
from ai_gen_hub.cache.multi_level_cache import MultiLevelCache

# 使用兼容的Redis缓存实现，解决aioredis在Python 3.11中的兼容性问题
try:
    from ai_gen_hub.cache.redis_cache_compat import CompatRedisCache as RedisCache
except ImportError:
    # 如果兼容实现不可用，直接设置为None，避免导入原始实现
    # 这样可以避免aioredis的兼容性问题
    RedisCache = None

__all__ = [
    # 基础接口和工具
    "CacheInterface",
    "CacheBackend",
    "CacheStats",
    "CacheKeyGenerator",
    "CacheSerializer",

    # 缓存实现
    "MemoryCache",
    "RedisCache",
    "MultiLevelCache",
]