"""
AI Gen Hub 内存缓存实现

提供基于内存的缓存功能，包括：
- LRU缓存策略
- TTL过期机制
- 内存使用限制
- 统计和监控
"""

import asyncio
import time
from collections import OrderedDict
from dataclasses import dataclass
from typing import Any, List, Optional

from ai_gen_hub.cache.base import BaseCacheManager, CacheBackend, CacheStats


@dataclass
class CacheItem:
    """缓存项"""
    value: Any
    created_at: float
    ttl: Optional[int] = None
    access_count: int = 0
    last_accessed: float = None
    
    def __post_init__(self):
        if self.last_accessed is None:
            self.last_accessed = self.created_at
    
    @property
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    @property
    def age(self) -> float:
        """获取年龄（秒）"""
        return time.time() - self.created_at
    
    def touch(self) -> None:
        """更新访问时间和计数"""
        self.last_accessed = time.time()
        self.access_count += 1


class MemoryCache(BaseCacheManager):
    """内存缓存实现"""
    
    def __init__(
        self,
        max_size: int = 1000,
        default_ttl: int = 3600,
        max_memory: int = 100 * 1024 * 1024,  # 100MB
        cleanup_interval: int = 300,  # 5分钟
        key_prefix: str = "ai_gen_hub"
    ):
        """初始化内存缓存
        
        Args:
            max_size: 最大缓存项数量
            default_ttl: 默认TTL（秒）
            max_memory: 最大内存使用（字节）
            cleanup_interval: 清理间隔（秒）
            key_prefix: 键前缀
        """
        super().__init__(CacheBackend.MEMORY, default_ttl, key_prefix)
        
        self.max_size = max_size
        self.max_memory = max_memory
        self.cleanup_interval = cleanup_interval
        
        # 使用OrderedDict实现LRU
        self._cache: OrderedDict[str, CacheItem] = OrderedDict()
        self._lock = asyncio.Lock()
        
        # 启动清理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self) -> None:
        """启动清理任务"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def _cleanup_loop(self) -> None:
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("缓存清理失败", error=str(e))
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        async with self._lock:
            item = self._cache.get(key)
            
            if item is None:
                self.stats.misses += 1
                return None
            
            if item.is_expired:
                # 过期项，删除并返回None
                del self._cache[key]
                self.stats.misses += 1
                self.stats.evictions += 1
                return None
            
            # 更新访问信息
            item.touch()
            
            # 移动到末尾（LRU）
            self._cache.move_to_end(key)
            
            self.stats.hits += 1
            return item.value
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        async with self._lock:
            try:
                # 检查内存限制
                value_size = self.serializer.get_size(value)
                if value_size > self.max_memory:
                    self.logger.warning(
                        "缓存值过大，拒绝缓存",
                        key=key,
                        size=value_size,
                        max_memory=self.max_memory
                    )
                    return False
                
                # 创建缓存项
                item = CacheItem(
                    value=value,
                    created_at=time.time(),
                    ttl=ttl or self.default_ttl
                )
                
                # 如果键已存在，更新
                if key in self._cache:
                    self._cache[key] = item
                    self._cache.move_to_end(key)
                else:
                    # 新键，检查大小限制
                    await self._ensure_capacity()
                    self._cache[key] = item
                
                self.stats.sets += 1
                return True
                
            except Exception as e:
                self.logger.error("设置缓存失败", key=key, error=str(e))
                return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
                self.stats.deletes += 1
                return True
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存键是否存在"""
        async with self._lock:
            item = self._cache.get(key)
            if item is None:
                return False
            
            if item.is_expired:
                del self._cache[key]
                self.stats.evictions += 1
                return False
            
            return True
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        async with self._lock:
            self._cache.clear()
            self.stats = CacheStats()
            return True
    
    async def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        async with self._lock:
            self.stats.total_size = await self._calculate_total_size()
            return self.stats
    
    async def get_size(self) -> int:
        """获取缓存大小"""
        return await self._calculate_total_size()
    
    async def _calculate_total_size(self) -> int:
        """计算总大小"""
        total_size = 0
        for item in self._cache.values():
            total_size += self.serializer.get_size(item.value)
        return total_size
    
    async def _ensure_capacity(self) -> None:
        """确保缓存容量"""
        # 检查数量限制
        while len(self._cache) >= self.max_size:
            # 删除最旧的项（LRU）
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]
            self.stats.evictions += 1
        
        # 检查内存限制
        current_size = await self._calculate_total_size()
        while current_size > self.max_memory and self._cache:
            # 删除最旧的项
            oldest_key = next(iter(self._cache))
            oldest_item = self._cache[oldest_key]
            item_size = self.serializer.get_size(oldest_item.value)
            
            del self._cache[oldest_key]
            current_size -= item_size
            self.stats.evictions += 1
    
    async def _cleanup_expired(self) -> None:
        """清理过期项"""
        async with self._lock:
            current_time = time.time()
            expired_keys = []
            
            for key, item in self._cache.items():
                if item.is_expired:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                self.stats.evictions += 1
            
            if expired_keys:
                self.logger.debug(
                    "清理过期缓存项",
                    count=len(expired_keys)
                )
    
    async def get_keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的缓存键列表"""
        async with self._lock:
            if pattern == "*":
                return list(self._cache.keys())
            
            # 简单的模式匹配
            import fnmatch
            return [
                key for key in self._cache.keys()
                if fnmatch.fnmatch(key, pattern)
            ]
    
    async def delete_pattern(self, pattern: str) -> int:
        """删除匹配模式的缓存键"""
        keys_to_delete = await self.get_keys(pattern)
        
        async with self._lock:
            deleted_count = 0
            for key in keys_to_delete:
                if key in self._cache:
                    del self._cache[key]
                    deleted_count += 1
            
            self.stats.deletes += deleted_count
            return deleted_count
    
    async def get_cache_info(self) -> dict:
        """获取缓存详细信息"""
        async with self._lock:
            current_time = time.time()
            
            # 统计信息
            total_items = len(self._cache)
            expired_items = sum(1 for item in self._cache.values() if item.is_expired)
            total_size = await self._calculate_total_size()
            
            # 访问统计
            access_counts = [item.access_count for item in self._cache.values()]
            avg_access_count = sum(access_counts) / len(access_counts) if access_counts else 0
            
            # 年龄统计
            ages = [item.age for item in self._cache.values()]
            avg_age = sum(ages) / len(ages) if ages else 0
            
            return {
                "backend": self.backend,
                "total_items": total_items,
                "expired_items": expired_items,
                "total_size": total_size,
                "max_size": self.max_size,
                "max_memory": self.max_memory,
                "memory_usage_ratio": total_size / self.max_memory if self.max_memory > 0 else 0,
                "capacity_usage_ratio": total_items / self.max_size if self.max_size > 0 else 0,
                "avg_access_count": avg_access_count,
                "avg_age": avg_age,
                "stats": await self.get_stats(),
            }
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self.clear()
        self.logger.info("内存缓存已清理")
