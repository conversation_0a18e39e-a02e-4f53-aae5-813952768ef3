"""
AI Gen Hub 多级缓存实现

提供多级缓存功能，结合内存缓存和Redis缓存的优势：
- L1缓存：内存缓存（快速访问）
- L2缓存：Redis缓存（持久化和分布式）
- 智能缓存策略
- 缓存一致性保证
"""

from typing import Any, List, Optional

from ai_gen_hub.cache.base import BaseCacheManager, CacheBackend, CacheStats
from ai_gen_hub.cache.memory_cache import MemoryCache
from ai_gen_hub.config.settings import CacheConfig, RedisConfig

# 使用兼容的Redis缓存实现，避免aioredis在Python 3.11中的兼容性问题
try:
    from ai_gen_hub.cache.redis_cache_compat import CompatRedisCache as RedisCache
except ImportError:
    # 如果兼容实现不可用，直接设置为None，不再尝试导入原始实现
    # 这样可以避免aioredis的兼容性问题
    RedisCache = None


class MultiLevelCache(BaseCacheManager):
    """多级缓存实现"""
    
    def __init__(
        self,
        cache_config: CacheConfig,
        redis_config: RedisConfig,
        key_prefix: str = "ai_gen_hub"
    ):
        """初始化多级缓存
        
        Args:
            cache_config: 缓存配置
            redis_config: Redis配置
            key_prefix: 键前缀
        """
        super().__init__(CacheBackend.MULTI_LEVEL, cache_config.redis_cache_ttl, key_prefix)
        
        self.cache_config = cache_config
        
        # L1缓存：内存缓存
        self.l1_cache: Optional[MemoryCache] = None
        if cache_config.enable_memory_cache:
            self.l1_cache = MemoryCache(
                max_size=cache_config.memory_cache_size,
                default_ttl=cache_config.memory_cache_ttl,
                key_prefix=key_prefix
            )
        
        # L2缓存：Redis缓存
        self.l2_cache: Optional[RedisCache] = None
        if cache_config.enable_redis_cache and RedisCache is not None:
            try:
                self.l2_cache = RedisCache(
                    redis_config=redis_config,
                    default_ttl=cache_config.redis_cache_ttl,
                    key_prefix=key_prefix,
                    compression_enabled=cache_config.cache_compression
                )
            except Exception as e:
                self.logger.warning("Redis缓存初始化失败，将仅使用内存缓存", error=str(e))
                self.l2_cache = None
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        # 首先尝试L1缓存
        if self.l1_cache:
            value = await self.l1_cache.get(key)
            if value is not None:
                self.logger.debug("L1缓存命中", key=key)
                return value
        
        # L1缓存未命中，尝试L2缓存
        if self.l2_cache:
            value = await self.l2_cache.get(key)
            if value is not None:
                self.logger.debug("L2缓存命中", key=key)
                
                # 将值回填到L1缓存
                if self.l1_cache:
                    await self.l1_cache.set(
                        key, 
                        value, 
                        self.cache_config.memory_cache_ttl
                    )
                
                return value
        
        # 所有缓存都未命中
        self.logger.debug("缓存未命中", key=key)
        return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        success = True
        
        # 设置L1缓存
        if self.l1_cache:
            l1_ttl = min(ttl or self.cache_config.memory_cache_ttl, 
                        self.cache_config.memory_cache_ttl)
            l1_success = await self.l1_cache.set(key, value, l1_ttl)
            if l1_success:
                self.logger.debug("L1缓存设置成功", key=key)
            else:
                self.logger.warning("L1缓存设置失败", key=key)
                success = False
        
        # 设置L2缓存
        if self.l2_cache:
            l2_ttl = ttl or self.cache_config.redis_cache_ttl
            l2_success = await self.l2_cache.set(key, value, l2_ttl)
            if l2_success:
                self.logger.debug("L2缓存设置成功", key=key)
            else:
                self.logger.warning("L2缓存设置失败", key=key)
                success = False
        
        return success
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        success = True
        
        # 删除L1缓存
        if self.l1_cache:
            l1_success = await self.l1_cache.delete(key)
            if not l1_success:
                success = False
        
        # 删除L2缓存
        if self.l2_cache:
            l2_success = await self.l2_cache.delete(key)
            if not l2_success:
                success = False
        
        return success
    
    async def exists(self, key: str) -> bool:
        """检查缓存键是否存在"""
        # 检查L1缓存
        if self.l1_cache and await self.l1_cache.exists(key):
            return True
        
        # 检查L2缓存
        if self.l2_cache and await self.l2_cache.exists(key):
            return True
        
        return False
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        success = True
        
        # 清空L1缓存
        if self.l1_cache:
            l1_success = await self.l1_cache.clear()
            if not l1_success:
                success = False
        
        # 清空L2缓存
        if self.l2_cache:
            l2_success = await self.l2_cache.clear()
            if not l2_success:
                success = False
        
        return success
    
    async def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        combined_stats = CacheStats()
        
        # 合并L1缓存统计
        if self.l1_cache:
            l1_stats = await self.l1_cache.get_stats()
            combined_stats.hits += l1_stats.hits
            combined_stats.misses += l1_stats.misses
            combined_stats.sets += l1_stats.sets
            combined_stats.deletes += l1_stats.deletes
            combined_stats.evictions += l1_stats.evictions
            combined_stats.total_size += l1_stats.total_size
        
        # 合并L2缓存统计
        if self.l2_cache:
            l2_stats = await self.l2_cache.get_stats()
            combined_stats.hits += l2_stats.hits
            combined_stats.misses += l2_stats.misses
            combined_stats.sets += l2_stats.sets
            combined_stats.deletes += l2_stats.deletes
            combined_stats.evictions += l2_stats.evictions
            combined_stats.total_size += l2_stats.total_size
        
        return combined_stats
    
    async def get_size(self) -> int:
        """获取缓存大小"""
        total_size = 0
        
        if self.l1_cache:
            total_size += await self.l1_cache.get_size()
        
        if self.l2_cache:
            total_size += await self.l2_cache.get_size()
        
        return total_size
    
    async def get_keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的缓存键列表"""
        keys = set()
        
        # 从L1缓存获取键
        if self.l1_cache:
            l1_keys = await self.l1_cache.get_keys(pattern)
            keys.update(l1_keys)
        
        # 从L2缓存获取键
        if self.l2_cache:
            l2_keys = await self.l2_cache.get_keys(pattern)
            keys.update(l2_keys)
        
        return list(keys)
    
    async def delete_pattern(self, pattern: str) -> int:
        """删除匹配模式的缓存键"""
        total_deleted = 0
        
        # 从L1缓存删除
        if self.l1_cache:
            l1_deleted = await self.l1_cache.delete_pattern(pattern)
            total_deleted += l1_deleted
        
        # 从L2缓存删除
        if self.l2_cache:
            l2_deleted = await self.l2_cache.delete_pattern(pattern)
            total_deleted += l2_deleted
        
        return total_deleted
    
    async def invalidate_l1(self, key: str) -> bool:
        """使L1缓存中的特定键失效"""
        if self.l1_cache:
            return await self.l1_cache.delete(key)
        return True
    
    async def warm_up_l1(self, keys: List[str]) -> int:
        """预热L1缓存"""
        if not self.l1_cache or not self.l2_cache:
            return 0
        
        warmed_count = 0
        
        for key in keys:
            # 检查L1缓存中是否已存在
            if await self.l1_cache.exists(key):
                continue
            
            # 从L2缓存获取值
            value = await self.l2_cache.get(key)
            if value is not None:
                # 设置到L1缓存
                success = await self.l1_cache.set(
                    key, 
                    value, 
                    self.cache_config.memory_cache_ttl
                )
                if success:
                    warmed_count += 1
        
        self.logger.info("L1缓存预热完成", warmed_count=warmed_count, total_keys=len(keys))
        return warmed_count
    
    async def get_detailed_stats(self) -> dict:
        """获取详细的缓存统计信息"""
        stats = {
            "backend": self.backend,
            "l1_enabled": self.l1_cache is not None,
            "l2_enabled": self.l2_cache is not None,
        }
        
        # L1缓存统计
        if self.l1_cache:
            if hasattr(self.l1_cache, 'get_cache_info'):
                stats["l1_cache"] = await self.l1_cache.get_cache_info()
            else:
                stats["l1_cache"] = {
                    "stats": await self.l1_cache.get_stats(),
                    "size": await self.l1_cache.get_size()
                }
        
        # L2缓存统计
        if self.l2_cache:
            if hasattr(self.l2_cache, 'get_redis_info'):
                stats["l2_cache"] = await self.l2_cache.get_redis_info()
            else:
                stats["l2_cache"] = {
                    "stats": await self.l2_cache.get_stats(),
                    "size": await self.l2_cache.get_size()
                }
        
        # 组合统计
        stats["combined"] = await self.get_stats()
        
        return stats
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self.l1_cache:
            await self.l1_cache.cleanup()
        
        if self.l2_cache:
            await self.l2_cache.cleanup()
        
        self.logger.info("多级缓存已清理")
