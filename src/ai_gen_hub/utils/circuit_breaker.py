"""
AI Gen Hub 熔断器

实现熔断器模式，防止级联失败，包括：
- 三种状态：关闭、打开、半开
- 失败率阈值检测
- 自动恢复机制
- 监控和统计
"""

import asyncio
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, Optional

from ai_gen_hub.core.exceptions import ProviderUnavailableError
from ai_gen_hub.core.logging import LoggerMixin


class CircuitBreakerState(str, Enum):
    """熔断器状态"""
    CLOSED = "closed"  # 关闭状态，正常工作
    OPEN = "open"  # 打开状态，拒绝请求
    HALF_OPEN = "half_open"  # 半开状态，试探性恢复


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5  # 失败次数阈值
    failure_rate_threshold: float = 0.5  # 失败率阈值（0-1）
    recovery_timeout: float = 60.0  # 恢复超时时间（秒）
    expected_exception: type = Exception  # 预期的异常类型
    success_threshold: int = 3  # 半开状态下成功次数阈值
    timeout: float = 30.0  # 请求超时时间
    
    # 统计窗口配置
    window_size: int = 100  # 统计窗口大小
    minimum_requests: int = 10  # 最小请求数（低于此数不触发熔断）


@dataclass
class CircuitBreakerStats:
    """熔断器统计信息"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    consecutive_failures: int = 0
    consecutive_successes: int = 0
    last_failure_time: Optional[float] = None
    last_success_time: Optional[float] = None
    state_change_time: float = field(default_factory=time.time)
    
    # 滑动窗口统计
    recent_requests: list = field(default_factory=list)  # (timestamp, success)
    
    @property
    def failure_rate(self) -> float:
        """计算失败率"""
        if self.total_requests == 0:
            return 0.0
        return self.failed_requests / self.total_requests
    
    @property
    def recent_failure_rate(self) -> float:
        """计算最近的失败率"""
        if not self.recent_requests:
            return 0.0
        
        failures = sum(1 for _, success in self.recent_requests if not success)
        return failures / len(self.recent_requests)


class CircuitBreaker(LoggerMixin):
    """熔断器实现"""
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        """初始化熔断器
        
        Args:
            name: 熔断器名称
            config: 熔断器配置
        """
        self.name = name
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.stats = CircuitBreakerStats()
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """通过熔断器调用函数
        
        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            ProviderUnavailableError: 熔断器打开时
            原函数的异常: 函数执行失败时
        """
        async with self._lock:
            # 检查熔断器状态
            await self._check_state()
            
            if self.state == CircuitBreakerState.OPEN:
                raise ProviderUnavailableError(
                    self.name,
                    f"熔断器已打开，拒绝请求"
                )
        
        # 执行函数
        start_time = time.time()
        try:
            # 设置超时
            if asyncio.iscoroutinefunction(func):
                result = await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=self.config.timeout
                )
            else:
                result = func(*args, **kwargs)
            
            # 记录成功
            await self._record_success()
            return result
            
        except Exception as e:
            # 记录失败
            await self._record_failure(e)
            raise
    
    async def _check_state(self) -> None:
        """检查并更新熔断器状态"""
        current_time = time.time()
        
        if self.state == CircuitBreakerState.OPEN:
            # 检查是否可以进入半开状态
            if current_time - self.stats.state_change_time >= self.config.recovery_timeout:
                await self._change_state(CircuitBreakerState.HALF_OPEN)
                
        elif self.state == CircuitBreakerState.HALF_OPEN:
            # 半开状态下，如果连续成功次数达到阈值，关闭熔断器
            if self.stats.consecutive_successes >= self.config.success_threshold:
                await self._change_state(CircuitBreakerState.CLOSED)
        
        elif self.state == CircuitBreakerState.CLOSED:
            # 检查是否需要打开熔断器
            if self._should_open():
                await self._change_state(CircuitBreakerState.OPEN)
    
    def _should_open(self) -> bool:
        """判断是否应该打开熔断器"""
        # 检查最小请求数
        if self.stats.total_requests < self.config.minimum_requests:
            return False
        
        # 检查连续失败次数
        if self.stats.consecutive_failures >= self.config.failure_threshold:
            return True
        
        # 检查失败率
        if self.stats.recent_failure_rate >= self.config.failure_rate_threshold:
            return True
        
        return False
    
    async def _change_state(self, new_state: CircuitBreakerState) -> None:
        """改变熔断器状态"""
        old_state = self.state
        self.state = new_state
        self.stats.state_change_time = time.time()
        
        # 重置相关计数器
        if new_state == CircuitBreakerState.CLOSED:
            self.stats.consecutive_failures = 0
            self.stats.consecutive_successes = 0
        elif new_state == CircuitBreakerState.HALF_OPEN:
            self.stats.consecutive_successes = 0
        
        self.logger.info(
            "熔断器状态变更",
            name=self.name,
            old_state=old_state,
            new_state=new_state,
            total_requests=self.stats.total_requests,
            failure_rate=self.stats.failure_rate,
            consecutive_failures=self.stats.consecutive_failures
        )
    
    async def _record_success(self) -> None:
        """记录成功请求"""
        async with self._lock:
            current_time = time.time()
            
            self.stats.total_requests += 1
            self.stats.successful_requests += 1
            self.stats.consecutive_successes += 1
            self.stats.consecutive_failures = 0
            self.stats.last_success_time = current_time
            
            # 更新滑动窗口
            self._update_sliding_window(current_time, True)
    
    async def _record_failure(self, exception: Exception) -> None:
        """记录失败请求"""
        async with self._lock:
            current_time = time.time()
            
            # 只有预期的异常才计入失败
            if isinstance(exception, self.config.expected_exception):
                self.stats.total_requests += 1
                self.stats.failed_requests += 1
                self.stats.consecutive_failures += 1
                self.stats.consecutive_successes = 0
                self.stats.last_failure_time = current_time
                
                # 更新滑动窗口
                self._update_sliding_window(current_time, False)
                
                # 在半开状态下，任何失败都会重新打开熔断器
                if self.state == CircuitBreakerState.HALF_OPEN:
                    await self._change_state(CircuitBreakerState.OPEN)
    
    def _update_sliding_window(self, timestamp: float, success: bool) -> None:
        """更新滑动窗口统计"""
        self.stats.recent_requests.append((timestamp, success))
        
        # 保持窗口大小
        if len(self.stats.recent_requests) > self.config.window_size:
            self.stats.recent_requests.pop(0)
        
        # 清理过期数据（超过5分钟的数据）
        cutoff_time = timestamp - 300  # 5分钟
        self.stats.recent_requests = [
            (t, s) for t, s in self.stats.recent_requests
            if t > cutoff_time
        ]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取熔断器统计信息"""
        return {
            "name": self.name,
            "state": self.state,
            "total_requests": self.stats.total_requests,
            "successful_requests": self.stats.successful_requests,
            "failed_requests": self.stats.failed_requests,
            "failure_rate": self.stats.failure_rate,
            "recent_failure_rate": self.stats.recent_failure_rate,
            "consecutive_failures": self.stats.consecutive_failures,
            "consecutive_successes": self.stats.consecutive_successes,
            "last_failure_time": self.stats.last_failure_time,
            "last_success_time": self.stats.last_success_time,
            "state_change_time": self.stats.state_change_time,
            "config": {
                "failure_threshold": self.config.failure_threshold,
                "failure_rate_threshold": self.config.failure_rate_threshold,
                "recovery_timeout": self.config.recovery_timeout,
                "success_threshold": self.config.success_threshold,
                "timeout": self.config.timeout,
            }
        }
    
    async def reset(self) -> None:
        """重置熔断器"""
        async with self._lock:
            self.state = CircuitBreakerState.CLOSED
            self.stats = CircuitBreakerStats()
            
            self.logger.info("熔断器已重置", name=self.name)
    
    async def force_open(self) -> None:
        """强制打开熔断器"""
        async with self._lock:
            await self._change_state(CircuitBreakerState.OPEN)
    
    async def force_close(self) -> None:
        """强制关闭熔断器"""
        async with self._lock:
            await self._change_state(CircuitBreakerState.CLOSED)


class CircuitBreakerManager(LoggerMixin):
    """熔断器管理器"""
    
    def __init__(self):
        """初始化熔断器管理器"""
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
    
    def get_circuit_breaker(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ) -> CircuitBreaker:
        """获取或创建熔断器
        
        Args:
            name: 熔断器名称
            config: 熔断器配置
            
        Returns:
            熔断器实例
        """
        if name not in self.circuit_breakers:
            if config is None:
                config = CircuitBreakerConfig()
            
            self.circuit_breakers[name] = CircuitBreaker(name, config)
            self.logger.info("创建熔断器", name=name)
        
        return self.circuit_breakers[name]
    
    def remove_circuit_breaker(self, name: str) -> None:
        """移除熔断器"""
        if name in self.circuit_breakers:
            del self.circuit_breakers[name]
            self.logger.info("移除熔断器", name=name)
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有熔断器的统计信息"""
        return {
            name: breaker.get_stats()
            for name, breaker in self.circuit_breakers.items()
        }
    
    async def reset_all(self) -> None:
        """重置所有熔断器"""
        for breaker in self.circuit_breakers.values():
            await breaker.reset()
        
        self.logger.info("所有熔断器已重置")


# 全局熔断器管理器实例
circuit_breaker_manager = CircuitBreakerManager()
