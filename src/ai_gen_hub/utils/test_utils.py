"""
AI Gen Hub 通用测试工具模块

提供统一的测试功能，消除测试脚本中的重复代码，
包括环境设置、应用创建、配置管理等通用功能。
"""

import os
import sys
import time
import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
def setup_project_path():
    """设置项目路径，确保可以导入项目模块"""
    project_root = Path(__file__).parent.parent.parent.parent
    src_path = project_root / "src"
    
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    return project_root, src_path


def load_env_file(project_root: Optional[Path] = None):
    """手动加载 .env 文件
    
    Args:
        project_root: 项目根目录路径
    """
    if project_root is None:
        project_root = Path(__file__).parent.parent.parent.parent
    
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()


def setup_test_environment(debug: bool = True, load_env: bool = True):
    """设置测试环境
    
    Args:
        debug: 是否启用调试模式
        load_env: 是否加载.env文件
        
    Returns:
        项目根目录路径
    """
    # 设置项目路径
    project_root, src_path = setup_project_path()
    
    # 设置基础环境变量
    os.environ.setdefault('ENVIRONMENT', 'development')
    os.environ.setdefault('DEBUG', 'true' if debug else 'false')
    
    # 加载.env文件
    if load_env:
        load_env_file(project_root)
    
    return project_root


class TestResult:
    """测试结果类"""
    
    def __init__(self, name: str):
        self.name = name
        self.success = False
        self.error: Optional[str] = None
        self.duration: float = 0.0
        self.details: Dict[str, Any] = {}
    
    def set_success(self, details: Optional[Dict[str, Any]] = None):
        """设置测试成功"""
        self.success = True
        if details:
            self.details.update(details)
    
    def set_failure(self, error: str, details: Optional[Dict[str, Any]] = None):
        """设置测试失败"""
        self.success = False
        self.error = error
        if details:
            self.details.update(details)
    
    def __str__(self):
        status = "✅ 通过" if self.success else "❌ 失败"
        duration_str = f" ({self.duration:.3f}秒)" if self.duration > 0 else ""
        return f"{self.name}: {status}{duration_str}"


class TestRunner:
    """测试运行器"""
    
    def __init__(self, name: str = "测试"):
        self.name = name
        self.results: List[TestResult] = []
        self.start_time = 0.0
    
    def start(self):
        """开始测试"""
        self.start_time = time.time()
        print(f"🚀 开始{self.name}...")
        print("=" * 60)
    
    def run_test(self, test_name: str, test_func, *args, **kwargs) -> TestResult:
        """运行单个测试
        
        Args:
            test_name: 测试名称
            test_func: 测试函数
            *args: 测试函数参数
            **kwargs: 测试函数关键字参数
            
        Returns:
            测试结果
        """
        result = TestResult(test_name)
        
        print(f"\n🔍 运行测试: {test_name}")
        
        start_time = time.time()
        try:
            if asyncio.iscoroutinefunction(test_func):
                # 异步测试函数
                test_result = asyncio.run(test_func(*args, **kwargs))
            else:
                # 同步测试函数
                test_result = test_func(*args, **kwargs)
            
            result.duration = time.time() - start_time
            
            if isinstance(test_result, bool):
                if test_result:
                    result.set_success()
                else:
                    result.set_failure("测试函数返回False")
            elif isinstance(test_result, dict):
                if test_result.get('success', False):
                    result.set_success(test_result)
                else:
                    result.set_failure(test_result.get('error', '未知错误'), test_result)
            else:
                result.set_success({'result': test_result})
            
        except Exception as e:
            result.duration = time.time() - start_time
            result.set_failure(str(e))
            print(f"   ❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()
        
        self.results.append(result)
        print(f"   {result}")
        
        return result
    
    def finish(self) -> bool:
        """完成测试并输出结果
        
        Returns:
            是否所有测试都通过
        """
        total_duration = time.time() - self.start_time
        
        print("\n" + "=" * 60)
        print(f"📊 {self.name}结果摘要:")
        
        passed = sum(1 for r in self.results if r.success)
        failed = len(self.results) - passed
        
        print(f"   总测试数: {len(self.results)}")
        print(f"   通过: {passed}")
        print(f"   失败: {failed}")
        print(f"   总耗时: {total_duration:.3f}秒")
        
        if failed > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.results:
                if not result.success:
                    print(f"   - {result.name}: {result.error}")
        
        all_passed = failed == 0
        
        print(f"\n🎯 总体结果: {'🎉 全部通过!' if all_passed else '💥 部分失败'}")
        
        return all_passed


class ConfigTestUtils:
    """配置测试工具类"""
    
    @staticmethod
    def test_config_loading(debug_logging: bool = True) -> Dict[str, Any]:
        """测试配置加载
        
        Args:
            debug_logging: 是否启用调试日志
            
        Returns:
            测试结果
        """
        try:
            from ai_gen_hub.config import get_enhanced_settings
            
            settings = get_enhanced_settings(debug_logging=debug_logging)
            
            return {
                'success': True,
                'settings': settings,
                'environment': settings.environment,
                'debug': settings.debug,
                'enabled_providers': settings.get_enabled_providers()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def test_provider_configs() -> Dict[str, Any]:
        """测试AI供应商配置
        
        Returns:
            测试结果
        """
        try:
            from ai_gen_hub.config import get_enhanced_settings
            
            settings = get_enhanced_settings()
            validation_result = settings.validate_all_configs()
            
            return {
                'success': validation_result['valid'],
                'validation_result': validation_result,
                'enabled_providers': settings.get_enabled_providers(),
                'warnings_count': len(validation_result['warnings']),
                'errors_count': len(validation_result['errors'])
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


class AppTestUtils:
    """应用测试工具类"""
    
    @staticmethod
    def create_test_app():
        """创建测试应用
        
        Returns:
            FastAPI应用实例
        """
        from ai_gen_hub.api.app import create_app
        return create_app()
    
    @staticmethod
    async def create_test_app_async():
        """创建异步测试应用
        
        Returns:
            FastAPI应用实例和应用实例
        """
        from ai_gen_hub.api.app import AIGenHubApp
        
        app_instance = AIGenHubApp()
        await app_instance.initialize()
        
        app = app_instance.create_app()
        
        # 设置应用状态
        app.state.settings = app_instance.settings
        app.state.key_manager = app_instance.key_manager
        app.state.provider_manager = app_instance.provider_manager
        app.state.router = app_instance.router
        app.state.cache = app_instance.cache
        app.state.health_manager = app_instance.health_manager
        app.state.text_service = app_instance.text_service
        app.state.image_service = app_instance.image_service
        
        return app, app_instance
    
    @staticmethod
    def test_endpoint(app, endpoint: str, method: str = "GET", **kwargs) -> Dict[str, Any]:
        """测试API端点
        
        Args:
            app: FastAPI应用
            endpoint: 端点路径
            method: HTTP方法
            **kwargs: 请求参数
            
        Returns:
            测试结果
        """
        try:
            from fastapi.testclient import TestClient
            
            with TestClient(app) as client:
                start_time = time.time()
                
                if method.upper() == "GET":
                    response = client.get(endpoint, **kwargs)
                elif method.upper() == "POST":
                    response = client.post(endpoint, **kwargs)
                elif method.upper() == "PUT":
                    response = client.put(endpoint, **kwargs)
                elif method.upper() == "DELETE":
                    response = client.delete(endpoint, **kwargs)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                duration = time.time() - start_time
                
                return {
                    'success': response.status_code < 400,
                    'status_code': response.status_code,
                    'duration': duration,
                    'response_data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                    'headers': dict(response.headers)
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


def run_common_tests(test_name: str = "通用功能测试") -> bool:
    """运行通用测试
    
    Args:
        test_name: 测试名称
        
    Returns:
        是否所有测试都通过
    """
    # 设置测试环境
    setup_test_environment()
    
    runner = TestRunner(test_name)
    runner.start()
    
    # 测试配置加载
    runner.run_test("配置加载测试", ConfigTestUtils.test_config_loading)
    
    # 测试供应商配置
    runner.run_test("AI供应商配置测试", ConfigTestUtils.test_provider_configs)
    
    # 测试应用创建
    runner.run_test("应用创建测试", AppTestUtils.create_test_app)
    
    return runner.finish()


if __name__ == "__main__":
    """直接运行时执行通用测试"""
    success = run_common_tests()
    sys.exit(0 if success else 1)
