"""
AI Gen Hub 工具模块

提供各种实用工具和辅助功能，包括：
- 密钥池管理
- 密钥管理器
- 重试机制
- 熔断器模式
- 通用工具函数
"""

from ai_gen_hub.utils.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitBreakerManager,
    CircuitBreakerState,
    circuit_breaker_manager,
)
from ai_gen_hub.utils.key_manager import KeyManager
from ai_gen_hub.utils.key_pool import (
    APIKey,
    KeyMetrics,
    KeyPool,
    KeySelectionStrategy,
    KeyStatus,
)
from ai_gen_hub.utils.retry import (
    AGGRESSIVE_RETRY_CONFIG,
    CONSERVATIVE_RETRY_CONFIG,
    DEFAULT_RETRY_CONFIG,
    RATE_LIMIT_RETRY_CONFIG,
    RetryConfig,
    RetryManager,
    RetryStrategy,
    retry,
    retry_with_config,
)

__all__ = [
    # 密钥管理
    "KeyManager",
    "KeyPool",
    "APIKey",
    "KeyMetrics",
    "KeyStatus",
    "KeySelectionStrategy",

    # 重试机制
    "RetryManager",
    "RetryConfig",
    "RetryStrategy",
    "retry",
    "retry_with_config",
    "DEFAULT_RETRY_CONFIG",
    "AGGRESSIVE_RETRY_CONFIG",
    "CONSERVATIVE_RETRY_CONFIG",
    "RATE_LIMIT_RETRY_CONFIG",

    # 熔断器
    "CircuitBreaker",
    "CircuitBreakerConfig",
    "CircuitBreakerManager",
    "CircuitBreakerState",
    "circuit_breaker_manager",
]