"""
AI Gen Hub 密钥池管理

提供智能的API密钥管理功能，包括：
- 密钥轮询和负载均衡
- 故障转移和健康检查
- 使用统计和监控
- 动态权重调整
"""

import asyncio
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Set
from uuid import uuid4

from ai_gen_hub.core.exceptions import (
    AuthenticationError,
    ProviderUnavailableError,
    QuotaExceededError,
    RateLimitError,
)
from ai_gen_hub.core.logging import LoggerMixin


class KeyStatus(str, Enum):
    """密钥状态枚举"""
    ACTIVE = "active"  # 活跃状态
    RATE_LIMITED = "rate_limited"  # 速率限制
    QUOTA_EXCEEDED = "quota_exceeded"  # 配额超限
    INVALID = "invalid"  # 无效密钥
    DISABLED = "disabled"  # 已禁用


@dataclass
class KeyMetrics:
    """密钥使用指标"""
    total_requests: int = 0  # 总请求数
    successful_requests: int = 0  # 成功请求数
    failed_requests: int = 0  # 失败请求数
    rate_limit_errors: int = 0  # 速率限制错误数
    quota_exceeded_errors: int = 0  # 配额超限错误数
    auth_errors: int = 0  # 认证错误数
    last_used: Optional[datetime] = None  # 最后使用时间
    last_success: Optional[datetime] = None  # 最后成功时间
    last_error: Optional[datetime] = None  # 最后错误时间
    average_response_time: float = 0.0  # 平均响应时间
    current_weight: float = 1.0  # 当前权重
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def error_rate(self) -> float:
        """错误率"""
        return 1.0 - self.success_rate


@dataclass
class APIKey:
    """API密钥信息"""
    key: str  # 密钥值
    provider: str  # 供应商名称
    status: KeyStatus = KeyStatus.ACTIVE  # 密钥状态
    weight: float = 1.0  # 权重
    rate_limit: Optional[int] = None  # 速率限制（请求/分钟）
    quota_limit: Optional[int] = None  # 配额限制
    created_at: datetime = field(default_factory=datetime.now)  # 创建时间
    disabled_until: Optional[datetime] = None  # 禁用截止时间
    metrics: KeyMetrics = field(default_factory=KeyMetrics)  # 使用指标
    
    @property
    def is_available(self) -> bool:
        """检查密钥是否可用"""
        if self.status == KeyStatus.DISABLED:
            return False
        
        if self.disabled_until and datetime.now() < self.disabled_until:
            return False
        
        return self.status == KeyStatus.ACTIVE
    
    @property
    def masked_key(self) -> str:
        """获取掩码后的密钥"""
        if len(self.key) <= 8:
            return "*" * len(self.key)
        return self.key[:4] + "*" * (len(self.key) - 8) + self.key[-4:]


class KeySelectionStrategy(str, Enum):
    """密钥选择策略"""
    ROUND_ROBIN = "round_robin"  # 轮询
    WEIGHTED_RANDOM = "weighted_random"  # 加权随机
    LEAST_USED = "least_used"  # 最少使用
    BEST_PERFORMANCE = "best_performance"  # 最佳性能


class KeyPool(LoggerMixin):
    """密钥池管理器
    
    负责管理单个供应商的所有API密钥，提供智能的密钥选择和故障转移功能。
    """
    
    def __init__(
        self,
        provider: str,
        keys: List[str],
        selection_strategy: KeySelectionStrategy = KeySelectionStrategy.WEIGHTED_RANDOM,
        health_check_interval: int = 300,  # 5分钟
        auto_recovery_enabled: bool = True
    ):
        """初始化密钥池
        
        Args:
            provider: 供应商名称
            keys: API密钥列表
            selection_strategy: 密钥选择策略
            health_check_interval: 健康检查间隔（秒）
            auto_recovery_enabled: 是否启用自动恢复
        """
        self.provider = provider
        self.selection_strategy = selection_strategy
        self.health_check_interval = health_check_interval
        self.auto_recovery_enabled = auto_recovery_enabled
        
        # 初始化密钥
        self.keys: Dict[str, APIKey] = {}
        for key in keys:
            key_id = str(uuid4())
            self.keys[key_id] = APIKey(key=key, provider=provider)
        
        # 轮询索引
        self._round_robin_index = 0
        
        # 请求历史（用于速率限制检查）
        self._request_history: Dict[str, deque] = defaultdict(deque)
        
        # 健康检查任务
        self._health_check_task: Optional[asyncio.Task] = None
        
        self.logger.info(f"初始化密钥池", provider=provider, key_count=len(keys))
    
    async def start(self) -> None:
        """启动密钥池"""
        if self.auto_recovery_enabled:
            self._health_check_task = asyncio.create_task(self._health_check_loop())
        self.logger.info("密钥池已启动", provider=self.provider)
    
    async def stop(self) -> None:
        """停止密钥池"""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        self.logger.info("密钥池已停止", provider=self.provider)
    
    async def get_key(self) -> Optional[APIKey]:
        """获取可用的API密钥
        
        Returns:
            可用的API密钥，如果没有可用密钥则返回None
        """
        available_keys = [key for key in self.keys.values() if key.is_available]
        
        if not available_keys:
            self.logger.warning("没有可用的API密钥", provider=self.provider)
            return None
        
        # 根据策略选择密钥
        if self.selection_strategy == KeySelectionStrategy.ROUND_ROBIN:
            selected_key = self._select_round_robin(available_keys)
        elif self.selection_strategy == KeySelectionStrategy.WEIGHTED_RANDOM:
            selected_key = self._select_weighted_random(available_keys)
        elif self.selection_strategy == KeySelectionStrategy.LEAST_USED:
            selected_key = self._select_least_used(available_keys)
        elif self.selection_strategy == KeySelectionStrategy.BEST_PERFORMANCE:
            selected_key = self._select_best_performance(available_keys)
        else:
            selected_key = available_keys[0]
        
        # 检查速率限制
        if not self._check_rate_limit(selected_key):
            # 如果当前密钥被速率限制，尝试其他密钥
            other_keys = [k for k in available_keys if k != selected_key]
            for key in other_keys:
                if self._check_rate_limit(key):
                    selected_key = key
                    break
            else:
                # 所有密钥都被速率限制
                self.logger.warning("所有密钥都被速率限制", provider=self.provider)
                return None
        
        self.logger.debug(
            "选择API密钥",
            provider=self.provider,
            key_id=selected_key.masked_key,
            strategy=self.selection_strategy
        )
        
        return selected_key
    
    def _select_round_robin(self, available_keys: List[APIKey]) -> APIKey:
        """轮询选择密钥"""
        selected_key = available_keys[self._round_robin_index % len(available_keys)]
        self._round_robin_index += 1
        return selected_key
    
    def _select_weighted_random(self, available_keys: List[APIKey]) -> APIKey:
        """加权随机选择密钥"""
        import random
        
        weights = [key.metrics.current_weight for key in available_keys]
        total_weight = sum(weights)
        
        if total_weight == 0:
            return random.choice(available_keys)
        
        r = random.uniform(0, total_weight)
        cumulative_weight = 0
        
        for key, weight in zip(available_keys, weights):
            cumulative_weight += weight
            if r <= cumulative_weight:
                return key
        
        return available_keys[-1]
    
    def _select_least_used(self, available_keys: List[APIKey]) -> APIKey:
        """选择使用次数最少的密钥"""
        return min(available_keys, key=lambda k: k.metrics.total_requests)
    
    def _select_best_performance(self, available_keys: List[APIKey]) -> APIKey:
        """选择性能最佳的密钥"""
        def performance_score(key: APIKey) -> float:
            # 综合考虑成功率和响应时间
            success_rate = key.metrics.success_rate
            response_time = key.metrics.average_response_time or 1.0
            return success_rate / response_time
        
        return max(available_keys, key=performance_score)
    
    def _check_rate_limit(self, key: APIKey) -> bool:
        """检查密钥是否超过速率限制"""
        if not key.rate_limit:
            return True
        
        now = time.time()
        window_start = now - 60  # 1分钟窗口
        
        # 清理过期的请求记录
        key_history = self._request_history[key.key]
        while key_history and key_history[0] < window_start:
            key_history.popleft()
        
        # 检查是否超过限制
        return len(key_history) < key.rate_limit
    
    async def record_request(
        self,
        key: APIKey,
        success: bool,
        response_time: float,
        error: Optional[Exception] = None
    ) -> None:
        """记录请求结果
        
        Args:
            key: 使用的API密钥
            success: 是否成功
            response_time: 响应时间
            error: 错误信息
        """
        now = datetime.now()
        metrics = key.metrics
        
        # 更新基础指标
        metrics.total_requests += 1
        metrics.last_used = now
        
        if success:
            metrics.successful_requests += 1
            metrics.last_success = now
        else:
            metrics.failed_requests += 1
            metrics.last_error = now
        
        # 更新平均响应时间
        if metrics.total_requests == 1:
            metrics.average_response_time = response_time
        else:
            # 使用指数移动平均
            alpha = 0.1
            metrics.average_response_time = (
                alpha * response_time + (1 - alpha) * metrics.average_response_time
            )
        
        # 记录请求时间（用于速率限制）
        self._request_history[key.key].append(time.time())
        
        # 处理错误
        if error:
            await self._handle_error(key, error)
        
        # 更新权重
        self._update_key_weight(key)
        
        self.logger.debug(
            "记录请求结果",
            provider=self.provider,
            key_id=key.masked_key,
            success=success,
            response_time=response_time,
            error_type=type(error).__name__ if error else None
        )
    
    async def _handle_error(self, key: APIKey, error: Exception) -> None:
        """处理错误"""
        if isinstance(error, RateLimitError):
            key.metrics.rate_limit_errors += 1
            key.status = KeyStatus.RATE_LIMITED
            # 临时禁用密钥
            key.disabled_until = datetime.now() + timedelta(minutes=5)
            
        elif isinstance(error, QuotaExceededError):
            key.metrics.quota_exceeded_errors += 1
            key.status = KeyStatus.QUOTA_EXCEEDED
            # 禁用密钥直到下个计费周期
            key.disabled_until = datetime.now() + timedelta(hours=24)
            
        elif isinstance(error, AuthenticationError):
            key.metrics.auth_errors += 1
            key.status = KeyStatus.INVALID
            # 永久禁用无效密钥
            key.disabled_until = None
            
        self.logger.warning(
            "密钥错误",
            provider=self.provider,
            key_id=key.masked_key,
            error_type=type(error).__name__,
            status=key.status
        )
    
    def _update_key_weight(self, key: APIKey) -> None:
        """更新密钥权重"""
        metrics = key.metrics
        
        # 基于成功率和响应时间调整权重
        success_rate = metrics.success_rate
        response_time = metrics.average_response_time or 1.0
        
        # 权重计算：成功率越高权重越大，响应时间越短权重越大
        base_weight = key.weight
        performance_factor = success_rate / max(response_time, 0.1)
        
        # 应用指数衰减，避免权重变化过于剧烈
        alpha = 0.1
        new_weight = alpha * performance_factor + (1 - alpha) * metrics.current_weight
        
        metrics.current_weight = max(0.1, min(2.0, new_weight))  # 限制权重范围
    
    async def _health_check_loop(self) -> None:
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("健康检查失败", error=str(e))
    
    async def _perform_health_check(self) -> None:
        """执行健康检查"""
        now = datetime.now()
        
        for key in self.keys.values():
            # 检查是否可以恢复被禁用的密钥
            if key.disabled_until and now >= key.disabled_until:
                key.status = KeyStatus.ACTIVE
                key.disabled_until = None
                self.logger.info(
                    "密钥已恢复",
                    provider=self.provider,
                    key_id=key.masked_key
                )
        
        # 记录健康检查结果
        active_keys = sum(1 for key in self.keys.values() if key.is_available)
        total_keys = len(self.keys)
        
        self.logger.info(
            "健康检查完成",
            provider=self.provider,
            active_keys=active_keys,
            total_keys=total_keys
        )
    
    def get_statistics(self) -> Dict[str, any]:
        """获取密钥池统计信息"""
        total_requests = sum(key.metrics.total_requests for key in self.keys.values())
        successful_requests = sum(key.metrics.successful_requests for key in self.keys.values())
        failed_requests = sum(key.metrics.failed_requests for key in self.keys.values())
        
        active_keys = sum(1 for key in self.keys.values() if key.is_available)
        
        return {
            "provider": self.provider,
            "total_keys": len(self.keys),
            "active_keys": active_keys,
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "success_rate": successful_requests / max(total_requests, 1),
            "selection_strategy": self.selection_strategy,
            "keys": [
                {
                    "key_id": key.masked_key,
                    "status": key.status,
                    "weight": key.metrics.current_weight,
                    "requests": key.metrics.total_requests,
                    "success_rate": key.metrics.success_rate,
                    "avg_response_time": key.metrics.average_response_time,
                }
                for key in self.keys.values()
            ]
        }
