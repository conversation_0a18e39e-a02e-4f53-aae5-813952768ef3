"""
AI Gen Hub 密钥管理器

提供跨供应商的密钥管理功能，包括：
- 多供应商密钥池管理
- 全局负载均衡
- 统一的监控和统计
- 动态配置更新
"""

import asyncio
from typing import Dict, List, Optional

from ai_gen_hub.config.settings import Settings
from ai_gen_hub.core.exceptions import ProviderUnavailableError
from ai_gen_hub.core.logging import LoggerMixin
from ai_gen_hub.utils.key_pool import APIKey, KeyPool, KeySelectionStrategy


class KeyManager(LoggerMixin):
    """密钥管理器
    
    负责管理所有供应商的密钥池，提供统一的密钥获取和管理接口。
    """
    
    def __init__(self, settings: Settings):
        """初始化密钥管理器
        
        Args:
            settings: 应用配置
        """
        self.settings = settings
        self.key_pools: Dict[str, KeyPool] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """初始化密钥管理器"""
        if self._initialized:
            return
        
        # 初始化各供应商的密钥池
        await self._initialize_provider_pools()
        
        # 启动所有密钥池
        for pool in self.key_pools.values():
            await pool.start()
        
        self._initialized = True
        self.logger.info("密钥管理器初始化完成", providers=list(self.key_pools.keys()))
    
    async def cleanup(self) -> None:
        """清理资源"""
        if not self._initialized:
            return
        
        # 停止所有密钥池
        for pool in self.key_pools.values():
            await pool.stop()
        
        self.key_pools.clear()
        self._initialized = False
        self.logger.info("密钥管理器已清理")
    
    async def _initialize_provider_pools(self) -> None:
        """初始化供应商密钥池"""
        # OpenAI
        if self.settings.openai.enabled and self.settings.openai.api_keys:
            pool = KeyPool(
                provider="openai",
                keys=self.settings.openai.api_keys,
                selection_strategy=KeySelectionStrategy.WEIGHTED_RANDOM,
                health_check_interval=self.settings.monitoring.health_check_interval,
                auto_recovery_enabled=True
            )
            self.key_pools["openai"] = pool
        
        # Google AI
        if self.settings.google_ai.enabled and self.settings.google_ai.api_keys:
            pool = KeyPool(
                provider="google_ai",
                keys=self.settings.google_ai.api_keys,
                selection_strategy=KeySelectionStrategy.WEIGHTED_RANDOM,
                health_check_interval=self.settings.monitoring.health_check_interval,
                auto_recovery_enabled=True
            )
            self.key_pools["google_ai"] = pool
        
        # Anthropic
        if self.settings.anthropic.enabled and self.settings.anthropic.api_keys:
            pool = KeyPool(
                provider="anthropic",
                keys=self.settings.anthropic.api_keys,
                selection_strategy=KeySelectionStrategy.WEIGHTED_RANDOM,
                health_check_interval=self.settings.monitoring.health_check_interval,
                auto_recovery_enabled=True
            )
            self.key_pools["anthropic"] = pool
        
        # Azure OpenAI
        if self.settings.azure.enabled and self.settings.azure.api_keys:
            pool = KeyPool(
                provider="azure",
                keys=self.settings.azure.api_keys,
                selection_strategy=KeySelectionStrategy.WEIGHTED_RANDOM,
                health_check_interval=self.settings.monitoring.health_check_interval,
                auto_recovery_enabled=True
            )
            self.key_pools["azure"] = pool
    
    async def get_key(self, provider: str) -> Optional[APIKey]:
        """获取指定供应商的API密钥
        
        Args:
            provider: 供应商名称
            
        Returns:
            API密钥，如果没有可用密钥则返回None
            
        Raises:
            ProviderUnavailableError: 供应商不可用
        """
        if not self._initialized:
            await self.initialize()
        
        pool = self.key_pools.get(provider)
        if not pool:
            raise ProviderUnavailableError(provider, "密钥池未配置")
        
        key = await pool.get_key()
        if not key:
            raise ProviderUnavailableError(provider, "没有可用的API密钥")
        
        return key
    
    async def record_request(
        self,
        provider: str,
        key: APIKey,
        success: bool,
        response_time: float,
        error: Optional[Exception] = None
    ) -> None:
        """记录请求结果
        
        Args:
            provider: 供应商名称
            key: 使用的API密钥
            success: 是否成功
            response_time: 响应时间
            error: 错误信息
        """
        pool = self.key_pools.get(provider)
        if pool:
            await pool.record_request(key, success, response_time, error)
    
    def get_provider_pools(self) -> List[str]:
        """获取所有供应商密钥池列表
        
        Returns:
            供应商名称列表
        """
        return list(self.key_pools.keys())
    
    def has_provider(self, provider: str) -> bool:
        """检查是否有指定供应商的密钥池
        
        Args:
            provider: 供应商名称
            
        Returns:
            是否存在
        """
        return provider in self.key_pools
    
    async def get_provider_statistics(self, provider: str) -> Optional[Dict[str, any]]:
        """获取指定供应商的统计信息
        
        Args:
            provider: 供应商名称
            
        Returns:
            统计信息字典，如果供应商不存在则返回None
        """
        pool = self.key_pools.get(provider)
        if not pool:
            return None
        
        return pool.get_statistics()
    
    async def get_all_statistics(self) -> Dict[str, any]:
        """获取所有供应商的统计信息
        
        Returns:
            包含所有供应商统计信息的字典
        """
        statistics = {
            "total_providers": len(self.key_pools),
            "providers": {}
        }
        
        total_requests = 0
        total_successful = 0
        total_failed = 0
        total_keys = 0
        total_active_keys = 0
        
        for provider, pool in self.key_pools.items():
            provider_stats = pool.get_statistics()
            statistics["providers"][provider] = provider_stats
            
            total_requests += provider_stats["total_requests"]
            total_successful += provider_stats["successful_requests"]
            total_failed += provider_stats["failed_requests"]
            total_keys += provider_stats["total_keys"]
            total_active_keys += provider_stats["active_keys"]
        
        statistics.update({
            "total_keys": total_keys,
            "total_active_keys": total_active_keys,
            "total_requests": total_requests,
            "total_successful": total_successful,
            "total_failed": total_failed,
            "overall_success_rate": total_successful / max(total_requests, 1)
        })
        
        return statistics
    
    async def health_check(self, provider: Optional[str] = None) -> Dict[str, bool]:
        """执行健康检查
        
        Args:
            provider: 指定供应商名称，如果为None则检查所有供应商
            
        Returns:
            健康检查结果字典
        """
        results = {}
        
        if provider:
            # 检查指定供应商
            pool = self.key_pools.get(provider)
            if pool:
                # 检查是否有可用密钥
                key = await pool.get_key()
                results[provider] = key is not None
            else:
                results[provider] = False
        else:
            # 检查所有供应商
            for provider_name, pool in self.key_pools.items():
                key = await pool.get_key()
                results[provider_name] = key is not None
        
        return results
    
    async def reload_configuration(self, settings: Settings) -> None:
        """重新加载配置
        
        Args:
            settings: 新的配置
        """
        self.logger.info("重新加载密钥管理器配置")
        
        # 停止现有的密钥池
        await self.cleanup()
        
        # 更新配置
        self.settings = settings
        
        # 重新初始化
        await self.initialize()
        
        self.logger.info("密钥管理器配置重新加载完成")
    
    async def add_provider_keys(self, provider: str, keys: List[str]) -> None:
        """动态添加供应商密钥
        
        Args:
            provider: 供应商名称
            keys: 新的API密钥列表
        """
        if provider not in self.key_pools:
            # 创建新的密钥池
            pool = KeyPool(
                provider=provider,
                keys=keys,
                selection_strategy=KeySelectionStrategy.WEIGHTED_RANDOM,
                health_check_interval=self.settings.monitoring.health_check_interval,
                auto_recovery_enabled=True
            )
            await pool.start()
            self.key_pools[provider] = pool
            
            self.logger.info(
                "添加新供应商密钥池",
                provider=provider,
                key_count=len(keys)
            )
        else:
            # TODO: 实现向现有密钥池添加密钥的功能
            self.logger.warning(
                "暂不支持向现有密钥池添加密钥",
                provider=provider
            )
    
    async def remove_provider(self, provider: str) -> None:
        """移除供应商密钥池
        
        Args:
            provider: 供应商名称
        """
        pool = self.key_pools.get(provider)
        if pool:
            await pool.stop()
            del self.key_pools[provider]
            
            self.logger.info("移除供应商密钥池", provider=provider)
    
    async def update_provider_strategy(
        self,
        provider: str,
        strategy: KeySelectionStrategy
    ) -> None:
        """更新供应商的密钥选择策略
        
        Args:
            provider: 供应商名称
            strategy: 新的选择策略
        """
        pool = self.key_pools.get(provider)
        if pool:
            pool.selection_strategy = strategy
            self.logger.info(
                "更新供应商密钥选择策略",
                provider=provider,
                strategy=strategy
            )
