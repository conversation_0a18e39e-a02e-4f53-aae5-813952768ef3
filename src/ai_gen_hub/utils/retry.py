"""
AI Gen Hub 重试机制

提供智能的重试策略，包括：
- 指数退避重试
- 自适应重试间隔
- 错误类型分类
- 重试条件判断
"""

import asyncio
import random
import time
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, List, Optional, Type, Union

from ai_gen_hub.core.exceptions import (
    AIGenHubException,
    AuthenticationError,
    AuthorizationError,
    QuotaExceededError,
    RateLimitError,
    TimeoutError,
)
from ai_gen_hub.core.logging import LoggerMixin


class RetryStrategy(str, Enum):
    """重试策略枚举"""
    FIXED_DELAY = "fixed_delay"  # 固定延迟
    EXPONENTIAL_BACKOFF = "exponential_backoff"  # 指数退避
    LINEAR_BACKOFF = "linear_backoff"  # 线性退避
    ADAPTIVE = "adaptive"  # 自适应


@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3  # 最大重试次数
    base_delay: float = 1.0  # 基础延迟时间（秒）
    max_delay: float = 60.0  # 最大延迟时间（秒）
    exponential_base: float = 2.0  # 指数退避的底数
    jitter: bool = True  # 是否添加随机抖动
    jitter_range: float = 0.1  # 抖动范围（比例）
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    
    # 可重试的异常类型
    retryable_exceptions: List[Type[Exception]] = None
    
    # 不可重试的异常类型
    non_retryable_exceptions: List[Type[Exception]] = None
    
    def __post_init__(self):
        if self.retryable_exceptions is None:
            self.retryable_exceptions = [
                TimeoutError,
                RateLimitError,
                ConnectionError,
                OSError,
            ]
        
        if self.non_retryable_exceptions is None:
            self.non_retryable_exceptions = [
                AuthenticationError,
                AuthorizationError,
                QuotaExceededError,
                ValueError,
                TypeError,
            ]


class RetryManager(LoggerMixin):
    """重试管理器"""
    
    def __init__(self, config: RetryConfig):
        """初始化重试管理器
        
        Args:
            config: 重试配置
        """
        self.config = config
    
    async def execute_with_retry(
        self,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """执行函数并在失败时重试
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            最后一次执行的异常
        """
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 成功执行，返回结果
                if attempt > 0:
                    self.logger.info(
                        "重试成功",
                        attempt=attempt + 1,
                        total_attempts=self.config.max_attempts
                    )
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # 检查是否应该重试
                if not self._should_retry(e, attempt):
                    self.logger.warning(
                        "不可重试的错误",
                        error=str(e),
                        error_type=type(e).__name__,
                        attempt=attempt + 1
                    )
                    raise e
                
                # 如果是最后一次尝试，不再重试
                if attempt == self.config.max_attempts - 1:
                    self.logger.error(
                        "重试次数已用完",
                        error=str(e),
                        error_type=type(e).__name__,
                        total_attempts=self.config.max_attempts
                    )
                    break
                
                # 计算延迟时间
                delay = self._calculate_delay(attempt, e)
                
                self.logger.warning(
                    "执行失败，准备重试",
                    error=str(e),
                    error_type=type(e).__name__,
                    attempt=attempt + 1,
                    delay=delay,
                    next_attempt=attempt + 2
                )
                
                # 等待后重试
                await asyncio.sleep(delay)
        
        # 所有重试都失败了，抛出最后一个异常
        if last_exception:
            raise last_exception
    
    def _should_retry(self, exception: Exception, attempt: int) -> bool:
        """判断是否应该重试
        
        Args:
            exception: 发生的异常
            attempt: 当前尝试次数（从0开始）
            
        Returns:
            是否应该重试
        """
        # 检查是否超过最大重试次数
        if attempt >= self.config.max_attempts - 1:
            return False
        
        # 检查异常类型
        exception_type = type(exception)
        
        # 如果在不可重试列表中，不重试
        for non_retryable in self.config.non_retryable_exceptions:
            if issubclass(exception_type, non_retryable):
                return False
        
        # 如果在可重试列表中，重试
        for retryable in self.config.retryable_exceptions:
            if issubclass(exception_type, retryable):
                return True
        
        # 检查AIGenHubException的retryable属性
        if isinstance(exception, AIGenHubException):
            return exception.retryable
        
        # 默认不重试未知异常
        return False
    
    def _calculate_delay(self, attempt: int, exception: Exception) -> float:
        """计算延迟时间
        
        Args:
            attempt: 当前尝试次数（从0开始）
            exception: 发生的异常
            
        Returns:
            延迟时间（秒）
        """
        if self.config.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.config.base_delay
            
        elif self.config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.config.base_delay * (self.config.exponential_base ** attempt)
            
        elif self.config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.config.base_delay * (attempt + 1)
            
        elif self.config.strategy == RetryStrategy.ADAPTIVE:
            delay = self._calculate_adaptive_delay(attempt, exception)
            
        else:
            delay = self.config.base_delay
        
        # 限制最大延迟时间
        delay = min(delay, self.config.max_delay)
        
        # 添加随机抖动
        if self.config.jitter:
            jitter_amount = delay * self.config.jitter_range
            jitter = random.uniform(-jitter_amount, jitter_amount)
            delay = max(0.1, delay + jitter)  # 确保延迟至少0.1秒
        
        return delay
    
    def _calculate_adaptive_delay(self, attempt: int, exception: Exception) -> float:
        """计算自适应延迟时间
        
        Args:
            attempt: 当前尝试次数
            exception: 发生的异常
            
        Returns:
            延迟时间（秒）
        """
        base_delay = self.config.base_delay
        
        # 根据异常类型调整延迟
        if isinstance(exception, RateLimitError):
            # 速率限制错误，使用更长的延迟
            if hasattr(exception, 'retry_after') and exception.retry_after:
                return min(exception.retry_after, self.config.max_delay)
            else:
                return min(base_delay * (3 ** attempt), self.config.max_delay)
                
        elif isinstance(exception, TimeoutError):
            # 超时错误，使用指数退避
            return min(base_delay * (2 ** attempt), self.config.max_delay)
            
        elif isinstance(exception, ConnectionError):
            # 连接错误，使用较短的延迟
            return min(base_delay * (1.5 ** attempt), self.config.max_delay)
            
        else:
            # 其他错误，使用默认指数退避
            return min(base_delay * (2 ** attempt), self.config.max_delay)


def retry_with_config(config: RetryConfig):
    """重试装饰器
    
    Args:
        config: 重试配置
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            retry_manager = RetryManager(config)
            return await retry_manager.execute_with_retry(func, *args, **kwargs)
        
        def sync_wrapper(*args, **kwargs):
            # 对于同步函数，创建一个异步包装器
            async def async_func():
                return func(*args, **kwargs)
            
            retry_manager = RetryManager(config)
            return asyncio.run(retry_manager.execute_with_retry(async_func))
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def retry(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
    retryable_exceptions: Optional[List[Type[Exception]]] = None,
    non_retryable_exceptions: Optional[List[Type[Exception]]] = None
):
    """简化的重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        base_delay: 基础延迟时间
        max_delay: 最大延迟时间
        strategy: 重试策略
        retryable_exceptions: 可重试的异常类型
        non_retryable_exceptions: 不可重试的异常类型
        
    Returns:
        装饰器函数
    """
    config = RetryConfig(
        max_attempts=max_attempts,
        base_delay=base_delay,
        max_delay=max_delay,
        strategy=strategy,
        retryable_exceptions=retryable_exceptions,
        non_retryable_exceptions=non_retryable_exceptions
    )
    
    return retry_with_config(config)


# 预定义的重试配置
DEFAULT_RETRY_CONFIG = RetryConfig()

AGGRESSIVE_RETRY_CONFIG = RetryConfig(
    max_attempts=5,
    base_delay=0.5,
    max_delay=30.0,
    strategy=RetryStrategy.EXPONENTIAL_BACKOFF
)

CONSERVATIVE_RETRY_CONFIG = RetryConfig(
    max_attempts=2,
    base_delay=2.0,
    max_delay=10.0,
    strategy=RetryStrategy.FIXED_DELAY
)

RATE_LIMIT_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=5.0,
    max_delay=120.0,
    strategy=RetryStrategy.ADAPTIVE,
    retryable_exceptions=[RateLimitError, TimeoutError]
)
