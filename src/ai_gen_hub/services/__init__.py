"""
AI Gen Hub 服务模块

提供核心业务服务，包括：
- 供应商管理
- 请求路由
- 文本生成服务
- 图像生成服务
"""

from ai_gen_hub.services.hub import AIGenHub
from ai_gen_hub.services.image_generation import ImageGenerationService
from ai_gen_hub.services.provider_manager import AIProviderManager
from ai_gen_hub.services.router import (
    RequestRouter,
    RoutingRule,
    RoutingStrategy,
)
from ai_gen_hub.services.text_generation import TextGenerationService

__all__ = [
    # 核心服务
    "AIGenHub",

    # 供应商管理
    "AIProviderManager",

    # 请求路由
    "RequestRouter",
    "RoutingStrategy",
    "RoutingRule",

    # 业务服务
    "TextGenerationService",
    "ImageGenerationService",
]