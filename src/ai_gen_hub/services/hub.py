"""
AI Gen Hub 核心服务类

这是整个AI Gen Hub系统的核心服务类，负责协调所有组件的工作，
包括供应商管理、请求路由、缓存、监控等功能。
"""

import asyncio
from typing import Dict, List, Optional, Union

from ai_gen_hub.cache import MultiLevelCache
from ai_gen_hub.config.settings import Settings
from ai_gen_hub.core.exceptions import (
    AIGenHubException,
    ProviderNotFoundError,
    ProviderUnavailableError,
)
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
)
from ai_gen_hub.core.logging import LoggerMixin
from ai_gen_hub.services.image_generation import ImageGenerationService
from ai_gen_hub.services.provider_manager import AIProviderManager
from ai_gen_hub.services.router import RequestRouter
from ai_gen_hub.services.text_generation import TextGenerationService
from ai_gen_hub.utils.key_manager import KeyManager


class AIGenHub(LoggerMixin):
    """AI Gen Hub 核心服务类
    
    这是整个系统的核心协调器，负责：
    - 初始化和管理所有组件
    - 提供统一的API接口
    - 协调各个服务之间的交互
    - 处理系统级的错误和异常
    """
    
    def __init__(self, settings: Settings):
        """初始化AI Gen Hub
        
        Args:
            settings: 系统配置
        """
        super().__init__()
        self.settings = settings
        
        # 核心组件
        self.key_manager: Optional[KeyManager] = None
        self.provider_manager: Optional[AIProviderManager] = None
        self.router: Optional[RequestRouter] = None
        self.cache: Optional[MultiLevelCache] = None
        
        # 业务服务
        self.text_service: Optional[TextGenerationService] = None
        self.image_service: Optional[ImageGenerationService] = None
        
        # 初始化状态
        self._initialized = False
    
    async def initialize(self) -> None:
        """初始化所有组件"""
        if self._initialized:
            return
        
        self.logger.info("开始初始化AI Gen Hub系统")
        
        try:
            # 1. 初始化密钥管理器
            self.key_manager = KeyManager(self.settings)
            await self.key_manager.initialize()
            self.logger.info("密钥管理器初始化完成")
            
            # 2. 初始化供应商管理器
            self.provider_manager = AIProviderManager(self.settings, self.key_manager)
            await self.provider_manager.initialize()
            self.logger.info("供应商管理器初始化完成")
            
            # 3. 初始化请求路由器
            self.router = RequestRouter(self.settings, self.provider_manager)
            self.logger.info("请求路由器初始化完成")
            
            # 4. 初始化缓存（如果启用）
            if self.settings.features.enable_caching:
                try:
                    self.cache = MultiLevelCache(
                        self.settings.cache,
                        self.settings.redis
                    )
                    self.logger.info("缓存系统初始化完成")
                except Exception as e:
                    self.logger.warning("缓存系统初始化失败，将在无缓存模式下运行", error=str(e))
                    self.cache = None
            
            # 5. 初始化业务服务
            self.text_service = TextGenerationService(
                self.settings,
                self.provider_manager,
                self.router,
                self.cache
            )
            
            self.image_service = ImageGenerationService(
                self.settings,
                self.provider_manager,
                self.router,
                self.cache
            )
            
            self.logger.info("业务服务初始化完成")
            
            self._initialized = True
            self.logger.info("AI Gen Hub系统初始化完成")
            
        except Exception as e:
            self.logger.error("AI Gen Hub系统初始化失败", error=str(e))
            await self.cleanup()
            raise AIGenHubException(f"系统初始化失败: {e}")
    
    async def cleanup(self) -> None:
        """清理所有资源"""
        self.logger.info("开始清理AI Gen Hub系统资源")
        
        # 清理业务服务
        if self.text_service:
            try:
                await self.text_service.cleanup()
            except Exception as e:
                self.logger.warning("文本生成服务清理失败", error=str(e))
        
        if self.image_service:
            try:
                await self.image_service.cleanup()
            except Exception as e:
                self.logger.warning("图像生成服务清理失败", error=str(e))
        
        # 清理缓存
        if self.cache:
            try:
                await self.cache.cleanup()
            except Exception as e:
                self.logger.warning("缓存系统清理失败", error=str(e))
        
        # 清理供应商管理器
        if self.provider_manager:
            try:
                await self.provider_manager.cleanup()
            except Exception as e:
                self.logger.warning("供应商管理器清理失败", error=str(e))
        
        # 清理密钥管理器
        if self.key_manager:
            try:
                await self.key_manager.cleanup()
            except Exception as e:
                self.logger.warning("密钥管理器清理失败", error=str(e))
        
        self._initialized = False
        self.logger.info("AI Gen Hub系统资源清理完成")
    
    async def generate_text(
        self,
        request: TextGenerationRequest,
        user_id: Optional[str] = None
    ) -> Union[TextGenerationResponse, TextGenerationStreamChunk]:
        """生成文本
        
        Args:
            request: 文本生成请求
            user_id: 用户ID（可选）
            
        Returns:
            文本生成响应或流式响应
        """
        if not self._initialized:
            raise AIGenHubException("系统未初始化")
        
        if not self.text_service:
            raise AIGenHubException("文本生成服务未初始化")
        
        return await self.text_service.generate(request, user_id)
    
    async def generate_image(
        self,
        request: ImageGenerationRequest,
        user_id: Optional[str] = None
    ) -> ImageGenerationResponse:
        """生成图像
        
        Args:
            request: 图像生成请求
            user_id: 用户ID（可选）
            
        Returns:
            图像生成响应
        """
        if not self._initialized:
            raise AIGenHubException("系统未初始化")
        
        if not self.image_service:
            raise AIGenHubException("图像生成服务未初始化")
        
        return await self.image_service.generate(request, user_id)
    
    async def health_check_all(self) -> Dict[str, bool]:
        """检查所有供应商的健康状态
        
        Returns:
            供应商名称到健康状态的映射
        """
        if not self._initialized or not self.provider_manager:
            raise AIGenHubException("系统未初始化")
        
        return await self.provider_manager.health_check_all()
    
    async def health_check_provider(self, provider_name: str) -> bool:
        """检查特定供应商的健康状态
        
        Args:
            provider_name: 供应商名称
            
        Returns:
            健康状态
        """
        if not self._initialized or not self.provider_manager:
            raise AIGenHubException("系统未初始化")
        
        return await self.provider_manager.health_check_provider(provider_name)
    
    async def get_provider_info(self, provider_name: str) -> Dict:
        """获取供应商信息
        
        Args:
            provider_name: 供应商名称
            
        Returns:
            供应商信息
        """
        if not self._initialized or not self.provider_manager:
            raise AIGenHubException("系统未初始化")
        
        provider = self.provider_manager.get_provider(provider_name)
        if not provider:
            raise ProviderNotFoundError(provider_name)
        
        info = await provider.get_provider_info()
        return info.dict()
    
    async def get_system_status(self) -> Dict:
        """获取系统状态
        
        Returns:
            系统状态信息
        """
        if not self._initialized:
            return {"status": "not_initialized"}
        
        status = {
            "status": "running",
            "initialized": self._initialized,
            "components": {
                "key_manager": self.key_manager is not None,
                "provider_manager": self.provider_manager is not None,
                "router": self.router is not None,
                "cache": self.cache is not None,
                "text_service": self.text_service is not None,
                "image_service": self.image_service is not None,
            }
        }
        
        # 添加供应商状态
        if self.provider_manager:
            try:
                provider_status = await self.provider_manager.health_check_all()
                status["providers"] = provider_status
            except Exception as e:
                status["providers"] = {"error": str(e)}
        
        # 添加缓存状态
        if self.cache:
            try:
                cache_stats = await self.cache.get_stats()
                status["cache"] = cache_stats.dict()
            except Exception as e:
                status["cache"] = {"error": str(e)}
        
        return status
    
    @property
    def is_initialized(self) -> bool:
        """检查系统是否已初始化"""
        return self._initialized
