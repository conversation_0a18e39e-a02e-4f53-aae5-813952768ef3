"""
AI Gen Hub 请求路由器

提供智能的请求路由功能，包括：
- 基于模型类型的路由
- 负载均衡策略
- 供应商健康状态检查
- 动态权重调整
- A/B测试支持
"""

import random
import time
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from ai_gen_hub.config.settings import Settings
from ai_gen_hub.core.exceptions import (
    ModelNotSupportedError,
    ProviderUnavailableError,
)
from ai_gen_hub.core.interfaces import (
    AIProvider,
    ModelType,
    ProviderManager,
    ProviderStatus,
)
from ai_gen_hub.core.logging import LoggerMixin


class RoutingStrategy(str, Enum):
    """路由策略枚举"""
    ROUND_ROBIN = "round_robin"  # 轮询
    WEIGHTED_RANDOM = "weighted_random"  # 加权随机
    LEAST_CONNECTIONS = "least_connections"  # 最少连接
    RESPONSE_TIME = "response_time"  # 响应时间优先
    COST_OPTIMIZED = "cost_optimized"  # 成本优化
    AVAILABILITY_FIRST = "availability_first"  # 可用性优先


@dataclass
class ProviderMetrics:
    """供应商性能指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    active_connections: int = 0
    last_request_time: Optional[float] = None
    last_success_time: Optional[float] = None
    last_failure_time: Optional[float] = None
    
    # 动态权重
    current_weight: float = 1.0
    base_weight: float = 1.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_response_time / self.successful_requests
    
    @property
    def requests_per_second(self) -> float:
        """每秒请求数（最近1分钟）"""
        if not self.last_request_time:
            return 0.0
        
        time_window = 60.0  # 1分钟
        current_time = time.time()
        
        if current_time - self.last_request_time > time_window:
            return 0.0
        
        # 简化计算，实际应该使用滑动窗口
        return self.total_requests / max(current_time - (self.last_request_time - time_window), 1.0)


@dataclass
class RoutingRule:
    """路由规则"""
    model_pattern: str  # 模型名称模式
    provider_preferences: List[str]  # 供应商偏好列表
    weight_multiplier: float = 1.0  # 权重乘数
    max_retries: int = 3  # 最大重试次数
    timeout_override: Optional[float] = None  # 超时覆盖
    enabled: bool = True  # 是否启用


class RequestRouter(LoggerMixin):
    """请求路由器
    
    负责根据请求特征和供应商状态，智能地选择最合适的供应商来处理请求。
    """
    
    def __init__(
        self,
        settings: Settings,
        provider_manager: ProviderManager,
        default_strategy: RoutingStrategy = RoutingStrategy.WEIGHTED_RANDOM
    ):
        """初始化请求路由器
        
        Args:
            settings: 应用配置
            provider_manager: 供应商管理器
            default_strategy: 默认路由策略
        """
        self.settings = settings
        self.provider_manager = provider_manager
        self.default_strategy = default_strategy
        
        # 供应商性能指标
        self.provider_metrics: Dict[str, ProviderMetrics] = defaultdict(ProviderMetrics)
        
        # 路由规则
        self.routing_rules: List[RoutingRule] = []
        
        # 轮询索引
        self._round_robin_indices: Dict[str, int] = defaultdict(int)
        
        # A/B测试配置
        self.ab_test_config: Dict[str, Any] = {}
        
        self.logger.info("请求路由器初始化完成", strategy=default_strategy)
    
    async def route_request(
        self,
        model_type: ModelType,
        model: Optional[str] = None,
        request_params: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        strategy_override: Optional[RoutingStrategy] = None
    ) -> AIProvider:
        """路由请求到最合适的供应商
        
        Args:
            model_type: 模型类型
            model: 模型名称
            request_params: 请求参数
            user_id: 用户ID（用于A/B测试）
            strategy_override: 策略覆盖
            
        Returns:
            选中的AI供应商
            
        Raises:
            ProviderUnavailableError: 没有可用的供应商
            ModelNotSupportedError: 模型不受支持
        """
        # 获取可用的供应商
        available_providers = await self.provider_manager.get_available_providers(
            model_type, model
        )
        
        if not available_providers:
            raise ProviderUnavailableError(
                "all",
                f"没有支持 {model_type} 的可用供应商"
            )
        
        # 过滤健康的供应商
        healthy_providers = [
            p for p in available_providers
            if p.status == ProviderStatus.HEALTHY
        ]
        
        if not healthy_providers:
            # 如果没有完全健康的供应商，尝试使用降级状态的供应商
            degraded_providers = [
                p for p in available_providers
                if p.status == ProviderStatus.DEGRADED
            ]
            
            if degraded_providers:
                healthy_providers = degraded_providers
                self.logger.warning(
                    "使用降级状态的供应商",
                    providers=[p.name for p in degraded_providers]
                )
            else:
                raise ProviderUnavailableError(
                    "all",
                    "没有健康的供应商可用"
                )
        
        # 应用路由规则
        filtered_providers = self._apply_routing_rules(
            healthy_providers, model, request_params
        )
        
        # 应用A/B测试
        if user_id and self.ab_test_config:
            filtered_providers = self._apply_ab_testing(
                filtered_providers, user_id, model_type
            )
        
        # 选择路由策略
        strategy = strategy_override or self.default_strategy
        
        # 根据策略选择供应商
        selected_provider = await self._select_provider(
            filtered_providers, strategy, model_type, model
        )
        
        self.logger.info(
            "路由请求",
            provider=selected_provider.name,
            model_type=model_type,
            model=model,
            strategy=strategy,
            available_count=len(available_providers),
            healthy_count=len(healthy_providers)
        )
        
        return selected_provider
    
    def _apply_routing_rules(
        self,
        providers: List[AIProvider],
        model: Optional[str],
        request_params: Optional[Dict[str, Any]]
    ) -> List[AIProvider]:
        """应用路由规则"""
        if not self.routing_rules or not model:
            return providers
        
        # 查找匹配的路由规则
        matching_rules = []
        for rule in self.routing_rules:
            if rule.enabled and self._match_model_pattern(model, rule.model_pattern):
                matching_rules.append(rule)
        
        if not matching_rules:
            return providers
        
        # 应用规则偏好
        filtered_providers = []
        for rule in matching_rules:
            for provider_name in rule.provider_preferences:
                for provider in providers:
                    if provider.name == provider_name and provider not in filtered_providers:
                        # 调整权重
                        metrics = self.provider_metrics[provider.name]
                        metrics.current_weight = metrics.base_weight * rule.weight_multiplier
                        filtered_providers.append(provider)
        
        # 如果规则过滤后没有供应商，返回原列表
        return filtered_providers if filtered_providers else providers
    
    def _match_model_pattern(self, model: str, pattern: str) -> bool:
        """匹配模型名称模式"""
        # 简单的模式匹配，支持通配符
        if pattern == "*":
            return True
        
        if "*" in pattern:
            # 简单的通配符匹配
            parts = pattern.split("*")
            if len(parts) == 2:
                prefix, suffix = parts
                return model.startswith(prefix) and model.endswith(suffix)
        
        return model == pattern
    
    def _apply_ab_testing(
        self,
        providers: List[AIProvider],
        user_id: str,
        model_type: ModelType
    ) -> List[AIProvider]:
        """应用A/B测试"""
        # 简单的A/B测试实现
        test_config = self.ab_test_config.get(str(model_type))
        if not test_config:
            return providers
        
        # 基于用户ID的哈希值决定分组
        user_hash = hash(user_id) % 100
        
        for test in test_config.get("tests", []):
            if test.get("enabled", False):
                traffic_split = test.get("traffic_split", {})
                cumulative_percentage = 0
                
                for group, percentage in traffic_split.items():
                    cumulative_percentage += percentage
                    if user_hash < cumulative_percentage:
                        # 用户属于这个分组
                        group_providers = test.get("groups", {}).get(group, [])
                        if group_providers:
                            filtered = [
                                p for p in providers
                                if p.name in group_providers
                            ]
                            if filtered:
                                return filtered
                        break
        
        return providers
    
    async def _select_provider(
        self,
        providers: List[AIProvider],
        strategy: RoutingStrategy,
        model_type: ModelType,
        model: Optional[str]
    ) -> AIProvider:
        """根据策略选择供应商"""
        if len(providers) == 1:
            return providers[0]
        
        if strategy == RoutingStrategy.ROUND_ROBIN:
            return self._select_round_robin(providers, model_type)
        elif strategy == RoutingStrategy.WEIGHTED_RANDOM:
            return self._select_weighted_random(providers)
        elif strategy == RoutingStrategy.LEAST_CONNECTIONS:
            return self._select_least_connections(providers)
        elif strategy == RoutingStrategy.RESPONSE_TIME:
            return self._select_by_response_time(providers)
        elif strategy == RoutingStrategy.COST_OPTIMIZED:
            return self._select_cost_optimized(providers, model)
        elif strategy == RoutingStrategy.AVAILABILITY_FIRST:
            return self._select_availability_first(providers)
        else:
            # 默认使用加权随机
            return self._select_weighted_random(providers)
    
    def _select_round_robin(self, providers: List[AIProvider], model_type: ModelType) -> AIProvider:
        """轮询选择"""
        key = str(model_type)
        index = self._round_robin_indices[key] % len(providers)
        self._round_robin_indices[key] += 1
        return providers[index]
    
    def _select_weighted_random(self, providers: List[AIProvider]) -> AIProvider:
        """加权随机选择"""
        weights = []
        for provider in providers:
            metrics = self.provider_metrics[provider.name]
            # 综合考虑成功率和当前权重
            weight = metrics.current_weight * metrics.success_rate
            weights.append(max(weight, 0.1))  # 最小权重0.1
        
        total_weight = sum(weights)
        if total_weight == 0:
            return random.choice(providers)
        
        r = random.uniform(0, total_weight)
        cumulative_weight = 0
        
        for provider, weight in zip(providers, weights):
            cumulative_weight += weight
            if r <= cumulative_weight:
                return provider
        
        return providers[-1]
    
    def _select_least_connections(self, providers: List[AIProvider]) -> AIProvider:
        """最少连接选择"""
        return min(
            providers,
            key=lambda p: self.provider_metrics[p.name].active_connections
        )
    
    def _select_by_response_time(self, providers: List[AIProvider]) -> AIProvider:
        """响应时间优先选择"""
        def response_time_score(provider: AIProvider) -> float:
            metrics = self.provider_metrics[provider.name]
            avg_time = metrics.average_response_time
            success_rate = metrics.success_rate
            
            if avg_time == 0 or success_rate == 0:
                return float('inf')
            
            # 响应时间越短，成功率越高，分数越低（越好）
            return avg_time / success_rate
        
        return min(providers, key=response_time_score)
    
    def _select_cost_optimized(self, providers: List[AIProvider], model: Optional[str]) -> AIProvider:
        """成本优化选择"""
        # 简化的成本计算，实际应该基于具体的定价模型
        cost_map = {
            "openai": 1.0,
            "google_ai": 0.8,
            "anthropic": 1.2,
            "azure": 0.9,
        }
        
        def cost_score(provider: AIProvider) -> float:
            base_cost = cost_map.get(provider.name, 1.0)
            metrics = self.provider_metrics[provider.name]
            
            # 考虑成功率，失败的请求也有成本
            success_rate = metrics.success_rate
            if success_rate == 0:
                return float('inf')
            
            return base_cost / success_rate
        
        return min(providers, key=cost_score)
    
    def _select_availability_first(self, providers: List[AIProvider]) -> AIProvider:
        """可用性优先选择"""
        def availability_score(provider: AIProvider) -> Tuple[int, float]:
            metrics = self.provider_metrics[provider.name]
            
            # 首先按状态排序，然后按成功率
            status_priority = {
                ProviderStatus.HEALTHY: 0,
                ProviderStatus.DEGRADED: 1,
                ProviderStatus.UNHEALTHY: 2,
                ProviderStatus.MAINTENANCE: 3,
            }
            
            return (
                status_priority.get(provider.status, 99),
                -metrics.success_rate  # 负号表示成功率越高越好
            )
        
        return min(providers, key=availability_score)
    
    async def record_request_start(self, provider_name: str) -> None:
        """记录请求开始"""
        metrics = self.provider_metrics[provider_name]
        metrics.active_connections += 1
        metrics.last_request_time = time.time()
    
    async def record_request_end(
        self,
        provider_name: str,
        success: bool,
        response_time: float,
        error: Optional[Exception] = None
    ) -> None:
        """记录请求结束"""
        metrics = self.provider_metrics[provider_name]
        metrics.active_connections = max(0, metrics.active_connections - 1)
        metrics.total_requests += 1
        
        if success:
            metrics.successful_requests += 1
            metrics.total_response_time += response_time
            metrics.last_success_time = time.time()
        else:
            metrics.failed_requests += 1
            metrics.last_failure_time = time.time()
        
        # 动态调整权重
        self._update_provider_weight(provider_name)
    
    def _update_provider_weight(self, provider_name: str) -> None:
        """动态更新供应商权重"""
        metrics = self.provider_metrics[provider_name]
        
        # 基于成功率和响应时间调整权重
        success_rate = metrics.success_rate
        avg_response_time = metrics.average_response_time
        
        if avg_response_time == 0:
            performance_factor = success_rate
        else:
            # 响应时间越短，性能因子越高
            performance_factor = success_rate / max(avg_response_time, 0.1)
        
        # 使用指数移动平均更新权重
        alpha = 0.1
        new_weight = alpha * performance_factor + (1 - alpha) * metrics.current_weight
        metrics.current_weight = max(0.1, min(2.0, new_weight))  # 限制权重范围
    
    def get_provider_statistics(self) -> Dict[str, Any]:
        """获取供应商统计信息"""
        stats = {}
        for provider_name, metrics in self.provider_metrics.items():
            stats[provider_name] = {
                "total_requests": metrics.total_requests,
                "successful_requests": metrics.successful_requests,
                "failed_requests": metrics.failed_requests,
                "success_rate": metrics.success_rate,
                "average_response_time": metrics.average_response_time,
                "active_connections": metrics.active_connections,
                "current_weight": metrics.current_weight,
                "requests_per_second": metrics.requests_per_second,
            }
        
        return stats
    
    def add_routing_rule(self, rule: RoutingRule) -> None:
        """添加路由规则"""
        self.routing_rules.append(rule)
        self.logger.info(
            "添加路由规则",
            model_pattern=rule.model_pattern,
            providers=rule.provider_preferences
        )
    
    def remove_routing_rule(self, model_pattern: str) -> None:
        """移除路由规则"""
        self.routing_rules = [
            rule for rule in self.routing_rules
            if rule.model_pattern != model_pattern
        ]
        self.logger.info("移除路由规则", model_pattern=model_pattern)
    
    def update_ab_test_config(self, config: Dict[str, Any]) -> None:
        """更新A/B测试配置"""
        self.ab_test_config = config
        self.logger.info("更新A/B测试配置", config=config)
