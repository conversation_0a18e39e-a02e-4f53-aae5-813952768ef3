"""
AI Gen Hub 供应商管理器

负责管理所有AI供应商的生命周期，包括：
- 供应商注册和注销
- 健康检查和状态管理
- 供应商发现和选择
- 负载均衡和故障转移
"""

import asyncio
from typing import Dict, List, Optional

from ai_gen_hub.config.settings import Settings
from ai_gen_hub.core.exceptions import (
    ProviderNotFoundError,
    ProviderUnavailableError,
)
from ai_gen_hub.core.interfaces import (
    AIProvider,
    ModelType,
    ProviderManager,
    ProviderStatus,
)
from ai_gen_hub.core.logging import LoggerMixin
from ai_gen_hub.providers import (
    AnthropicProvider,
    GoogleAIProvider,
    OpenAIProvider,
)
from ai_gen_hub.providers.dashscope_provider import DashScopeProvider
from ai_gen_hub.utils.key_manager import KeyManager


class AIProviderManager(ProviderManager, LoggerMixin):
    """AI供应商管理器实现"""
    
    def __init__(self, settings: Settings, key_manager: KeyManager):
        """初始化供应商管理器
        
        Args:
            settings: 应用配置
            key_manager: 密钥管理器
        """
        self.settings = settings
        self.key_manager = key_manager
        self.providers: Dict[str, AIProvider] = {}
        self._health_check_task: Optional[asyncio.Task] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """初始化供应商管理器"""
        if self._initialized:
            return
        
        # 初始化各个供应商
        await self._initialize_providers()
        
        # 启动健康检查任务
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
        self._initialized = True
        self.logger.info(
            "供应商管理器初始化完成",
            providers=list(self.providers.keys())
        )
    
    async def cleanup(self) -> None:
        """清理资源"""
        if not self._initialized:
            return
        
        # 停止健康检查任务
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 清理所有供应商
        for provider in self.providers.values():
            await provider.cleanup()
        
        self.providers.clear()
        self._initialized = False
        self.logger.info("供应商管理器已清理")
    
    async def _initialize_providers(self) -> None:
        """初始化所有配置的供应商"""
        # OpenAI
        if self.settings.openai.enabled and self.settings.openai.api_keys:
            try:
                provider = OpenAIProvider(self.settings.openai, self.key_manager)
                await provider.initialize()
                self.providers["openai"] = provider
                self.logger.info("OpenAI供应商初始化成功")
            except Exception as e:
                self.logger.error("OpenAI供应商初始化失败", error=str(e))
        
        # Google AI
        if self.settings.google_ai.enabled and self.settings.google_ai.api_keys:
            try:
                provider = GoogleAIProvider(self.settings.google_ai, self.key_manager)
                await provider.initialize()
                self.providers["google_ai"] = provider
                self.logger.info("Google AI供应商初始化成功")
            except Exception as e:
                self.logger.error("Google AI供应商初始化失败", error=str(e))
        
        # Anthropic
        if self.settings.anthropic.enabled and self.settings.anthropic.api_keys:
            try:
                provider = AnthropicProvider(self.settings.anthropic, self.key_manager)
                await provider.initialize()
                self.providers["anthropic"] = provider
                self.logger.info("Anthropic供应商初始化成功")
            except Exception as e:
                self.logger.error("Anthropic供应商初始化失败", error=str(e))

        # DashScope
        if self.settings.dashscope.enabled and self.settings.dashscope.api_keys:
            try:
                provider = DashScopeProvider(self.settings.dashscope, self.key_manager)
                await provider.initialize()
                self.providers["dashscope"] = provider
                self.logger.info("DashScope供应商初始化成功")
            except Exception as e:
                self.logger.error("DashScope供应商初始化失败", error=str(e))

        # TODO: 添加其他供应商（Azure OpenAI等）
    
    async def add_provider(self, provider: AIProvider) -> None:
        """添加供应商"""
        await provider.initialize()
        self.providers[provider.name] = provider
        self.logger.info("添加供应商", provider=provider.name)
    
    async def remove_provider(self, name: str) -> None:
        """移除供应商"""
        provider = self.providers.get(name)
        if provider:
            await provider.cleanup()
            del self.providers[name]
            self.logger.info("移除供应商", provider=name)
        else:
            raise ProviderNotFoundError(name)
    
    async def get_provider(self, name: str) -> Optional[AIProvider]:
        """获取指定供应商"""
        return self.providers.get(name)
    
    async def get_available_providers(
        self,
        model_type: ModelType,
        model: Optional[str] = None
    ) -> List[AIProvider]:
        """获取可用的供应商列表"""
        available_providers = []
        
        for provider in self.providers.values():
            # 检查是否支持模型类型
            if not provider.supports_model_type(model_type):
                continue
            
            # 检查是否支持特定模型
            if model and hasattr(provider, 'supports_model'):
                if not provider.supports_model(model):
                    continue
            
            # 检查供应商状态
            if provider.status in [ProviderStatus.HEALTHY, ProviderStatus.DEGRADED]:
                available_providers.append(provider)
        
        return available_providers
    
    async def select_provider(
        self,
        model_type: ModelType,
        model: Optional[str] = None,
        request_params: Optional[Dict[str, any]] = None
    ) -> Optional[AIProvider]:
        """选择最佳供应商"""
        available_providers = await self.get_available_providers(model_type, model)
        
        if not available_providers:
            return None
        
        # 简单的选择策略：优先选择健康状态的供应商
        healthy_providers = [
            p for p in available_providers
            if p.status == ProviderStatus.HEALTHY
        ]
        
        if healthy_providers:
            # 从健康的供应商中选择第一个
            return healthy_providers[0]
        else:
            # 如果没有完全健康的，选择降级状态的
            return available_providers[0]
    
    async def _health_check_loop(self) -> None:
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.settings.monitoring.health_check_interval)
                await self._perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("健康检查循环出错", error=str(e))
    
    async def _perform_health_checks(self) -> None:
        """执行所有供应商的健康检查"""
        health_check_tasks = []
        
        for provider in self.providers.values():
            task = asyncio.create_task(self._check_provider_health(provider))
            health_check_tasks.append(task)
        
        if health_check_tasks:
            await asyncio.gather(*health_check_tasks, return_exceptions=True)
    
    async def _check_provider_health(self, provider: AIProvider) -> None:
        """检查单个供应商的健康状态"""
        try:
            is_healthy = await provider.health_check()
            
            self.logger.debug(
                "供应商健康检查",
                provider=provider.name,
                healthy=is_healthy,
                status=provider.status
            )
            
        except Exception as e:
            self.logger.error(
                "供应商健康检查失败",
                provider=provider.name,
                error=str(e)
            )
    
    def get_provider_list(self) -> List[str]:
        """获取所有供应商名称列表"""
        return list(self.providers.keys())
    
    def get_provider_count(self) -> int:
        """获取供应商数量"""
        return len(self.providers)
    
    def get_healthy_provider_count(self) -> int:
        """获取健康供应商数量"""
        return sum(
            1 for provider in self.providers.values()
            if provider.status == ProviderStatus.HEALTHY
        )
    
    async def get_provider_statistics(self) -> Dict[str, any]:
        """获取所有供应商的统计信息"""
        statistics = {
            "total_providers": len(self.providers),
            "healthy_providers": self.get_healthy_provider_count(),
            "providers": {}
        }
        
        for name, provider in self.providers.items():
            try:
                provider_info = await provider.get_provider_info()
                statistics["providers"][name] = {
                    "name": provider_info.name,
                    "status": provider_info.status,
                    "models": provider_info.models,
                    "capabilities": [str(cap) for cap in provider_info.capabilities],
                    "rate_limits": provider_info.rate_limits,
                    "last_health_check": provider_info.last_health_check,
                }
            except Exception as e:
                statistics["providers"][name] = {
                    "name": name,
                    "status": "error",
                    "error": str(e)
                }
        
        return statistics
    
    async def health_check_all(self) -> Dict[str, bool]:
        """对所有供应商执行健康检查"""
        results = {}
        
        for name, provider in self.providers.items():
            try:
                is_healthy = await provider.health_check()
                results[name] = is_healthy
            except Exception as e:
                self.logger.error(
                    "供应商健康检查失败",
                    provider=name,
                    error=str(e)
                )
                results[name] = False
        
        return results
    
    async def health_check_provider(self, provider_name: str) -> bool:
        """对指定供应商执行健康检查"""
        provider = self.providers.get(provider_name)
        if not provider:
            raise ProviderNotFoundError(provider_name)
        
        try:
            return await provider.health_check()
        except Exception as e:
            self.logger.error(
                "供应商健康检查失败",
                provider=provider_name,
                error=str(e)
            )
            return False
    
    async def reload_configuration(self, settings: Settings) -> None:
        """重新加载配置"""
        self.logger.info("重新加载供应商管理器配置")
        
        # 保存当前设置
        old_settings = self.settings
        self.settings = settings
        
        try:
            # 清理现有供应商
            await self.cleanup()
            
            # 重新初始化
            await self.initialize()
            
            self.logger.info("供应商管理器配置重新加载完成")
            
        except Exception as e:
            # 如果重新加载失败，恢复旧配置
            self.logger.error("配置重新加载失败，恢复旧配置", error=str(e))
            self.settings = old_settings
            await self.initialize()
            raise
