"""
AI Gen Hub 增强配置管理器

提供优化的配置获取逻辑，优先从系统环境变量获取配置，
回退到 .env 文件，包含详细的调试日志和错误处理。
"""

import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import logging

from ai_gen_hub.config.settings import Settings, ProviderConfig, get_settings


class ConfigManager:
    """增强的配置管理器
    
    功能特性：
    1. 优先从系统环境变量获取配置
    2. 回退到 .env 文件配置
    3. 提供详细的配置获取调试日志
    4. 安全的配置验证和错误处理
    5. 支持配置热重载
    """
    
    def __init__(self, debug_logging: bool = False):
        """初始化配置管理器
        
        Args:
            debug_logging: 是否启用调试日志
        """
        self.debug_logging = debug_logging
        self.logger = logging.getLogger(__name__)
        self._settings: Optional[Settings] = None
        self._config_sources: Dict[str, str] = {}  # 记录配置来源
        
    def get_config(self, config_path: Optional[str] = None, force_reload: bool = False) -> Settings:
        """获取配置实例
        
        Args:
            config_path: 指定的配置文件路径
            force_reload: 是否强制重新加载配置
            
        Returns:
            配置实例
        """
        if self._settings is None or force_reload:
            self._load_config(config_path)
        
        return self._settings
    
    def _load_config(self, config_path: Optional[str] = None) -> None:
        """加载配置
        
        Args:
            config_path: 指定的配置文件路径
        """
        if self.debug_logging:
            self._log_debug("🔧 开始加载配置...")
            self._log_debug(f"   Python版本: {sys.version}")
            self._log_debug(f"   工作目录: {os.getcwd()}")
        
        # 记录环境变量配置来源
        self._record_env_sources()
        
        # 加载配置
        try:
            if config_path and Path(config_path).exists():
                self._log_debug(f"   从指定配置文件加载: {config_path}")
                self._settings = Settings(_env_file=config_path)
            else:
                self._log_debug("   从默认配置源加载（环境变量 + .env文件）")
                self._settings = Settings()
            
            # 验证配置
            self._validate_config()
            
            # 记录配置加载结果
            self._log_config_summary()
            
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            raise
    
    def _record_env_sources(self) -> None:
        """记录环境变量配置来源"""
        # AI供应商相关的环境变量
        ai_provider_vars = [
            'OPENAI_API_KEYS', 'OPENAI_BASE_URL', 'OPENAI_ENABLED',
            'GOOGLE_AI_API_KEYS', 'GOOGLE_AI_BASE_URL', 'GOOGLE_AI_ENABLED',
            'ANTHROPIC_API_KEYS', 'ANTHROPIC_BASE_URL', 'ANTHROPIC_ENABLED',
            'DASHSCOPE_API_KEYS', 'DASHSCOPE_BASE_URL', 'DASHSCOPE_ENABLED',
            'AZURE_API_KEYS', 'AZURE_BASE_URL', 'AZURE_ENABLED',
        ]
        
        # 基础配置环境变量
        basic_vars = [
            'ENVIRONMENT', 'DEBUG', 'API_HOST', 'API_PORT',
            'JWT_SECRET_KEY', 'API_KEY'
        ]
        
        all_vars = ai_provider_vars + basic_vars
        
        for var in all_vars:
            if var in os.environ:
                self._config_sources[var] = "环境变量"
                if self.debug_logging:
                    # 对敏感信息进行脱敏
                    value = os.environ[var]
                    if 'key' in var.lower() or 'secret' in var.lower():
                        display_value = self._mask_sensitive_value(value)
                    else:
                        display_value = value
                    self._log_debug(f"   🌍 {var}={display_value} (来源: 环境变量)")
            else:
                self._config_sources[var] = ".env文件或默认值"
    
    def _mask_sensitive_value(self, value: str) -> str:
        """脱敏处理敏感配置值
        
        Args:
            value: 原始值
            
        Returns:
            脱敏后的值
        """
        if not value:
            return "未设置"
        
        if len(value) <= 8:
            return "*" * len(value)
        
        return value[:4] + "*" * (len(value) - 8) + value[-4:]
    
    def _validate_config(self) -> None:
        """验证配置的有效性"""
        if not self._settings:
            raise ValueError("配置未加载")
        
        # 验证基础配置
        if not self._settings.app_name:
            raise ValueError("应用名称不能为空")
        
        # 验证AI供应商配置
        enabled_providers = self._settings.get_enabled_providers()
        if not enabled_providers:
            self.logger.warning("⚠️ 没有启用的AI供应商，请检查API密钥配置")
        
        if self.debug_logging:
            self._log_debug(f"✅ 配置验证通过，启用的供应商: {enabled_providers}")
    
    def _log_config_summary(self) -> None:
        """记录配置加载摘要"""
        if not self.debug_logging or not self._settings:
            return
        
        self._log_debug("✅ 配置加载完成:")
        self._log_debug(f"   应用名称: {self._settings.app_name}")
        self._log_debug(f"   环境: {self._settings.environment}")
        self._log_debug(f"   调试模式: {self._settings.debug}")
        self._log_debug(f"   API地址: {self._settings.api_host}:{self._settings.api_port}")
        
        # AI供应商配置摘要
        enabled_providers = self._settings.get_enabled_providers()
        self._log_debug(f"   启用的AI供应商: {enabled_providers}")
        
        for provider_name in ['openai', 'google_ai', 'anthropic', 'dashscope', 'azure']:
            config = self._settings.get_provider_config(provider_name)
            if config and config.enabled:
                api_keys_count = len(config.api_keys) if config.api_keys else 0
                self._log_debug(f"     {provider_name}: {api_keys_count}个API密钥")
    
    def _log_debug(self, message: str) -> None:
        """输出调试日志
        
        Args:
            message: 日志消息
        """
        if self.debug_logging:
            print(message)
            self.logger.debug(message)
    
    def get_provider_config_with_source(self, provider_name: str) -> Dict[str, Any]:
        """获取供应商配置及其来源信息
        
        Args:
            provider_name: 供应商名称
            
        Returns:
            包含配置和来源信息的字典
        """
        if not self._settings:
            raise ValueError("配置未加载")
        
        config = self._settings.get_provider_config(provider_name)
        if not config:
            return {"config": None, "sources": {}}
        
        # 构建配置来源信息
        provider_upper = provider_name.upper()
        sources = {}
        
        for key in ['API_KEYS', 'BASE_URL', 'ENABLED', 'TIMEOUT', 'MAX_RETRIES']:
            env_var = f"{provider_upper}_{key}"
            sources[key.lower()] = self._config_sources.get(env_var, ".env文件或默认值")
        
        return {
            "config": config.dict(),
            "sources": sources,
            "enabled": config.enabled,
            "api_keys_count": len(config.api_keys) if config.api_keys else 0
        }
    
    def get_config_sources_summary(self) -> Dict[str, Any]:
        """获取配置来源摘要
        
        Returns:
            配置来源摘要信息
        """
        env_count = sum(1 for source in self._config_sources.values() if source == "环境变量")
        file_count = len(self._config_sources) - env_count
        
        return {
            "total_configs": len(self._config_sources),
            "from_environment": env_count,
            "from_file_or_default": file_count,
            "sources": self._config_sources.copy(),
            "environment_priority": "环境变量优先于.env文件"
        }
    
    def reload_config(self, config_path: Optional[str] = None) -> Settings:
        """重新加载配置
        
        Args:
            config_path: 指定的配置文件路径
            
        Returns:
            新的配置实例
        """
        self._log_debug("🔄 重新加载配置...")
        self._settings = None
        self._config_sources.clear()
        return self.get_config(config_path, force_reload=True)


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager(debug_logging: bool = False) -> ConfigManager:
    """获取全局配置管理器实例
    
    Args:
        debug_logging: 是否启用调试日志
        
    Returns:
        配置管理器实例
    """
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigManager(debug_logging=debug_logging)
    
    return _config_manager


def get_enhanced_settings(config_path: Optional[str] = None, debug_logging: bool = False) -> Settings:
    """获取增强的配置实例（推荐使用）
    
    Args:
        config_path: 指定的配置文件路径
        debug_logging: 是否启用调试日志
        
    Returns:
        配置实例
    """
    manager = get_config_manager(debug_logging=debug_logging)
    return manager.get_config(config_path)
