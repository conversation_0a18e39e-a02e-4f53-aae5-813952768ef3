"""
AI Gen Hub - 高性能AI服务聚合平台

这是一个企业级的AI服务聚合平台，提供以下核心功能：
- 统一的AI服务接口层，支持多个供应商（OpenAI、Google AI、Anthropic等）
- 智能负载均衡和请求路由
- 密钥池管理和故障转移
- 多级缓存系统
- 完整的监控和日志系统
- 错误处理和重试机制
- 流式输出支持

主要模块：
- core: 核心接口和抽象类
- config: 配置管理系统
- providers: AI供应商适配器
- services: 业务服务层
- cache: 缓存系统
- monitoring: 监控和日志
- api: API服务层
"""

__version__ = "0.1.0"
__author__ = "AI Gen Hub Team"
__email__ = "<EMAIL>"
__description__ = "高性能AI服务聚合平台"

# 导出主要的公共接口
from ai_gen_hub.core.interfaces import (
    AIProvider,
    TextGenerationRequest,
    TextGenerationResponse,
    ImageGenerationRequest,
    ImageGenerationResponse,
)
from ai_gen_hub.services.hub import AIGenHub
from ai_gen_hub.config.settings import Settings

__all__ = [
    "AIProvider",
    "TextGenerationRequest", 
    "TextGenerationResponse",
    "ImageGenerationRequest",
    "ImageGenerationResponse", 
    "AIGenHub",
    "Settings",
]
