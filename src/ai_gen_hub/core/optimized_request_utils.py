"""
优化版本请求的工具方法

包含渐进式迁移、供应商兼容性检查和参数映射等功能。
这些方法被设计为OptimizedTextGenerationRequest类的扩展方法。
"""

import logging
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class OptimizedRequestMixin:
    """优化请求的混入类，提供渐进式迁移和供应商兼容性功能"""
    
    @classmethod
    def from_legacy_request(cls, legacy_request: Union[Dict[str, Any], "TextGenerationRequest"]) -> "OptimizedTextGenerationRequest":
        """从传统的TextGenerationRequest格式转换为优化版本
        
        这个方法实现了向后兼容性，允许现有代码逐步迁移到优化版本。
        它会智能地将扁平化的参数重新组织到相应的配置组中。
        
        Args:
            legacy_request: 传统格式的请求参数（字典或TextGenerationRequest对象）
            
        Returns:
            OptimizedTextGenerationRequest: 转换后的优化版本请求
        """
        from .interfaces import GenerationConfig, StreamConfig, SafetyConfig
        
        logger.info("正在将传统请求格式转换为优化版本")
        
        # 如果是TextGenerationRequest对象，转换为字典
        if hasattr(legacy_request, 'dict'):
            legacy_data = legacy_request.dict()
        else:
            legacy_data = legacy_request
        
        # 提取核心必需参数
        messages = legacy_data.get("messages", [])
        model = legacy_data.get("model", "")
        
        # 构建生成配置
        generation_params = {}
        for param in ["max_tokens", "temperature", "top_p", "top_k", 
                     "frequency_penalty", "presence_penalty", "stop"]:
            if param in legacy_data and legacy_data[param] is not None:
                generation_params[param] = legacy_data[param]
        
        generation_config = GenerationConfig(**generation_params)
        
        # 构建流式配置
        stream_enabled = legacy_data.get("stream", False)
        stream_config = StreamConfig(enabled=stream_enabled)
        
        # 构建安全配置（如果有安全相关参数）
        safety_config = None
        if "safety_settings" in legacy_data:
            safety_config = SafetyConfig(
                content_filter=True,
                safety_level="medium"  # 默认安全级别
            )
        
        # 提取高级功能参数
        functions = legacy_data.get("functions")
        function_call = legacy_data.get("function_call")
        tools = legacy_data.get("tools")
        tool_choice = legacy_data.get("tool_choice")
        response_format = legacy_data.get("response_format")
        response_schema = legacy_data.get("response_schema")
        user = legacy_data.get("user")
        
        # 提取供应商特定参数
        provider_params = legacy_data.get("provider_params", {}).copy()
        
        # 添加thinking相关参数到provider_params（如果存在）
        if "thinking_budget" in legacy_data:
            provider_params["thinking_budget"] = legacy_data["thinking_budget"]
        if "thinking_config" in legacy_data:
            provider_params["thinking_config"] = legacy_data["thinking_config"]
        
        # 构建优化版本请求
        return cls(
            messages=messages,
            model=model,
            generation=generation_config,
            stream=stream_config,
            safety=safety_config,
            functions=functions,
            function_call=function_call,
            tools=tools,
            tool_choice=tool_choice,
            response_format=response_format,
            response_schema=response_schema,
            user=user,
            provider_params=provider_params
        )
    
    def to_legacy_format(self) -> Dict[str, Any]:
        """转换为传统的扁平化格式
        
        这个方法确保优化版本可以与现有的供应商适配器兼容，
        在迁移期间提供双向转换能力。
        
        Returns:
            Dict[str, Any]: 传统格式的请求参数
        """
        logger.info("正在将优化版本转换为传统格式")
        
        # 基础参数
        legacy_format = {
            "messages": self.messages,
            "model": self.model,
        }
        
        # 展开生成配置
        if self.generation.max_tokens is not None:
            legacy_format["max_tokens"] = self.generation.max_tokens
        legacy_format["temperature"] = self.generation.temperature
        if self.generation.top_p is not None:
            legacy_format["top_p"] = self.generation.top_p
        if self.generation.top_k is not None:
            legacy_format["top_k"] = self.generation.top_k
        legacy_format["frequency_penalty"] = self.generation.frequency_penalty
        legacy_format["presence_penalty"] = self.generation.presence_penalty
        if self.generation.stop is not None:
            legacy_format["stop"] = self.generation.stop
        
        # 流式配置
        legacy_format["stream"] = self.stream.enabled
        
        # 高级功能参数
        if self.functions is not None:
            legacy_format["functions"] = self.functions
        if self.function_call is not None:
            legacy_format["function_call"] = self.function_call
        if self.tools is not None:
            legacy_format["tools"] = self.tools
        if self.tool_choice is not None:
            legacy_format["tool_choice"] = self.tool_choice
        if self.response_format is not None:
            legacy_format["response_format"] = self.response_format
        if self.response_schema is not None:
            legacy_format["response_schema"] = self.response_schema
        if self.user is not None:
            legacy_format["user"] = self.user
        
        # 安全设置
        if self.safety is not None:
            legacy_format["safety_settings"] = [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": self.safety.safety_level.upper()
                }
            ]
        
        # 供应商特定参数
        legacy_format["provider_params"] = self.provider_params.copy()
        
        return legacy_format
    
    @staticmethod
    def get_provider_capabilities(provider_name: str) -> Dict[str, Any]:
        """获取供应商能力信息
        
        返回指定供应商支持的功能和限制，用于参数验证和智能适配。
        
        Args:
            provider_name: 供应商名称
            
        Returns:
            Dict[str, Any]: 供应商能力信息
        """
        capabilities = {
            "openai": {
                "supports_streaming": True,
                "supports_functions": True,
                "supports_tools": True,
                "supports_structured_output": True,
                "supports_safety_settings": False,
                "supports_thinking": False,
                "max_tokens_limit": 128000,
                "supported_parameters": [
                    "temperature", "top_p", "frequency_penalty", 
                    "presence_penalty", "stop", "max_tokens"
                ],
                "required_parameters": ["model", "messages"],
                "message_roles": ["system", "user", "assistant", "function", "tool"]
            },
            "anthropic": {
                "supports_streaming": True,
                "supports_functions": False,
                "supports_tools": False,
                "supports_structured_output": False,
                "supports_safety_settings": False,
                "supports_thinking": False,
                "max_tokens_limit": 200000,
                "supported_parameters": [
                    "temperature", "top_p", "top_k", "stop_sequences", "max_tokens"
                ],
                "required_parameters": ["model", "messages", "max_tokens"],
                "message_roles": ["system", "user", "assistant"],
                "special_handling": {
                    "system_message": "separate_field",
                    "stop_parameter": "stop_sequences"
                }
            },
            "google": {
                "supports_streaming": True,
                "supports_functions": True,
                "supports_tools": True,
                "supports_structured_output": True,
                "supports_safety_settings": True,
                "supports_thinking": True,
                "max_tokens_limit": 8192,
                "supported_parameters": [
                    "temperature", "top_p", "top_k", "stop_sequences", "max_output_tokens"
                ],
                "required_parameters": ["contents"],
                "message_roles": ["user", "model"],
                "special_handling": {
                    "system_message": "system_instruction",
                    "message_format": "contents_parts",
                    "generation_config": "nested"
                }
            },
            "dashscope": {
                "supports_streaming": True,
                "supports_functions": False,
                "supports_tools": True,
                "supports_structured_output": False,
                "supports_safety_settings": False,
                "supports_thinking": False,
                "max_tokens_limit": 32000,
                "supported_parameters": [
                    "temperature", "top_p", "top_k", "stop", "max_tokens"
                ],
                "required_parameters": ["model", "input"],
                "message_roles": ["system", "user", "assistant"],
                "special_handling": {
                    "message_format": "input_messages",
                    "parameters": "nested"
                }
            }
        }
        
        return capabilities.get(provider_name.lower(), {})
    
    def validate_for_provider(self, provider_name: str) -> Dict[str, List[str]]:
        """验证请求是否与指定供应商兼容
        
        检查请求参数是否符合供应商的能力和限制，返回验证结果。
        
        Args:
            provider_name: 供应商名称
            
        Returns:
            Dict[str, List[str]]: 验证结果，包含errors、warnings、info三个列表
        """
        result = {
            "errors": [],
            "warnings": [],
            "info": []
        }
        
        capabilities = self.get_provider_capabilities(provider_name)
        if not capabilities:
            result["errors"].append(f"不支持的供应商: {provider_name}")
            return result
        
        # 检查token限制
        if self.generation.max_tokens is not None:
            max_limit = capabilities.get("max_tokens_limit", float('inf'))
            if self.generation.max_tokens > max_limit:
                result["warnings"].append(
                    f"max_tokens ({self.generation.max_tokens}) 超过供应商限制 ({max_limit})，将被调整"
                )
        
        # 检查不支持的功能
        if self.functions is not None and not capabilities.get("supports_functions", False):
            result["warnings"].append(f"{provider_name} 不支持函数调用功能，functions参数将被忽略")
        
        if self.tools is not None and not capabilities.get("supports_tools", False):
            result["warnings"].append(f"{provider_name} 不支持工具调用功能，tools参数将被忽略")
        
        if self.response_format is not None and not capabilities.get("supports_structured_output", False):
            result["warnings"].append(f"{provider_name} 不支持结构化输出，response_format参数将被忽略")
        
        if self.safety is not None and not capabilities.get("supports_safety_settings", False):
            result["warnings"].append(f"{provider_name} 不支持详细安全设置，将使用默认安全策略")
        
        # 检查thinking功能
        thinking_params = ["thinking_budget", "thinking_config"]
        has_thinking = any(param in self.provider_params for param in thinking_params)
        if has_thinking and not capabilities.get("supports_thinking", False):
            result["warnings"].append(f"{provider_name} 不支持thinking功能，相关参数将被忽略")
        
        # 检查消息角色
        supported_roles = capabilities.get("message_roles", [])
        for msg in self.messages:
            if msg.role.value not in supported_roles:
                result["warnings"].append(
                    f"{provider_name} 不支持 {msg.role.value} 角色，消息可能被跳过或转换"
                )
        
        # Anthropic特殊检查
        if provider_name.lower() == "anthropic":
            if self.generation.max_tokens is None:
                result["errors"].append("Anthropic要求必须指定max_tokens参数")
        
        # 添加优化建议
        if provider_name.lower() == "google" and self.generation.max_tokens is not None:
            if self.generation.max_tokens > 8192:
                result["info"].append("建议使用Google的thinking功能来处理长文本生成需求")
        
        return result
