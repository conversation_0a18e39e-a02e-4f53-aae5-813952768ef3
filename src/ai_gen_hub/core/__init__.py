"""
AI Gen Hub 核心模块

提供系统的核心接口、异常定义、日志系统等基础功能。
这个模块是整个系统的基础，定义了所有其他模块需要遵循的规范和约定。
"""

from ai_gen_hub.core.exceptions import (
    AIGenHubException,
    APIError,
    AuthenticationError,
    AuthorizationError,
    CacheError,
    ConfigurationError,
    InvalidRequestError,
    ModelNotSupportedError,
    ProviderError,
    ProviderNotFoundError,
    ProviderUnavailableError,
    QuotaExceededError,
    RateLimitError,
    RequestError,
    TimeoutError,
)
from ai_gen_hub.core.interfaces import (
    AIProvider,
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    MessageRole,
    ModelType,
    ProviderInfo,
    ProviderManager,
    ProviderStatus,
    RequestStatus,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
)
from ai_gen_hub.core.logging import (
    LoggerMixin,
    PerformanceLogger,
    RequestLogger,
    add_request_context,
    clear_request_context,
    get_logger,
    setup_logging,
)

__all__ = [
    # 异常类
    "AIGenHubException",
    "APIError",
    "AuthenticationError",
    "AuthorizationError",
    "CacheError",
    "ConfigurationError",
    "InvalidRequestError",
    "ModelNotSupportedError",
    "ProviderError",
    "ProviderNotFoundError",
    "ProviderUnavailableError",
    "QuotaExceededError",
    "RateLimitError",
    "RequestError",
    "TimeoutError",

    # 接口和数据模型
    "AIProvider",
    "ImageGenerationRequest",
    "ImageGenerationResponse",
    "Message",
    "MessageRole",
    "ModelType",
    "ProviderInfo",
    "ProviderManager",
    "ProviderStatus",
    "RequestStatus",
    "TextGenerationRequest",
    "TextGenerationResponse",
    "TextGenerationStreamChunk",
    "Usage",

    # 日志系统
    "LoggerMixin",
    "PerformanceLogger",
    "RequestLogger",
    "add_request_context",
    "clear_request_context",
    "get_logger",
    "setup_logging",
]