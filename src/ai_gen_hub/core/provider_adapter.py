"""
供应商适配器基类

提供统一的供应商适配接口，支持优化版本请求的处理和参数映射。
这个适配器可以被现有的供应商实现继承，以支持优化版本功能。
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, AsyncIterator, Dict, List, Optional, Union

from .interfaces import (
    OptimizedTextGenerationRequest,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    MessageRole
)

logger = logging.getLogger(__name__)


class ProviderAdapterMixin:
    """供应商适配器混入类
    
    这个混入类可以被现有的供应商实现继承，提供优化版本请求的处理能力。
    它实现了渐进式迁移策略，既支持新的优化版本，也保持对传统格式的兼容性。
    """
    
    async def generate_text_optimized(
        self, 
        request: OptimizedTextGenerationRequest
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """生成文本（优化版本接口）
        
        这个方法是新的优化版本接口，提供增强的供应商兼容性和参数验证。
        
        Args:
            request: 优化版本的文本生成请求
            
        Returns:
            文本生成响应或流式响应迭代器
        """
        provider_name = getattr(self, 'name', 'unknown')
        logger.info(f"开始处理优化版本请求 - 供应商: {provider_name}")
        
        try:
            # 执行供应商兼容性检查
            validation_result = request.validate_for_provider(provider_name)
            
            # 处理兼容性错误
            if validation_result["errors"]:
                error_msg = f"请求与供应商 {provider_name} 不兼容: {validation_result['errors']}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # 记录兼容性警告
            for warning in validation_result["warnings"]:
                logger.warning(f"供应商兼容性警告: {warning}")
            
            # 记录优化建议
            for info in validation_result["info"]:
                logger.info(f"供应商优化建议: {info}")
            
            # 获取供应商特定参数
            provider_params = self._get_provider_specific_params(request, provider_name)
            
            # 调用供应商特定的实现
            return await self._generate_text_with_params(provider_params, request)
            
        except Exception as e:
            logger.error(f"优化版本文本生成失败 - 供应商: {provider_name}, 错误: {e}")
            raise
    
    def _get_provider_specific_params(
        self, 
        request: OptimizedTextGenerationRequest, 
        provider_name: str
    ) -> Dict[str, Any]:
        """获取供应商特定参数
        
        这个方法根据供应商类型生成相应的API参数格式。
        
        Args:
            request: 优化版本请求
            provider_name: 供应商名称
            
        Returns:
            Dict[str, Any]: 供应商特定的API参数
        """
        provider_name = provider_name.lower()
        
        if provider_name == "openai":
            return self._get_openai_params(request)
        elif provider_name == "anthropic":
            return self._get_anthropic_params(request)
        elif provider_name == "google":
            return self._get_google_params(request)
        elif provider_name == "dashscope":
            return self._get_dashscope_params(request)
        else:
            # 默认转换为传统格式
            logger.warning(f"未知供应商 {provider_name}，使用传统格式")
            return request.to_legacy_format()
    
    def _get_openai_params(self, request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """获取OpenAI格式的参数"""
        params = {
            "model": request.model,
            "messages": [
                {
                    "role": msg.role.value,
                    "content": msg.content,
                    **({"name": msg.name} if msg.name else {}),
                    **({"function_call": msg.function_call} if msg.function_call else {}),
                    **({"tool_calls": msg.tool_calls} if msg.tool_calls else {})
                }
                for msg in request.messages
            ],
            "stream": request.stream.enabled,
        }
        
        # 生成参数
        if request.generation.max_tokens is not None:
            params["max_tokens"] = request.generation.max_tokens
        params["temperature"] = request.generation.temperature
        if request.generation.top_p is not None:
            params["top_p"] = request.generation.top_p
        params["frequency_penalty"] = request.generation.frequency_penalty
        params["presence_penalty"] = request.generation.presence_penalty
        if request.generation.stop is not None:
            params["stop"] = request.generation.stop
        
        # 高级功能
        if request.functions is not None:
            params["functions"] = request.functions
        if request.function_call is not None:
            params["function_call"] = request.function_call
        if request.tools is not None:
            params["tools"] = request.tools
        if request.tool_choice is not None:
            params["tool_choice"] = request.tool_choice
        if request.response_format is not None:
            params["response_format"] = request.response_format
        if request.user is not None:
            params["user"] = request.user
        
        # 合并供应商特定参数
        params.update(request.provider_params)
        
        return params
    
    def _get_anthropic_params(self, request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """获取Anthropic格式的参数"""
        # 分离system消息和对话消息
        system_messages = []
        conversation_messages = []
        
        for msg in request.messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg.content)
            elif msg.role in [MessageRole.USER, MessageRole.ASSISTANT]:
                conversation_messages.append({
                    "role": msg.role.value,
                    "content": msg.content
                })
        
        params = {
            "model": request.model,
            "messages": conversation_messages,
            "max_tokens": request.generation.max_tokens or 4096,  # Anthropic要求必须指定
            "stream": request.stream.enabled,
        }
        
        # 添加system消息（如果有）
        if system_messages:
            params["system"] = "\n".join(system_messages)
        
        # 生成参数（只添加Anthropic支持的）
        params["temperature"] = request.generation.temperature
        if request.generation.top_p is not None:
            params["top_p"] = request.generation.top_p
        if request.generation.top_k is not None:
            params["top_k"] = request.generation.top_k
        
        # 停止序列（Anthropic使用不同的字段名）
        if request.generation.stop is not None:
            stop_sequences = (
                [request.generation.stop] if isinstance(request.generation.stop, str) 
                else request.generation.stop
            )
            params["stop_sequences"] = stop_sequences
        
        # 合并供应商特定参数
        params.update(request.provider_params)
        
        return params
    
    def _get_google_params(self, request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """获取Google AI格式的参数"""
        # 分离system消息和对话消息
        system_messages = []
        conversation_contents = []
        
        for msg in request.messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg.content)
            else:
                # 转换为Google格式
                role = "user" if msg.role == MessageRole.USER else "model"
                conversation_contents.append({
                    "role": role,
                    "parts": [{"text": msg.content}]
                })
        
        params = {
            "contents": conversation_contents,
            "generationConfig": {}
        }
        
        # 添加system指令（如果有）
        if system_messages:
            params["systemInstruction"] = {
                "parts": [{"text": "\n".join(system_messages)}]
            }
        
        # 生成配置
        generation_config = {}
        if request.generation.max_tokens is not None:
            generation_config["maxOutputTokens"] = min(request.generation.max_tokens, 8192)
        generation_config["temperature"] = request.generation.temperature
        if request.generation.top_p is not None:
            generation_config["topP"] = request.generation.top_p
        if request.generation.top_k is not None:
            generation_config["topK"] = request.generation.top_k
        if request.generation.stop is not None:
            stop_sequences = (
                [request.generation.stop] if isinstance(request.generation.stop, str) 
                else request.generation.stop
            )
            generation_config["stopSequences"] = stop_sequences
        
        params["generationConfig"] = generation_config
        
        # 安全设置
        if request.safety is not None:
            safety_settings = []
            safety_categories = [
                "HARM_CATEGORY_HARASSMENT",
                "HARM_CATEGORY_HATE_SPEECH", 
                "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "HARM_CATEGORY_DANGEROUS_CONTENT"
            ]
            
            threshold_mapping = {
                "low": "BLOCK_ONLY_HIGH",
                "medium": "BLOCK_MEDIUM_AND_ABOVE", 
                "high": "BLOCK_LOW_AND_ABOVE",
                "strict": "BLOCK_LOW_AND_ABOVE"
            }
            
            threshold = threshold_mapping.get(request.safety.safety_level, "BLOCK_MEDIUM_AND_ABOVE")
            
            for category in safety_categories:
                safety_settings.append({
                    "category": category,
                    "threshold": threshold
                })
            
            params["safetySettings"] = safety_settings
        
        # Thinking配置（Google特有功能）
        if "thinking_budget" in request.provider_params:
            params["thinkingConfig"] = {
                "thinkingBudget": request.provider_params["thinking_budget"]
            }
        
        # 合并其他供应商特定参数
        for key, value in request.provider_params.items():
            if key not in ["thinking_budget"]:  # 避免重复添加
                params[key] = value
        
        return params
    
    def _get_dashscope_params(self, request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """获取DashScope格式的参数"""
        # 转换消息格式
        messages = []
        for msg in request.messages:
            messages.append({
                "role": msg.role.value,
                "content": msg.content
            })
        
        params = {
            "model": request.model,
            "input": {
                "messages": messages
            },
            "parameters": {
                "result_format": "message"  # DashScope特有设置
            }
        }
        
        # 生成参数
        parameters = params["parameters"]
        if request.generation.max_tokens is not None:
            parameters["max_tokens"] = min(request.generation.max_tokens, 32000)
        parameters["temperature"] = request.generation.temperature
        if request.generation.top_p is not None:
            parameters["top_p"] = request.generation.top_p
        if request.generation.top_k is not None:
            parameters["top_k"] = request.generation.top_k
        if request.generation.stop is not None:
            parameters["stop"] = request.generation.stop
        
        # 流式输出
        if request.stream.enabled:
            parameters["incremental_output"] = True
        
        # 工具调用支持
        if request.tools is not None:
            parameters["tools"] = request.tools
        if request.tool_choice is not None:
            parameters["tool_choice"] = request.tool_choice
        
        # 合并供应商特定参数
        if request.provider_params:
            parameters.update(request.provider_params)
        
        return params
    
    @abstractmethod
    async def _generate_text_with_params(
        self, 
        params: Dict[str, Any], 
        request: OptimizedTextGenerationRequest
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """使用供应商特定参数生成文本
        
        这个方法需要由具体的供应商实现来重写，
        用于调用实际的供应商API。
        
        Args:
            params: 供应商特定的API参数
            request: 原始的优化版本请求
            
        Returns:
            文本生成响应或流式响应迭代器
        """
        pass
