"""
AI Gen Hub 核心接口定义

定义了所有AI服务的抽象基类和标准接口，确保不同供应商的实现具有一致性。
这些接口遵循SOLID原则，提供了良好的扩展性和可维护性。

版本更新：
- 集成优化版本的文本生成请求数据结构
- 支持渐进式迁移和供应商兼容性增强
- 保持向后兼容性，现有代码无需修改
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, AsyncIterator, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator

# 设置日志记录器
logger = logging.getLogger(__name__)


# =============================================================================
# 枚举定义
# =============================================================================

class ModelType(str, Enum):
    """AI模型类型枚举"""
    TEXT_GENERATION = "text_generation"  # 文本生成模型
    IMAGE_GENERATION = "image_generation"  # 图像生成模型
    EMBEDDING = "embedding"  # 嵌入模型
    AUDIO_GENERATION = "audio_generation"  # 音频生成模型
    VIDEO_GENERATION = "video_generation"  # 视频生成模型


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"  # 系统消息
    USER = "user"  # 用户消息
    ASSISTANT = "assistant"  # 助手消息
    FUNCTION = "function"  # 函数调用消息
    TOOL = "tool"  # 工具调用消息


class ProviderStatus(str, Enum):
    """供应商状态枚举"""
    HEALTHY = "healthy"  # 健康状态
    DEGRADED = "degraded"  # 降级状态
    UNHEALTHY = "unhealthy"  # 不健康状态
    MAINTENANCE = "maintenance"  # 维护状态


class RequestStatus(str, Enum):
    """请求状态枚举"""
    PENDING = "pending"  # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


# =============================================================================
# 基础数据模型
# =============================================================================

class Message(BaseModel):
    """聊天消息模型"""
    role: MessageRole = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    name: Optional[str] = Field(None, description="消息发送者名称")
    function_call: Optional[Dict[str, Any]] = Field(None, description="函数调用信息")
    tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="工具调用信息")


class Usage(BaseModel):
    """使用量统计模型"""
    prompt_tokens: int = Field(0, description="输入token数量")
    completion_tokens: int = Field(0, description="输出token数量")
    total_tokens: int = Field(0, description="总token数量")
    thinking_tokens: Optional[int] = Field(None, description="思考过程token数量（Gemini 2.5专用）")


class ProviderInfo(BaseModel):
    """供应商信息模型"""
    name: str = Field(..., description="供应商名称")
    version: str = Field(..., description="供应商版本")
    status: ProviderStatus = Field(..., description="供应商状态")
    models: List[str] = Field(default_factory=list, description="支持的模型列表")
    capabilities: List[ModelType] = Field(default_factory=list, description="支持的能力")
    rate_limits: Dict[str, int] = Field(default_factory=dict, description="速率限制")
    last_health_check: Optional[datetime] = Field(None, description="最后健康检查时间")


# =============================================================================
# 文本生成相关模型
# =============================================================================

class TextGenerationRequest(BaseModel):
    """文本生成请求模型"""
    messages: List[Message] = Field(..., description="对话消息列表")
    model: str = Field(..., description="使用的模型名称")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="生成温度")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="核采样参数")
    top_k: Optional[int] = Field(None, ge=1, description="Top-K采样参数")
    frequency_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="频率惩罚")
    presence_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="存在惩罚")
    stop: Optional[Union[str, List[str]]] = Field(None, description="停止序列")
    stream: bool = Field(False, description="是否流式输出")
    functions: Optional[List[Dict[str, Any]]] = Field(None, description="可用函数列表")
    function_call: Optional[Union[str, Dict[str, str]]] = Field(None, description="函数调用设置")
    tools: Optional[List[Dict[str, Any]]] = Field(None, description="可用工具列表")
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(None, description="工具选择设置")
    user: Optional[str] = Field(None, description="用户标识")

    # 结构化输出相关参数
    response_format: Optional[Dict[str, Any]] = Field(None, description="响应格式配置")
    response_schema: Optional[Dict[str, Any]] = Field(None, description="JSON Schema 约束")

    # Thinking 配置参数（Gemini 2.5 专用）
    thinking_budget: Optional[int] = Field(None, description="思考过程的 token 预算")
    thinking_config: Optional[Dict[str, Any]] = Field(None, description="思考配置")

    # 安全设置参数
    safety_settings: Optional[List[Dict[str, Any]]] = Field(None, description="安全设置配置")

    # 供应商特定参数
    provider_params: Dict[str, Any] = Field(default_factory=dict, description="供应商特定参数")


class TextGenerationChoice(BaseModel):
    """文本生成选择模型"""
    index: int = Field(..., description="选择索引")
    message: Message = Field(..., description="生成的消息")
    finish_reason: Optional[str] = Field(None, description="结束原因")


class TextGenerationResponse(BaseModel):
    """文本生成响应模型"""
    id: str = Field(..., description="响应ID")
    object: str = Field("chat.completion", description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="使用的模型")
    choices: List[TextGenerationChoice] = Field(..., description="生成选择列表")
    usage: Optional[Usage] = Field(None, description="使用量统计")
    provider: str = Field(..., description="供应商名称")
    request_id: str = Field(..., description="请求ID")
    processing_time: float = Field(..., description="处理时间（秒）")


class TextGenerationStreamChunk(BaseModel):
    """文本生成流式响应块模型"""
    id: str = Field(..., description="响应ID")
    object: str = Field("chat.completion.chunk", description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="使用的模型")
    choices: List[Dict[str, Any]] = Field(..., description="流式选择数据")
    provider: str = Field(..., description="供应商名称")
    request_id: str = Field(..., description="请求ID")


# =============================================================================
# 图像生成相关模型
# =============================================================================

class ImageGenerationRequest(BaseModel):
    """图像生成请求模型"""
    prompt: str = Field(..., description="图像描述提示")
    model: Optional[str] = Field(None, description="使用的模型名称")
    n: int = Field(1, ge=1, le=10, description="生成图像数量")
    size: str = Field("1024x1024", description="图像尺寸")
    quality: str = Field("standard", description="图像质量")
    style: Optional[str] = Field(None, description="图像风格")
    response_format: str = Field("url", description="响应格式")
    user: Optional[str] = Field(None, description="用户标识")

    # Gemini 图像生成特定参数
    input_images: Optional[List[str]] = Field(None, description="输入图像列表（用于图像编辑）")
    response_modalities: Optional[List[str]] = Field(None, description="响应模态（TEXT, IMAGE）")
    edit_instruction: Optional[str] = Field(None, description="图像编辑指令")

    # 供应商特定参数
    provider_params: Dict[str, Any] = Field(default_factory=dict, description="供应商特定参数")


class ImageData(BaseModel):
    """图像数据模型"""
    url: Optional[str] = Field(None, description="图像URL")
    b64_json: Optional[str] = Field(None, description="Base64编码的图像数据")
    revised_prompt: Optional[str] = Field(None, description="修订后的提示")


class ImageGenerationResponse(BaseModel):
    """图像生成响应模型"""
    created: int = Field(..., description="创建时间戳")
    data: List[ImageData] = Field(..., description="生成的图像数据")
    provider: str = Field(..., description="供应商名称")
    request_id: str = Field(..., description="请求ID")
    processing_time: float = Field(..., description="处理时间（秒）")


# =============================================================================
# 核心接口定义
# =============================================================================

class AIProvider(ABC):
    """AI供应商抽象基类
    
    定义了所有AI供应商必须实现的核心接口。每个具体的供应商适配器
    都应该继承这个基类并实现相应的方法。
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """初始化供应商
        
        Args:
            name: 供应商名称
            config: 供应商配置
        """
        self.name = name
        self.config = config
        self._status = ProviderStatus.HEALTHY
        self._last_health_check: Optional[datetime] = None
    
    @property
    def status(self) -> ProviderStatus:
        """获取供应商状态"""
        return self._status
    
    @property
    def last_health_check(self) -> Optional[datetime]:
        """获取最后健康检查时间"""
        return self._last_health_check
    
    @abstractmethod
    async def initialize(self) -> None:
        """初始化供应商连接和配置"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源和连接"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """执行健康检查
        
        Returns:
            bool: 健康状态，True表示健康，False表示不健康
        """
        pass
    
    @abstractmethod
    async def get_provider_info(self) -> ProviderInfo:
        """获取供应商信息
        
        Returns:
            ProviderInfo: 供应商详细信息
        """
        pass
    
    @abstractmethod
    def supports_model_type(self, model_type: ModelType) -> bool:
        """检查是否支持指定的模型类型
        
        Args:
            model_type: 模型类型
            
        Returns:
            bool: 是否支持
        """
        pass
    
    @abstractmethod
    async def generate_text(
        self, 
        request: TextGenerationRequest
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """生成文本
        
        Args:
            request: 文本生成请求
            
        Returns:
            文本生成响应或流式响应迭代器
        """
        pass
    
    @abstractmethod
    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResponse:
        """生成图像
        
        Args:
            request: 图像生成请求
            
        Returns:
            ImageGenerationResponse: 图像生成响应
        """
        pass


class ProviderManager(ABC):
    """供应商管理器抽象基类
    
    负责管理多个AI供应商，提供负载均衡、故障转移等功能。
    """
    
    @abstractmethod
    async def add_provider(self, provider: AIProvider) -> None:
        """添加供应商"""
        pass
    
    @abstractmethod
    async def remove_provider(self, name: str) -> None:
        """移除供应商"""
        pass
    
    @abstractmethod
    async def get_provider(self, name: str) -> Optional[AIProvider]:
        """获取指定供应商"""
        pass
    
    @abstractmethod
    async def get_available_providers(
        self, 
        model_type: ModelType, 
        model: Optional[str] = None
    ) -> List[AIProvider]:
        """获取可用的供应商列表"""
        pass
    
    @abstractmethod
    async def select_provider(
        self,
        model_type: ModelType,
        model: Optional[str] = None,
        request_params: Optional[Dict[str, Any]] = None
    ) -> Optional[AIProvider]:
        """选择最佳供应商"""
        pass


# =============================================================================
# 优化版本的文本生成数据结构
# =============================================================================

class GenerationConfig(BaseModel):
    """生成配置参数组

    将文本生成相关的参数组织到一个配置类中，提供更好的结构化和验证。
    这个设计使得参数管理更加清晰，同时便于不同供应商的参数映射。
    """
    max_tokens: Optional[int] = Field(
        None,
        ge=1,
        le=32768,
        description="最大生成token数，不设置则使用模型默认值"
    )
    temperature: float = Field(
        0.7,
        ge=0.0,
        le=2.0,
        description="生成温度，控制随机性。0.0为确定性输出，2.0为最大随机性"
    )
    top_p: Optional[float] = Field(
        None,
        ge=0.0,
        le=1.0,
        description="核采样参数，与temperature互斥使用"
    )
    top_k: Optional[int] = Field(
        None,
        ge=1,
        le=100,
        description="Top-K采样参数，限制候选token数量"
    )
    frequency_penalty: float = Field(
        0.0,
        ge=-2.0,
        le=2.0,
        description="频率惩罚，减少重复内容"
    )
    presence_penalty: float = Field(
        0.0,
        ge=-2.0,
        le=2.0,
        description="存在惩罚，鼓励话题多样性"
    )
    stop: Optional[Union[str, List[str]]] = Field(
        None,
        description="停止序列，遇到时停止生成"
    )

    @validator('stop')
    def validate_stop_sequences(cls, v):
        """验证停止序列"""
        if v is None:
            return v
        if isinstance(v, str):
            return [v] if v.strip() else None
        if isinstance(v, list):
            # 过滤空字符串并限制数量
            filtered = [s for s in v if isinstance(s, str) and s.strip()]
            if len(filtered) > 10:
                raise ValueError("停止序列数量不能超过10个")
            return filtered if filtered else None
        raise ValueError("停止序列必须是字符串或字符串列表")


class StreamConfig(BaseModel):
    """流式输出配置

    专门用于配置流式输出相关的参数，提供更精细的控制。
    """
    enabled: bool = Field(False, description="是否启用流式输出")
    chunk_size: Optional[int] = Field(
        None,
        ge=1,
        le=1000,
        description="流式输出块大小（字符数）"
    )
    include_usage: bool = Field(
        True,
        description="是否在流式输出中包含使用量统计"
    )


class SafetyConfig(BaseModel):
    """安全配置参数

    统一的安全配置抽象，可以映射到不同供应商的安全设置。
    """
    content_filter: bool = Field(True, description="是否启用内容过滤")
    safety_level: str = Field(
        "medium",
        pattern="^(low|medium|high|strict)$",
        description="安全级别：low, medium, high, strict"
    )
    custom_filters: Optional[List[str]] = Field(
        None,
        description="自定义过滤规则"
    )


class OptimizedTextGenerationRequest(BaseModel):
    """优化版本的文本生成请求模型

    这是新的优化版本请求格式，提供了以下改进：
    1. 参数分组：将相关参数组织到子配置中，提高可读性和可维护性
    2. 更好的默认值：基于最佳实践设置合理默认值
    3. 增强验证：添加更严格的参数验证和智能建议
    4. 供应商兼容性：内置供应商适配和兼容性检查功能
    5. 向后兼容：支持与传统格式的双向转换

    使用示例：
        >>> request = OptimizedTextGenerationRequest(
        ...     messages=[Message(role=MessageRole.USER, content="Hello")],
        ...     model="gpt-4",
        ...     generation=GenerationConfig(temperature=0.8, max_tokens=1000),
        ...     stream=StreamConfig(enabled=True)
        ... )
    """

    # === 核心必需参数 ===
    messages: List[Message] = Field(
        ...,
        min_items=1,
        description="对话消息列表，至少包含一条消息"
    )
    model: str = Field(
        ...,
        min_length=1,
        description="使用的模型名称，如 'gpt-4', 'claude-3-sonnet'"
    )

    # === 生成配置 ===
    generation: GenerationConfig = Field(
        default_factory=GenerationConfig,
        description="文本生成配置参数"
    )

    # === 流式配置 ===
    stream: StreamConfig = Field(
        default_factory=StreamConfig,
        description="流式输出配置"
    )

    # === 安全配置 ===
    safety: Optional[SafetyConfig] = Field(
        None,
        description="安全配置，不设置则使用默认安全策略"
    )

    # === 高级功能参数 ===
    functions: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="可用函数列表，用于函数调用功能"
    )
    function_call: Optional[Union[str, Dict[str, str]]] = Field(
        None,
        description="函数调用设置：'auto', 'none', 或指定函数"
    )
    tools: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="可用工具列表"
    )
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(
        None,
        description="工具选择设置"
    )

    # === 结构化输出参数 ===
    response_format: Optional[Dict[str, Any]] = Field(
        None,
        description="响应格式配置，支持JSON Schema约束"
    )
    response_schema: Optional[Dict[str, Any]] = Field(
        None,
        description="JSON Schema约束"
    )

    # === 元数据参数 ===
    user: Optional[str] = Field(
        None,
        description="用户标识，用于追踪和分析"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="请求元数据，用于日志和分析"
    )

    # === 供应商特定参数 ===
    provider_params: Dict[str, Any] = Field(
        default_factory=dict,
        description="供应商特定参数，透传给底层API"
    )

    @validator('messages')
    def validate_messages(cls, v):
        """验证消息列表"""
        if not v:
            raise ValueError("消息列表不能为空")

        # 检查消息角色顺序的合理性
        roles = [msg.role for msg in v]

        # 第一条消息通常应该是system或user
        if roles[0] not in [MessageRole.SYSTEM, MessageRole.USER]:
            logger.warning("第一条消息建议使用system或user角色")

        return v

    @validator('model')
    def validate_model(cls, v):
        """验证模型名称"""
        if not v or not v.strip():
            raise ValueError("模型名称不能为空")
        return v.strip()

    class Config:
        """Pydantic配置"""
        extra = "forbid"  # 禁止额外字段
        use_enum_values = True  # 使用枚举值
        validate_assignment = True  # 验证赋值

    # 集成渐进式迁移和供应商兼容性方法
    @classmethod
    def from_legacy_request(cls, legacy_request: Union[Dict[str, Any], "TextGenerationRequest"]) -> "OptimizedTextGenerationRequest":
        """从传统格式转换为优化版本"""
        from .optimized_request_utils import OptimizedRequestMixin
        return OptimizedRequestMixin.from_legacy_request.__func__(cls, legacy_request)

    def to_legacy_format(self) -> Dict[str, Any]:
        """转换为传统格式"""
        from .optimized_request_utils import OptimizedRequestMixin
        return OptimizedRequestMixin.to_legacy_format(self)

    @staticmethod
    def get_provider_capabilities(provider_name: str) -> Dict[str, Any]:
        """获取供应商能力信息"""
        from .optimized_request_utils import OptimizedRequestMixin
        return OptimizedRequestMixin.get_provider_capabilities(provider_name)

    def validate_for_provider(self, provider_name: str) -> Dict[str, List[str]]:
        """验证供应商兼容性"""
        from .optimized_request_utils import OptimizedRequestMixin
        return OptimizedRequestMixin.validate_for_provider(self, provider_name)


# =============================================================================
# 请求适配器接口
# =============================================================================

class RequestAdapter(ABC):
    """请求适配器抽象基类

    用于在传统格式和优化版本之间进行转换，支持渐进式迁移。
    """

    @staticmethod
    def adapt_request(request: Union[Dict[str, Any], TextGenerationRequest, OptimizedTextGenerationRequest]) -> OptimizedTextGenerationRequest:
        """适配请求到优化版本

        Args:
            request: 任意格式的请求

        Returns:
            OptimizedTextGenerationRequest: 优化版本请求
        """
        if isinstance(request, OptimizedTextGenerationRequest):
            return request
        elif isinstance(request, (dict, TextGenerationRequest)):
            return OptimizedTextGenerationRequest.from_legacy_request(request)
        else:
            raise ValueError(f"不支持的请求类型: {type(request)}")

    @staticmethod
    def is_optimized_request(request: Any) -> bool:
        """检查是否为优化版本请求"""
        return isinstance(request, OptimizedTextGenerationRequest)
