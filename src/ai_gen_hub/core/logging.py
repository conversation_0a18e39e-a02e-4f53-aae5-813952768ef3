"""
AI Gen Hub 日志系统

提供结构化日志记录功能，支持多种输出格式和日志级别。
集成了请求追踪、性能监控和错误报告功能。
"""

import logging
import sys
import time
from contextvars import ContextVar
from typing import Any, Dict, Optional
from uuid import uuid4

import structlog
from structlog.stdlib import LoggerFactory


# 上下文变量，用于存储请求ID和其他上下文信息
request_id_var: ContextVar[Optional[str]] = ContextVar("request_id", default=None)
user_id_var: ContextVar[Optional[str]] = ContextVar("user_id", default=None)
provider_var: ContextVar[Optional[str]] = ContextVar("provider", default=None)


def add_request_context(
    request_id: Optional[str] = None,
    user_id: Optional[str] = None,
    provider: Optional[str] = None
) -> None:
    """添加请求上下文信息
    
    Args:
        request_id: 请求ID，如果为None则自动生成
        user_id: 用户ID
        provider: 供应商名称
    """
    if request_id is None:
        request_id = str(uuid4())
    
    request_id_var.set(request_id)
    if user_id:
        user_id_var.set(user_id)
    if provider:
        provider_var.set(provider)


def clear_request_context() -> None:
    """清除请求上下文信息"""
    request_id_var.set(None)
    user_id_var.set(None)
    provider_var.set(None)


def add_context_processor(logger: Any, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """添加上下文信息到日志事件
    
    Args:
        logger: 日志记录器
        method_name: 方法名
        event_dict: 事件字典
        
    Returns:
        更新后的事件字典
    """
    # 添加请求ID
    request_id = request_id_var.get()
    if request_id:
        event_dict["request_id"] = request_id
    
    # 添加用户ID
    user_id = user_id_var.get()
    if user_id:
        event_dict["user_id"] = user_id
    
    # 添加供应商信息
    provider = provider_var.get()
    if provider:
        event_dict["provider"] = provider
    
    # 添加时间戳
    event_dict["timestamp"] = time.time()
    
    return event_dict


def add_log_level_processor(logger: Any, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """添加日志级别信息
    
    Args:
        logger: 日志记录器
        method_name: 方法名
        event_dict: 事件字典
        
    Returns:
        更新后的事件字典
    """
    event_dict["level"] = method_name.upper()
    return event_dict


def setup_logging(
    log_level: str = "INFO",
    log_format: str = "json",
    debug: bool = False
) -> None:
    """设置日志系统
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: 日志格式 (json, text)
        debug: 是否启用调试模式
    """
    # 设置日志级别
    if debug:
        log_level = "DEBUG"
    
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 配置structlog
    processors = [
        structlog.stdlib.filter_by_level,
        add_context_processor,
        add_log_level_processor,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    if log_format.lower() == "json":
        # JSON格式输出
        processors.append(structlog.processors.JSONRenderer())
    else:
        # 文本格式输出
        processors.extend([
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.dev.ConsoleRenderer(colors=debug)
        ])
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # 配置标准库日志
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=level,
    )
    
    # 设置第三方库的日志级别
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        配置好的日志记录器
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """日志记录器混入类
    
    为类提供便捷的日志记录功能
    """
    
    @property
    def logger(self) -> structlog.stdlib.BoundLogger:
        """获取类的日志记录器"""
        if not hasattr(self, "_logger"):
            self._logger = get_logger(self.__class__.__name__)
        return self._logger


class RequestLogger:
    """请求日志记录器
    
    专门用于记录API请求的详细信息
    """
    
    def __init__(self, logger_name: str = "request"):
        self.logger = get_logger(logger_name)
    
    def log_request_start(
        self,
        method: str,
        path: str,
        headers: Optional[Dict[str, str]] = None,
        query_params: Optional[Dict[str, Any]] = None,
        body_size: Optional[int] = None
    ) -> None:
        """记录请求开始
        
        Args:
            method: HTTP方法
            path: 请求路径
            headers: 请求头（敏感信息会被过滤）
            query_params: 查询参数
            body_size: 请求体大小
        """
        # 过滤敏感头信息
        safe_headers = {}
        if headers:
            for key, value in headers.items():
                if key.lower() in ["authorization", "x-api-key", "cookie"]:
                    safe_headers[key] = "***"
                else:
                    safe_headers[key] = value
        
        self.logger.info(
            "请求开始",
            method=method,
            path=path,
            headers=safe_headers,
            query_params=query_params,
            body_size=body_size
        )
    
    def log_request_end(
        self,
        status_code: int,
        response_size: Optional[int] = None,
        processing_time: Optional[float] = None,
        error: Optional[str] = None
    ) -> None:
        """记录请求结束
        
        Args:
            status_code: HTTP状态码
            response_size: 响应体大小
            processing_time: 处理时间（秒）
            error: 错误信息
        """
        log_data = {
            "status_code": status_code,
            "response_size": response_size,
            "processing_time": processing_time
        }
        
        if error:
            log_data["error"] = error
            self.logger.error("请求失败", **log_data)
        else:
            self.logger.info("请求完成", **log_data)
    
    def log_provider_request(
        self,
        provider: str,
        model: str,
        request_type: str,
        input_tokens: Optional[int] = None,
        output_tokens: Optional[int] = None,
        cost: Optional[float] = None
    ) -> None:
        """记录供应商请求
        
        Args:
            provider: 供应商名称
            model: 模型名称
            request_type: 请求类型
            input_tokens: 输入token数
            output_tokens: 输出token数
            cost: 请求成本
        """
        self.logger.info(
            "供应商请求",
            provider=provider,
            model=model,
            request_type=request_type,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            cost=cost
        )


class PerformanceLogger:
    """性能日志记录器
    
    专门用于记录性能相关的指标
    """
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = get_logger(logger_name)
    
    def log_cache_hit(self, cache_type: str, key: str, hit: bool) -> None:
        """记录缓存命中情况
        
        Args:
            cache_type: 缓存类型
            key: 缓存键
            hit: 是否命中
        """
        self.logger.info(
            "缓存访问",
            cache_type=cache_type,
            cache_key=key,
            cache_hit=hit
        )
    
    def log_provider_performance(
        self,
        provider: str,
        response_time: float,
        success: bool,
        error_type: Optional[str] = None
    ) -> None:
        """记录供应商性能
        
        Args:
            provider: 供应商名称
            response_time: 响应时间
            success: 是否成功
            error_type: 错误类型
        """
        self.logger.info(
            "供应商性能",
            provider=provider,
            response_time=response_time,
            success=success,
            error_type=error_type
        )
    
    def log_queue_metrics(
        self,
        queue_name: str,
        queue_size: int,
        processing_time: float
    ) -> None:
        """记录队列指标
        
        Args:
            queue_name: 队列名称
            queue_size: 队列大小
            processing_time: 处理时间
        """
        self.logger.info(
            "队列指标",
            queue_name=queue_name,
            queue_size=queue_size,
            processing_time=processing_time
        )
