"""
AI Gen Hub WebSocket API路由

提供WebSocket连接支持，包括：
- 实时文本生成
- 连接管理
- 消息处理
"""

import json
from typing import Dict, Any

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Request
from fastapi.websockets import WebSocketState

from ai_gen_hub.core.interfaces import TextGenerationRequest
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.core.logging import get_logger


router = APIRouter()
logger = get_logger("websocket")


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info("WebSocket连接建立", client_id=client_id)
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info("WebSocket连接断开", client_id=client_id)
    
    async def send_personal_message(self, message: str, client_id: str):
        """发送个人消息"""
        websocket = self.active_connections.get(client_id)
        if websocket and websocket.client_state == WebSocketState.CONNECTED:
            await websocket.send_text(message)
    
    async def send_json_message(self, data: Dict[str, Any], client_id: str):
        """发送JSON消息"""
        websocket = self.active_connections.get(client_id)
        if websocket and websocket.client_state == WebSocketState.CONNECTED:
            await websocket.send_json(data)


# 全局连接管理器
manager = ConnectionManager()


@router.websocket("/text/generate")
async def websocket_text_generation(websocket: WebSocket):
    """WebSocket文本生成端点"""
    client_id = f"ws_{id(websocket)}"
    
    try:
        await manager.connect(websocket, client_id)
        
        # 发送连接确认
        await manager.send_json_message({
            "type": "connection",
            "status": "connected",
            "client_id": client_id,
            "message": "WebSocket连接已建立"
        }, client_id)
        
        while True:
            # 接收消息
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                await handle_text_generation_message(websocket, client_id, message)
                
            except json.JSONDecodeError:
                await manager.send_json_message({
                    "type": "error",
                    "error": "无效的JSON格式"
                }, client_id)
            except Exception as e:
                logger.error("处理WebSocket消息失败", client_id=client_id, error=str(e))
                await manager.send_json_message({
                    "type": "error",
                    "error": str(e)
                }, client_id)
    
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error("WebSocket连接异常", client_id=client_id, error=str(e))
        manager.disconnect(client_id)


async def handle_text_generation_message(
    websocket: WebSocket,
    client_id: str,
    message: Dict[str, Any]
):
    """处理文本生成消息"""
    message_type = message.get("type")
    
    if message_type == "generate":
        await handle_generate_request(websocket, client_id, message)
    elif message_type == "ping":
        await manager.send_json_message({
            "type": "pong",
            "timestamp": message.get("timestamp")
        }, client_id)
    else:
        await manager.send_json_message({
            "type": "error",
            "error": f"未知的消息类型: {message_type}"
        }, client_id)


async def handle_generate_request(
    websocket: WebSocket,
    client_id: str,
    message: Dict[str, Any]
):
    """处理生成请求"""
    try:
        # 获取服务
        text_service = getattr(websocket.app.state, "text_service", None)
        if not text_service:
            await manager.send_json_message({
                "type": "error",
                "error": "文本生成服务未初始化"
            }, client_id)
            return
        
        # 解析请求
        request_data = message.get("data", {})
        
        # 强制启用流式输出
        request_data["stream"] = True
        
        try:
            text_request = TextGenerationRequest(**request_data)
        except Exception as e:
            await manager.send_json_message({
                "type": "error",
                "error": f"请求参数无效: {str(e)}"
            }, client_id)
            return
        
        # 发送开始消息
        await manager.send_json_message({
            "type": "generation_start",
            "request_id": message.get("request_id")
        }, client_id)
        
        # 执行生成
        try:
            response_stream = await text_service.generate_text(
                text_request,
                user_id=client_id,
                request_id=message.get("request_id")
            )
            
            # 流式发送结果
            async for chunk in response_stream:
                await manager.send_json_message({
                    "type": "generation_chunk",
                    "request_id": message.get("request_id"),
                    "data": chunk.dict()
                }, client_id)
            
            # 发送完成消息
            await manager.send_json_message({
                "type": "generation_complete",
                "request_id": message.get("request_id")
            }, client_id)
            
        except AIGenHubException as e:
            await manager.send_json_message({
                "type": "generation_error",
                "request_id": message.get("request_id"),
                "error": e.to_dict()
            }, client_id)
        except Exception as e:
            await manager.send_json_message({
                "type": "generation_error",
                "request_id": message.get("request_id"),
                "error": {
                    "error_code": "INTERNAL_ERROR",
                    "message": str(e),
                    "retryable": False
                }
            }, client_id)
    
    except Exception as e:
        logger.error("处理生成请求失败", client_id=client_id, error=str(e))
        await manager.send_json_message({
            "type": "error",
            "error": "内部服务器错误"
        }, client_id)


@router.websocket("/status")
async def websocket_status(websocket: WebSocket):
    """WebSocket状态监控端点"""
    client_id = f"status_{id(websocket)}"
    
    try:
        await manager.connect(websocket, client_id)
        
        # 定期发送状态信息
        import asyncio
        
        while True:
            try:
                # 获取系统状态
                status_data = {
                    "type": "status",
                    "timestamp": time.time(),
                    "active_connections": len(manager.active_connections),
                    "system_status": "healthy"  # 可以从健康检查获取
                }
                
                await manager.send_json_message(status_data, client_id)
                
                # 等待30秒
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error("发送状态信息失败", client_id=client_id, error=str(e))
                break
    
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error("状态WebSocket连接异常", client_id=client_id, error=str(e))
        manager.disconnect(client_id)
