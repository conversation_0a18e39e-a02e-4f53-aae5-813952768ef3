"""
AI Gen Hub 图像生成API路由

提供图像生成相关的API端点，包括：
- 图像生成
- 批量图像生成
- 支持的模型查询
"""

from typing import Dict, List

from fastapi import APIRouter, Request, HTTPException, Depends

from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
)
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.services import ImageGenerationService


router = APIRouter()


def get_image_service(request: Request) -> ImageGenerationService:
    """获取图像生成服务依赖"""
    service = getattr(request.app.state, "image_service", None)
    if not service:
        raise HTTPException(status_code=500, detail="图像生成服务未初始化")
    return service


def get_user_id(request: Request) -> str:
    """获取用户ID依赖"""
    user = getattr(request.state, "user", {})
    return user.get("user_id", "anonymous")


@router.post("/generate", response_model=ImageGenerationResponse)
async def generate_image(
    request_data: ImageGenerationRequest,
    request: Request,
    image_service: ImageGenerationService = Depends(get_image_service),
    user_id: str = Depends(get_user_id)
):
    """生成图像
    
    根据文本提示生成图像
    """
    try:
        response = await image_service.generate_image(
            request_data,
            user_id=user_id,
            request_id=getattr(request.state, "request_id", None)
        )
        return response
        
    except AIGenHubException as e:
        raise HTTPException(status_code=400, detail=e.to_dict())
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate/batch", response_model=List[ImageGenerationResponse])
async def generate_images_batch(
    requests: List[ImageGenerationRequest],
    request: Request,
    image_service: ImageGenerationService = Depends(get_image_service),
    user_id: str = Depends(get_user_id)
):
    """批量生成图像
    
    同时处理多个图像生成请求
    """
    try:
        responses = await image_service.generate_images_batch(
            requests,
            user_id=user_id,
            batch_id=getattr(request.state, "request_id", None)
        )
        return responses
        
    except AIGenHubException as e:
        raise HTTPException(status_code=400, detail=e.to_dict())
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models", response_model=Dict[str, List[str]])
async def get_supported_models(
    image_service: ImageGenerationService = Depends(get_image_service)
):
    """获取支持的图像生成模型列表
    
    返回按供应商分组的模型列表
    """
    try:
        models = await image_service.get_supported_models()
        return models
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_service_stats(
    image_service: ImageGenerationService = Depends(get_image_service)
):
    """获取图像生成服务统计信息"""
    try:
        stats = await image_service.get_service_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 兼容OpenAI API格式的端点
@router.post("/generations", response_model=ImageGenerationResponse)
async def image_generations(
    request_data: ImageGenerationRequest,
    request: Request,
    image_service: ImageGenerationService = Depends(get_image_service),
    user_id: str = Depends(get_user_id)
):
    """OpenAI兼容的图像生成API
    
    提供与OpenAI Image Generation API兼容的接口
    """
    return await generate_image(request_data, request, image_service, user_id)
