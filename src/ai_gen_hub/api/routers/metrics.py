"""
AI Gen Hub 监控指标API路由

提供监控指标相关的API端点，包括：
- Prometheus指标
- 系统统计信息
- 供应商统计信息
"""

import time

from fastapi import APIRouter, Request, Response
from fastapi.responses import PlainTextResponse

from ai_gen_hub.monitoring import metrics_collector


router = APIRouter()


@router.get("/prometheus", response_class=PlainTextResponse)
async def prometheus_metrics():
    """Prometheus格式的指标数据
    
    返回Prometheus可以抓取的指标数据
    """
    metrics_data = metrics_collector.get_metrics()
    return Response(
        content=metrics_data,
        media_type=metrics_collector.get_content_type()
    )


@router.get("/")
async def get_metrics():
    """获取JSON格式的指标摘要"""
    return {
        "timestamp": time.time(),
        "metrics_endpoint": "/metrics/prometheus",
        "description": "使用 /metrics/prometheus 获取Prometheus格式的指标数据"
    }


@router.get("/stats")
async def get_system_stats(request: Request):
    """获取系统统计信息"""
    try:
        # 获取各个组件的统计信息
        stats = {
            "timestamp": time.time(),
            "system": {}
        }
        
        # 供应商统计
        provider_manager = getattr(request.app.state, "provider_manager", None)
        if provider_manager:
            stats["providers"] = await provider_manager.get_provider_statistics()
        
        # 路由器统计
        router_obj = getattr(request.app.state, "router", None)
        if router_obj:
            stats["routing"] = router_obj.get_provider_statistics()
        
        # 缓存统计
        cache = getattr(request.app.state, "cache", None)
        if cache:
            if hasattr(cache, 'get_detailed_stats'):
                stats["cache"] = await cache.get_detailed_stats()
            else:
                cache_stats = await cache.get_stats()
                stats["cache"] = {
                    "hits": cache_stats.hits,
                    "misses": cache_stats.misses,
                    "hit_rate": cache_stats.hit_rate,
                    "total_size": cache_stats.total_size
                }
        
        # 文本生成服务统计
        text_service = getattr(request.app.state, "text_service", None)
        if text_service:
            stats["text_generation"] = await text_service.get_service_stats()
        
        # 图像生成服务统计
        image_service = getattr(request.app.state, "image_service", None)
        if image_service:
            stats["image_generation"] = await image_service.get_service_stats()
        
        return stats
        
    except Exception as e:
        return {
            "error": str(e),
            "timestamp": time.time()
        }
