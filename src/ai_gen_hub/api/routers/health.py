"""
AI Gen Hub 健康检查API路由

提供系统健康检查相关的API端点，包括：
- 存活检查
- 就绪检查
- 详细健康报告
"""

from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import JSONResponse

from ai_gen_hub.monitoring import HealthManager


router = APIRouter()


def get_health_manager(request: Request) -> HealthManager:
    """获取健康管理器依赖"""
    manager = getattr(request.app.state, "health_manager", None)
    if not manager:
        raise HTTPException(status_code=500, detail="健康管理器未初始化")
    return manager


@router.get("/live")
async def liveness_check():
    """存活检查
    
    检查应用是否正在运行
    """
    return {"status": "alive", "timestamp": time.time()}


@router.get("/ready")
async def readiness_check(
    health_manager: HealthManager = Depends(get_health_manager)
):
    """就绪检查
    
    检查应用是否准备好接收请求
    """
    try:
        report = await health_manager.check_health()
        
        if report.overall_status == "healthy":
            return JSONResponse(
                status_code=200,
                content={
                    "status": "ready",
                    "overall_status": report.overall_status,
                    "timestamp": report.timestamp
                }
            )
        else:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "not_ready",
                    "overall_status": report.overall_status,
                    "timestamp": report.timestamp,
                    "issues": [
                        {
                            "name": check.name,
                            "status": check.status,
                            "message": check.message
                        }
                        for check in report.checks
                        if not check.is_healthy
                    ]
                }
            )
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "error",
                "message": str(e),
                "timestamp": time.time()
            }
        )


@router.get("/")
async def health_check(
    health_manager: HealthManager = Depends(get_health_manager)
):
    """完整健康检查
    
    返回详细的健康检查报告
    """
    try:
        report = await health_manager.check_health()
        
        status_code = 200
        if report.overall_status == "degraded":
            status_code = 200  # 降级状态仍返回200
        elif report.overall_status == "unhealthy":
            status_code = 503
        
        return JSONResponse(
            status_code=status_code,
            content=report.to_dict()
        )
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "overall_status": "error",
                "message": str(e),
                "timestamp": time.time()
            }
        )


@router.get("/detailed")
async def detailed_health_check(
    health_manager: HealthManager = Depends(get_health_manager)
):
    """详细健康检查
    
    返回包含所有检查项详细信息的健康报告
    """
    try:
        report = await health_manager.check_health()
        return report.to_dict()
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
