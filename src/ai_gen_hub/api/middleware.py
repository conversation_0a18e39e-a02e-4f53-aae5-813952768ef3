"""
AI Gen Hub API 中间件

提供各种中间件功能，包括：
- 认证和授权
- 请求日志记录
- 速率限制
- 请求追踪
"""

import time
from typing import Optional
from uuid import uuid4

import jwt
from fastapi import Request, Response, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware

from ai_gen_hub.core.exceptions import (
    AuthenticationError,
    AuthorizationError,
    RateLimitError,
)
from ai_gen_hub.core.logging import (
    get_logger,
    add_request_context,
    clear_request_context,
    RequestLogger,
)
from ai_gen_hub.monitoring import metrics_collector


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志记录中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = get_logger("middleware.logging")
        self.request_logger = RequestLogger()
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        # 生成请求ID
        request_id = str(uuid4())
        request.state.request_id = request_id
        
        # 设置请求上下文
        add_request_context(request_id=request_id)
        
        # 记录请求开始
        start_time = time.time()
        
        # 获取请求信息
        method = request.method
        path = request.url.path
        query_params = dict(request.query_params)
        headers = dict(request.headers)
        
        # 获取请求体大小
        body_size = None
        if hasattr(request, "body"):
            try:
                body = await request.body()
                body_size = len(body) if body else 0
            except Exception:
                pass
        
        # 记录请求开始
        self.request_logger.log_request_start(
            method=method,
            path=path,
            headers=headers,
            query_params=query_params,
            body_size=body_size
        )
        
        # 更新活跃请求数
        metrics_collector.active_requests.inc()
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 获取响应大小
            response_size = None
            if hasattr(response, "body"):
                response_size = len(response.body) if response.body else 0
            
            # 记录请求结束
            self.request_logger.log_request_end(
                status_code=response.status_code,
                response_size=response_size,
                processing_time=processing_time
            )
            
            # 记录指标
            metrics_collector.record_request(
                provider="api",
                model="api",
                request_type="http",
                duration=processing_time,
                status="success" if response.status_code < 400 else "error",
                request_size=body_size,
                response_size=response_size
            )
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Processing-Time"] = str(processing_time)
            
            return response
            
        except Exception as e:
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 记录请求失败
            self.request_logger.log_request_end(
                status_code=500,
                processing_time=processing_time,
                error=str(e)
            )
            
            # 记录指标
            metrics_collector.record_request(
                provider="api",
                model="api",
                request_type="http",
                duration=processing_time,
                status="error"
            )
            
            raise
        
        finally:
            # 更新活跃请求数
            metrics_collector.active_requests.dec()
            
            # 清除请求上下文
            clear_request_context()


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """认证中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = get_logger("middleware.auth")
        self.security = HTTPBearer(auto_error=False)
        
        # 不需要认证的路径
        self.public_paths = {
            "/",
            "/health",
            "/health/",
            "/health/live",
            "/health/ready",
            "/metrics",
            "/docs",
            "/redoc",
            "/openapi.json",
        }
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        path = request.url.path

        # 记录认证中间件处理的请求
        self.logger.debug(
            "认证中间件处理请求",
            method=request.method,
            path=path,
            headers=dict(request.headers)
        )

        # 检查是否需要认证
        if self._is_public_path(path):
            self.logger.debug("请求路径为公开路径，跳过认证", path=path)
            return await call_next(request)

        # 获取设置
        settings = getattr(request.app.state, "settings", None)
        if not settings:
            self.logger.warning("设置未初始化，跳过认证", path=path)
            return await call_next(request)

        # 在开发环境下，对调试路径提供额外的宽松处理
        import os
        debug_mode = os.environ.get('DEBUG', '').lower() in ('true', '1', 'yes')
        environment = os.environ.get('ENVIRONMENT', 'production').lower()

        if (debug_mode or environment != 'production') and path.startswith('/debug'):
            self.logger.info("开发环境下访问调试路径，跳过认证", path=path)
            # 为调试路径设置匿名用户信息
            request.state.user = {"type": "debug", "user_id": "debug_user"}
            return await call_next(request)

        try:
            # 验证认证
            user_info = await self._authenticate(request, settings)

            # 将用户信息添加到请求状态
            request.state.user = user_info

            self.logger.debug("认证成功", path=path, user_type=user_info.get("type"))
            return await call_next(request)

        except (AuthenticationError, AuthorizationError) as e:
            self.logger.warning(
                "认证失败",
                path=path,
                error=e.message,
                has_api_key=bool(self._get_api_key(request)),
                has_bearer_token=bool(self._get_bearer_token(request))
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=e.message,
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    def _is_public_path(self, path: str) -> bool:
        """检查是否是公开路径"""
        # 精确匹配
        if path in self.public_paths:
            return True

        # 前缀匹配
        public_prefixes = ["/health/", "/docs", "/redoc"]

        # 在开发环境下，调试路径也是公开的
        # 注意：这里我们无法直接访问settings，所以通过环境变量检查
        import os
        debug_mode = os.environ.get('DEBUG', '').lower() in ('true', '1', 'yes')
        environment = os.environ.get('ENVIRONMENT', 'production').lower()

        # 记录调试信息
        self.logger.debug(
            "检查公开路径",
            path=path,
            debug_mode=debug_mode,
            environment=environment
        )

        if debug_mode or environment != 'production':
            # 添加调试相关的公开路径
            debug_prefixes = ["/debug", "/debug/"]
            public_prefixes.extend(debug_prefixes)

            # 同时添加诊断端点
            if path == "/diagnostic":
                self.logger.debug("诊断端点被识别为公开路径", path=path)
                return True

        for prefix in public_prefixes:
            if path.startswith(prefix):
                self.logger.debug("路径匹配公开前缀", path=path, prefix=prefix)
                return True

        self.logger.debug("路径不是公开路径", path=path)
        return False
    
    async def _authenticate(self, request: Request, settings) -> dict:
        """执行认证"""
        path = request.url.path

        # 记录认证尝试
        self.logger.debug(
            "开始认证过程",
            path=path,
            has_api_key_config=bool(settings.security.api_key),
            has_jwt_config=bool(settings.security.jwt_secret_key)
        )

        # 尝试API密钥认证
        api_key = self._get_api_key(request)
        if api_key and settings.security.api_key:
            if api_key == settings.security.api_key:
                self.logger.debug("API密钥认证成功", path=path)
                return {"type": "api_key", "user_id": "api_user"}
            else:
                self.logger.warning("API密钥认证失败", path=path)
                raise AuthenticationError("无效的API密钥")

        # 尝试JWT认证
        token = self._get_bearer_token(request)
        if token and settings.security.jwt_secret_key:
            try:
                payload = jwt.decode(
                    token,
                    settings.security.jwt_secret_key,
                    algorithms=[settings.security.jwt_algorithm]
                )
                self.logger.debug("JWT认证成功", path=path, user_id=payload.get("sub"))
                return {
                    "type": "jwt",
                    "user_id": payload.get("sub"),
                    "payload": payload
                }
            except jwt.ExpiredSignatureError:
                self.logger.warning("JWT Token已过期", path=path)
                raise AuthenticationError("Token已过期")
            except jwt.InvalidTokenError:
                self.logger.warning("JWT Token无效", path=path)
                raise AuthenticationError("无效的Token")

        # 如果配置了认证但没有提供凭证
        if settings.security.api_key or settings.security.jwt_secret_key:
            self.logger.warning(
                "需要认证但未提供凭证",
                path=path,
                provided_api_key=bool(api_key),
                provided_token=bool(token)
            )
            raise AuthenticationError("需要认证")

        # 没有配置认证，允许访问
        self.logger.debug("未配置认证，允许匿名访问", path=path)
        return {"type": "none", "user_id": "anonymous"}
    
    def _get_api_key(self, request: Request) -> Optional[str]:
        """获取API密钥"""
        # 从头部获取
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return api_key
        
        # 从查询参数获取
        api_key = request.query_params.get("api_key")
        if api_key:
            return api_key
        
        return None
    
    def _get_bearer_token(self, request: Request) -> Optional[str]:
        """获取Bearer Token"""
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            return authorization[7:]  # 移除 "Bearer " 前缀
        
        return None


class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = get_logger("middleware.rate_limit")
        
        # 简单的内存存储（生产环境应使用Redis）
        self._request_counts = {}
        self._last_reset = time.time()
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        # 获取设置
        settings = getattr(request.app.state, "settings", None)
        if not settings or not settings.features.enable_rate_limiting:
            return await call_next(request)
        
        # 获取客户端标识
        client_id = self._get_client_id(request)
        
        # 检查速率限制
        if self._is_rate_limited(client_id, settings):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="请求频率超限，请稍后重试",
                headers={
                    "Retry-After": str(settings.performance.rate_limit_window),
                    "X-RateLimit-Limit": str(settings.performance.rate_limit_requests),
                    "X-RateLimit-Remaining": "0",
                }
            )
        
        # 记录请求
        self._record_request(client_id)
        
        return await call_next(request)
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用用户ID
        user = getattr(request.state, "user", None)
        if user and user.get("user_id"):
            return f"user:{user['user_id']}"
        
        # 使用IP地址
        client_ip = request.client.host if request.client else "unknown"
        
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            client_ip = real_ip
        
        return f"ip:{client_ip}"
    
    def _is_rate_limited(self, client_id: str, settings) -> bool:
        """检查是否超过速率限制"""
        current_time = time.time()
        
        # 重置计数器（简单的滑动窗口）
        if current_time - self._last_reset > settings.performance.rate_limit_window:
            self._request_counts.clear()
            self._last_reset = current_time
        
        # 检查当前请求数
        current_count = self._request_counts.get(client_id, 0)
        return current_count >= settings.performance.rate_limit_requests
    
    def _record_request(self, client_id: str) -> None:
        """记录请求"""
        self._request_counts[client_id] = self._request_counts.get(client_id, 0) + 1


class CacheMiddleware(BaseHTTPMiddleware):
    """缓存中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = get_logger("middleware.cache")
        
        # 可缓存的路径模式
        self.cacheable_paths = [
            "/api/v1/text/models",
            "/api/v1/image/models",
            "/health",
        ]
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        # 只缓存GET请求
        if request.method != "GET":
            return await call_next(request)
        
        # 检查是否可缓存
        if not self._is_cacheable(request.url.path):
            return await call_next(request)
        
        # 获取缓存
        cache = getattr(request.app.state, "cache", None)
        if not cache:
            return await call_next(request)
        
        # 生成缓存键
        cache_key = self._generate_cache_key(request)
        
        try:
            # 尝试从缓存获取
            cached_response = await cache.get(cache_key)
            if cached_response:
                self.logger.debug("返回缓存响应", cache_key=cache_key)
                return Response(
                    content=cached_response["content"],
                    status_code=cached_response["status_code"],
                    headers=cached_response["headers"],
                    media_type=cached_response["media_type"]
                )
            
            # 缓存未命中，处理请求
            response = await call_next(request)
            
            # 缓存响应（仅缓存成功响应）
            if response.status_code == 200:
                await self._cache_response(cache, cache_key, response)
            
            return response
            
        except Exception as e:
            self.logger.warning("缓存操作失败", error=str(e))
            return await call_next(request)
    
    def _is_cacheable(self, path: str) -> bool:
        """检查路径是否可缓存"""
        for cacheable_path in self.cacheable_paths:
            if path.startswith(cacheable_path):
                return True
        return False
    
    def _generate_cache_key(self, request: Request) -> str:
        """生成缓存键"""
        path = request.url.path
        query = str(request.query_params)
        return f"api_cache:{path}:{hash(query)}"
    
    async def _cache_response(self, cache, cache_key: str, response: Response) -> None:
        """缓存响应"""
        try:
            # 读取响应内容
            content = b""
            async for chunk in response.body_iterator:
                content += chunk
            
            # 重新创建响应
            response.body_iterator = iter([content])
            
            # 缓存数据
            cache_data = {
                "content": content.decode("utf-8"),
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "media_type": response.media_type
            }
            
            await cache.set(cache_key, cache_data, ttl=300)  # 5分钟缓存
            
        except Exception as e:
            self.logger.warning("缓存响应失败", error=str(e))
