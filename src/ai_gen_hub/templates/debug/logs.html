{% extends "debug/base.html" %}

{% block title %}日志查看器 - AI Gen Hub 调试仪表板{% endblock %}

{% block page_title %}
    <i class="fas fa-file-alt"></i> 日志查看器
    <small class="text-muted">- 实时日志监控和分析</small>
{% endblock %}

{% block content %}
<!-- 日志统计概览 -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-primary" id="total-logs">--</div>
                <div class="metric-label">总日志数</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-success" id="info-logs">--</div>
                <div class="metric-label">INFO</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-warning" id="warning-logs">--</div>
                <div class="metric-label">WARNING</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-danger" id="error-logs">--</div>
                <div class="metric-label">ERROR</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-dark" id="critical-logs">--</div>
                <div class="metric-label">CRITICAL</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-secondary" id="debug-logs">--</div>
                <div class="metric-label">DEBUG</div>
            </div>
        </div>
    </div>
</div>

<!-- 过滤控制面板 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter"></i> 日志过滤器
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="level-filter" class="form-label">日志级别:</label>
                        <select class="form-select" id="level-filter">
                            <option value="">全部级别</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search-filter" class="form-label">搜索关键词:</label>
                        <input type="text" class="form-control" id="search-filter" placeholder="搜索日志内容...">
                    </div>
                    <div class="col-md-2">
                        <label for="limit-filter" class="form-label">显示条数:</label>
                        <select class="form-select" id="limit-filter">
                            <option value="50">50条</option>
                            <option value="100" selected>100条</option>
                            <option value="200">200条</option>
                            <option value="500">500条</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary btn-custom" onclick="applyFilters()">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button class="btn btn-outline-secondary btn-custom" onclick="clearFilters()">
                                <i class="fas fa-eraser"></i> 清空
                            </button>
                            <button class="btn btn-outline-info btn-custom" onclick="toggleAutoRefresh()">
                                <i class="fas fa-sync-alt" id="auto-refresh-icon"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志内容区域 -->
<div class="row">
    <div class="col-md-9">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> 日志列表
                    <span class="badge bg-secondary" id="logs-count">0</span>
                </h5>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary" onclick="exportLogs()">
                        <i class="fas fa-download"></i> 导出
                    </button>
                    <button class="btn btn-outline-secondary" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="logs-container" style="max-height: 600px; overflow-y: auto;">
                    <div class="text-center p-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载日志中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 日志统计图表 -->
    <div class="col-md-3">
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie"></i> 级别分布
                </h6>
            </div>
            <div class="card-body">
                <canvas id="levelChart" height="200"></canvas>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line"></i> 时间趋势
                </h6>
            </div>
            <div class="card-body">
                <canvas id="timeChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">日志详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="log-detail-content"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.log-entry {
    border-bottom: 1px solid #e2e8f0;
    padding: 12px 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.log-entry:hover {
    background-color: #f8fafc;
}

.log-level {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
    margin-right: 10px;
}

.log-level-DEBUG { background-color: #6b7280; color: white; }
.log-level-INFO { background-color: #3b82f6; color: white; }
.log-level-WARNING { background-color: #f59e0b; color: white; }
.log-level-ERROR { background-color: #ef4444; color: white; }
.log-level-CRITICAL { background-color: #dc2626; color: white; }

.log-timestamp {
    color: #6b7280;
    font-size: 0.8rem;
    margin-right: 10px;
}

.log-logger {
    color: #8b5cf6;
    font-size: 0.8rem;
    margin-right: 10px;
}

.log-message {
    color: #1f2937;
}

.auto-refresh-active {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let logs = [];
let logStats = {};
let levelChart, timeChart;
let autoRefreshInterval;
let isAutoRefresh = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeLogsViewer();
    initializeCharts();
    
    // 绑定搜索框回车事件
    document.getElementById('search-filter').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            applyFilters();
        }
    });
});

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});

// 初始化日志查看器
async function initializeLogsViewer() {
    await loadLogLevels();
    await loadLogStats();
    await loadLogs();
}

// 加载日志级别
async function loadLogLevels() {
    try {
        const data = await fetchData('/debug/api/logs/levels');
        const levelFilter = document.getElementById('level-filter');
        
        // 清空现有选项（保留"全部级别"）
        while (levelFilter.children.length > 1) {
            levelFilter.removeChild(levelFilter.lastChild);
        }
        
        // 添加级别选项
        data.levels.forEach(level => {
            const option = document.createElement('option');
            option.value = level.name;
            option.textContent = `${level.name} (${level.count})`;
            levelFilter.appendChild(option);
        });
        
    } catch (error) {
        console.error('加载日志级别失败:', error);
    }
}

// 加载日志统计
async function loadLogStats() {
    try {
        const data = await fetchData('/debug/api/logs/stats');
        logStats = data;
        
        // 更新统计卡片
        document.getElementById('total-logs').textContent = data.total_logs.toLocaleString();
        document.getElementById('debug-logs').textContent = data.by_level.DEBUG.toLocaleString();
        document.getElementById('info-logs').textContent = data.by_level.INFO.toLocaleString();
        document.getElementById('warning-logs').textContent = data.by_level.WARNING.toLocaleString();
        document.getElementById('error-logs').textContent = data.by_level.ERROR.toLocaleString();
        document.getElementById('critical-logs').textContent = data.by_level.CRITICAL.toLocaleString();
        
        // 更新图表
        updateCharts();
        
    } catch (error) {
        console.error('加载日志统计失败:', error);
    }
}

// 加载日志
async function loadLogs() {
    try {
        const level = document.getElementById('level-filter').value;
        const search = document.getElementById('search-filter').value;
        const limit = document.getElementById('limit-filter').value;
        
        let url = `/debug/api/logs?limit=${limit}`;
        if (level) url += `&level=${level}`;
        if (search) url += `&search=${encodeURIComponent(search)}`;
        
        const data = await fetchData(url);
        logs = data.logs;
        
        renderLogs();
        
    } catch (error) {
        console.error('加载日志失败:', error);
        showError('logs-container', '加载日志失败: ' + error.message);
    }
}

// 渲染日志列表
function renderLogs() {
    const container = document.getElementById('logs-container');
    const countBadge = document.getElementById('logs-count');
    
    countBadge.textContent = logs.length;
    
    if (logs.length === 0) {
        container.innerHTML = '<div class="text-center p-4 text-muted">没有找到匹配的日志</div>';
        return;
    }
    
    let html = '';
    logs.forEach((log, index) => {
        const timestamp = new Date(log.timestamp).toLocaleString('zh-CN');
        
        html += `
            <div class="log-entry" onclick="showLogDetail(${index})">
                <div class="d-flex align-items-center">
                    <span class="log-level log-level-${log.level}">${log.level}</span>
                    <span class="log-timestamp">${timestamp}</span>
                    <span class="log-logger">${log.logger}</span>
                </div>
                <div class="log-message mt-1">${log.message}</div>
                ${log.extra && Object.keys(log.extra).length > 0 ? 
                    `<div class="mt-1"><small class="text-muted">额外信息: ${Object.keys(log.extra).length} 项</small></div>` : 
                    ''
                }
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 显示日志详情
function showLogDetail(index) {
    const log = logs[index];
    const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
    const content = document.getElementById('log-detail-content');
    
    let html = `
        <div class="row mb-3">
            <div class="col-md-3"><strong>时间戳:</strong></div>
            <div class="col-md-9">${new Date(log.timestamp).toLocaleString('zh-CN')}</div>
        </div>
        <div class="row mb-3">
            <div class="col-md-3"><strong>级别:</strong></div>
            <div class="col-md-9"><span class="log-level log-level-${log.level}">${log.level}</span></div>
        </div>
        <div class="row mb-3">
            <div class="col-md-3"><strong>记录器:</strong></div>
            <div class="col-md-9"><code>${log.logger}</code></div>
        </div>
        <div class="row mb-3">
            <div class="col-md-3"><strong>消息:</strong></div>
            <div class="col-md-9">${log.message}</div>
        </div>
    `;
    
    if (log.extra && Object.keys(log.extra).length > 0) {
        html += `
            <div class="row mb-3">
                <div class="col-md-3"><strong>额外信息:</strong></div>
                <div class="col-md-9">
                    <div class="code-block">
                        <pre>${JSON.stringify(log.extra, null, 2)}</pre>
                    </div>
                </div>
            </div>
        `;
    }
    
    content.innerHTML = html;
    modal.show();
}

// 应用过滤器
function applyFilters() {
    loadLogs();
}

// 清空过滤器
function clearFilters() {
    document.getElementById('level-filter').value = '';
    document.getElementById('search-filter').value = '';
    document.getElementById('limit-filter').value = '100';
    loadLogs();
}

// 切换自动刷新
function toggleAutoRefresh() {
    const icon = document.getElementById('auto-refresh-icon');
    
    if (isAutoRefresh) {
        // 停止自动刷新
        clearInterval(autoRefreshInterval);
        isAutoRefresh = false;
        icon.classList.remove('auto-refresh-active');
    } else {
        // 开始自动刷新
        autoRefreshInterval = setInterval(() => {
            loadLogs();
            loadLogStats();
        }, 5000); // 每5秒刷新一次
        isAutoRefresh = true;
        icon.classList.add('auto-refresh-active');
    }
}

// 刷新日志
function refreshLogs() {
    loadLogStats();
    loadLogs();
}

// 导出日志
function exportLogs() {
    if (logs.length === 0) {
        alert('没有日志可以导出');
        return;
    }
    
    // 生成CSV格式的日志数据
    let csvContent = "时间戳,级别,记录器,消息,额外信息\n";
    
    logs.forEach(log => {
        const timestamp = new Date(log.timestamp).toLocaleString('zh-CN');
        const extra = log.extra ? JSON.stringify(log.extra) : '';
        const row = `"${timestamp}","${log.level}","${log.logger}","${log.message}","${extra}"\n`;
        csvContent += row;
    });
    
    // 创建下载链接
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `logs_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 初始化图表
function initializeCharts() {
    // 级别分布饼图
    const levelCtx = document.getElementById('levelChart').getContext('2d');
    levelChart = new Chart(levelCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#6b7280', // DEBUG
                    '#3b82f6', // INFO
                    '#f59e0b', // WARNING
                    '#ef4444', // ERROR
                    '#dc2626'  // CRITICAL
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        fontSize: 10
                    }
                }
            }
        }
    });
    
    // 时间趋势图
    const timeCtx = document.getElementById('timeChart').getContext('2d');
    timeChart = new Chart(timeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '日志数量',
                data: [],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// 更新图表
function updateCharts() {
    if (!logStats.by_level) return;
    
    // 更新级别分布图
    const levels = Object.keys(logStats.by_level);
    const counts = Object.values(logStats.by_level);
    
    levelChart.data.labels = levels;
    levelChart.data.datasets[0].data = counts;
    levelChart.update();
    
    // 更新时间趋势图
    if (logStats.by_hour) {
        const hours = logStats.by_hour.map(item => item.hour);
        const hourCounts = logStats.by_hour.map(item => item.count);
        
        timeChart.data.labels = hours;
        timeChart.data.datasets[0].data = hourCounts;
        timeChart.update();
    }
}
</script>
{% endblock %}
