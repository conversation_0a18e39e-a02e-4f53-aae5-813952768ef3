{% extends "debug/base.html" %}

{% block title %}开发工具 - AI Gen Hub 调试仪表板{% endblock %}

{% block page_title %}
    <i class="fas fa-tools"></i> 开发工具
    <small class="text-muted">- 开发和维护工具集</small>
{% endblock %}

{% block content %}
<!-- 工具概览 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning alert-custom">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>注意：</strong> 这些工具可能会影响应用的运行状态，请谨慎使用。建议在非生产环境中操作。
        </div>
    </div>
</div>

<!-- 缓存管理 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-memory"></i> 缓存管理
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>缓存状态</h6>
                    <div id="cache-status">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">检查缓存状态...</p>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-warning btn-custom" onclick="clearCache('all')">
                        <i class="fas fa-trash"></i> 清空所有缓存
                    </button>
                    <button class="btn btn-outline-warning btn-custom" onclick="clearCache('expired')">
                        <i class="fas fa-clock"></i> 清理过期缓存
                    </button>
                    <button class="btn btn-outline-info btn-custom" onclick="refreshCacheStats()">
                        <i class="fas fa-sync-alt"></i> 刷新缓存统计
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i> 配置管理
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>配置状态</h6>
                    <div id="config-status">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">检查配置状态...</p>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-primary btn-custom" onclick="reloadConfig()">
                        <i class="fas fa-redo"></i> 重新加载配置
                    </button>
                    <button class="btn btn-outline-secondary btn-custom" onclick="validateConfig()">
                        <i class="fas fa-check"></i> 验证配置
                    </button>
                    <button class="btn btn-outline-info btn-custom" onclick="exportConfig()">
                        <i class="fas fa-download"></i> 导出配置
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据库工具 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database"></i> 数据库工具
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>数据库状态</h6>
                    <div id="database-status">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">检查数据库状态...</p>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-success btn-custom" onclick="testDatabaseConnection()">
                        <i class="fas fa-plug"></i> 测试数据库连接
                    </button>
                    <button class="btn btn-outline-primary btn-custom" onclick="checkMigrations()">
                        <i class="fas fa-list"></i> 检查迁移状态
                    </button>
                    <button class="btn btn-outline-warning btn-custom" onclick="optimizeDatabase()">
                        <i class="fas fa-wrench"></i> 优化数据库
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> 监控工具
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>监控状态</h6>
                    <div id="monitoring-status">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">检查监控状态...</p>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-info btn-custom" onclick="resetMetrics()">
                        <i class="fas fa-chart-line"></i> 重置性能指标
                    </button>
                    <button class="btn btn-outline-success btn-custom" onclick="generateReport()">
                        <i class="fas fa-file-alt"></i> 生成健康报告
                    </button>
                    <button class="btn btn-outline-secondary btn-custom" onclick="exportLogs()">
                        <i class="fas fa-download"></i> 导出日志
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统维护 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-wrench"></i> 系统维护
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-primary btn-custom" onclick="runHealthCheck()">
                                <i class="fas fa-heartbeat"></i><br>
                                <small>运行健康检查</small>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-success btn-custom" onclick="cleanupTempFiles()">
                                <i class="fas fa-broom"></i><br>
                                <small>清理临时文件</small>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-warning btn-custom" onclick="restartServices()">
                                <i class="fas fa-redo"></i><br>
                                <small>重启服务</small>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-danger btn-custom" onclick="emergencyMode()">
                                <i class="fas fa-exclamation-triangle"></i><br>
                                <small>紧急模式</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作日志 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> 操作日志
                </h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearOperationLog()">
                    <i class="fas fa-trash"></i> 清空日志
                </button>
            </div>
            <div class="card-body">
                <div id="operation-log" style="max-height: 300px; overflow-y: auto;">
                    <div class="text-center text-muted">
                        <p>暂无操作记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认对话框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirm-message">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirm-action">确认</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.operation-log-entry {
    border-bottom: 1px solid #e2e8f0;
    padding: 10px 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.operation-log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    color: #6b7280;
    font-size: 0.8rem;
}

.log-action {
    font-weight: bold;
    color: #374151;
}

.log-result-success {
    color: #10b981;
}

.log-result-error {
    color: #ef4444;
}

.log-result-warning {
    color: #f59e0b;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-healthy {
    background-color: #10b981;
}

.status-warning {
    background-color: #f59e0b;
}

.status-error {
    background-color: #ef4444;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let operationLog = [];
let confirmCallback = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTools();
    loadOperationLog();
});

// 初始化工具
async function initializeTools() {
    await updateCacheStatus();
    await updateConfigStatus();
    await updateDatabaseStatus();
    await updateMonitoringStatus();
}

// 更新缓存状态
async function updateCacheStatus() {
    try {
        // 模拟缓存状态数据
        const status = {
            connected: true,
            total_keys: Math.floor(Math.random() * 10000 + 1000),
            memory_usage: Math.floor(Math.random() * 100 + 50),
            hit_rate: (Math.random() * 30 + 70).toFixed(1)
        };
        
        const container = document.getElementById('cache-status');
        container.innerHTML = `
            <div class="d-flex align-items-center mb-2">
                <span class="status-indicator status-${status.connected ? 'healthy' : 'error'}"></span>
                <span>连接状态: ${status.connected ? '已连接' : '断开'}</span>
            </div>
            <div class="row">
                <div class="col-6">
                    <small>总键数: ${status.total_keys.toLocaleString()}</small>
                </div>
                <div class="col-6">
                    <small>内存使用: ${status.memory_usage} MB</small>
                </div>
                <div class="col-12">
                    <small>命中率: ${status.hit_rate}%</small>
                </div>
            </div>
        `;
        
    } catch (error) {
        showError('cache-status', '获取缓存状态失败');
    }
}

// 更新配置状态
async function updateConfigStatus() {
    try {
        const status = {
            last_reload: new Date(Date.now() - Math.random() * 3600000).toLocaleString('zh-CN'),
            valid: true,
            environment: 'development'
        };
        
        const container = document.getElementById('config-status');
        container.innerHTML = `
            <div class="d-flex align-items-center mb-2">
                <span class="status-indicator status-${status.valid ? 'healthy' : 'error'}"></span>
                <span>配置状态: ${status.valid ? '有效' : '无效'}</span>
            </div>
            <div>
                <small>最后重载: ${status.last_reload}</small><br>
                <small>环境: ${status.environment}</small>
            </div>
        `;
        
    } catch (error) {
        showError('config-status', '获取配置状态失败');
    }
}

// 更新数据库状态
async function updateDatabaseStatus() {
    try {
        const status = {
            connected: true,
            active_connections: Math.floor(Math.random() * 20 + 5),
            pending_migrations: Math.floor(Math.random() * 3)
        };
        
        const container = document.getElementById('database-status');
        container.innerHTML = `
            <div class="d-flex align-items-center mb-2">
                <span class="status-indicator status-${status.connected ? 'healthy' : 'error'}"></span>
                <span>连接状态: ${status.connected ? '已连接' : '断开'}</span>
            </div>
            <div>
                <small>活跃连接: ${status.active_connections}</small><br>
                <small>待执行迁移: ${status.pending_migrations}</small>
            </div>
        `;
        
    } catch (error) {
        showError('database-status', '获取数据库状态失败');
    }
}

// 更新监控状态
async function updateMonitoringStatus() {
    try {
        const status = {
            prometheus_enabled: true,
            metrics_count: Math.floor(Math.random() * 1000 + 500),
            last_export: new Date(Date.now() - Math.random() * 300000).toLocaleString('zh-CN')
        };
        
        const container = document.getElementById('monitoring-status');
        container.innerHTML = `
            <div class="d-flex align-items-center mb-2">
                <span class="status-indicator status-${status.prometheus_enabled ? 'healthy' : 'warning'}"></span>
                <span>Prometheus: ${status.prometheus_enabled ? '已启用' : '已禁用'}</span>
            </div>
            <div>
                <small>指标数量: ${status.metrics_count}</small><br>
                <small>最后导出: ${status.last_export}</small>
            </div>
        `;
        
    } catch (error) {
        showError('monitoring-status', '获取监控状态失败');
    }
}

// 清除缓存
function clearCache(type) {
    const message = type === 'all' ? 
        '确定要清空所有缓存吗？这将影响应用性能。' : 
        '确定要清理过期缓存吗？';
    
    showConfirmDialog(message, async () => {
        try {
            // 模拟清除缓存操作
            await simulateOperation('清除缓存', type);
            logOperation('清除缓存', `类型: ${type}`, 'success');
            await updateCacheStatus();
        } catch (error) {
            logOperation('清除缓存', error.message, 'error');
        }
    });
}

// 重新加载配置
function reloadConfig() {
    showConfirmDialog('确定要重新加载配置吗？', async () => {
        try {
            await simulateOperation('重新加载配置');
            logOperation('重新加载配置', '配置已成功重载', 'success');
            await updateConfigStatus();
        } catch (error) {
            logOperation('重新加载配置', error.message, 'error');
        }
    });
}

// 验证配置
async function validateConfig() {
    try {
        await simulateOperation('验证配置');
        logOperation('验证配置', '配置验证通过', 'success');
    } catch (error) {
        logOperation('验证配置', error.message, 'error');
    }
}

// 导出配置
async function exportConfig() {
    try {
        await simulateOperation('导出配置');
        
        // 创建模拟配置文件
        const config = {
            app_name: "AI Gen Hub",
            environment: "development",
            debug: true,
            exported_at: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `config_${new Date().toISOString().slice(0, 10)}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        logOperation('导出配置', '配置文件已导出', 'success');
    } catch (error) {
        logOperation('导出配置', error.message, 'error');
    }
}

// 测试数据库连接
async function testDatabaseConnection() {
    try {
        await simulateOperation('测试数据库连接');
        logOperation('测试数据库连接', '数据库连接正常', 'success');
        await updateDatabaseStatus();
    } catch (error) {
        logOperation('测试数据库连接', error.message, 'error');
    }
}

// 检查迁移状态
async function checkMigrations() {
    try {
        await simulateOperation('检查迁移状态');
        const pendingCount = Math.floor(Math.random() * 3);
        const message = pendingCount > 0 ? 
            `发现 ${pendingCount} 个待执行的迁移` : 
            '所有迁移已执行完成';
        logOperation('检查迁移状态', message, pendingCount > 0 ? 'warning' : 'success');
    } catch (error) {
        logOperation('检查迁移状态', error.message, 'error');
    }
}

// 优化数据库
function optimizeDatabase() {
    showConfirmDialog('确定要优化数据库吗？这可能需要一些时间。', async () => {
        try {
            await simulateOperation('优化数据库', null, 3000);
            logOperation('优化数据库', '数据库优化完成', 'success');
        } catch (error) {
            logOperation('优化数据库', error.message, 'error');
        }
    });
}

// 重置性能指标
function resetMetrics() {
    showConfirmDialog('确定要重置所有性能指标吗？', async () => {
        try {
            await simulateOperation('重置性能指标');
            logOperation('重置性能指标', '性能指标已重置', 'success');
        } catch (error) {
            logOperation('重置性能指标', error.message, 'error');
        }
    });
}

// 生成健康报告
async function generateReport() {
    try {
        await simulateOperation('生成健康报告');
        
        // 创建模拟报告
        const report = {
            timestamp: new Date().toISOString(),
            system_health: "healthy",
            database_status: "connected",
            cache_status: "operational",
            performance_summary: "good"
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `health_report_${new Date().toISOString().slice(0, 10)}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        logOperation('生成健康报告', '健康报告已生成', 'success');
    } catch (error) {
        logOperation('生成健康报告', error.message, 'error');
    }
}

// 运行健康检查
async function runHealthCheck() {
    try {
        await simulateOperation('运行健康检查');
        logOperation('运行健康检查', '系统健康状态良好', 'success');
    } catch (error) {
        logOperation('运行健康检查', error.message, 'error');
    }
}

// 清理临时文件
async function cleanupTempFiles() {
    try {
        await simulateOperation('清理临时文件');
        const cleanedSize = (Math.random() * 100 + 10).toFixed(1);
        logOperation('清理临时文件', `已清理 ${cleanedSize} MB 临时文件`, 'success');
    } catch (error) {
        logOperation('清理临时文件', error.message, 'error');
    }
}

// 重启服务
function restartServices() {
    showConfirmDialog('确定要重启服务吗？这将导致短暂的服务中断。', async () => {
        try {
            await simulateOperation('重启服务', null, 5000);
            logOperation('重启服务', '服务已重启', 'success');
        } catch (error) {
            logOperation('重启服务', error.message, 'error');
        }
    });
}

// 紧急模式
function emergencyMode() {
    showConfirmDialog('确定要启用紧急模式吗？这将禁用非关键功能。', async () => {
        try {
            await simulateOperation('启用紧急模式');
            logOperation('紧急模式', '紧急模式已启用', 'warning');
        } catch (error) {
            logOperation('紧急模式', error.message, 'error');
        }
    });
}

// 刷新缓存统计
async function refreshCacheStats() {
    await updateCacheStatus();
    logOperation('刷新缓存统计', '缓存统计已更新', 'success');
}

// 模拟操作
async function simulateOperation(operation, type = null, delay = 1000) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            // 90% 成功率
            if (Math.random() > 0.1) {
                resolve();
            } else {
                reject(new Error(`${operation}失败: 模拟错误`));
            }
        }, delay);
    });
}

// 记录操作日志
function logOperation(action, result, type = 'info') {
    const entry = {
        timestamp: new Date().toLocaleString('zh-CN'),
        action: action,
        result: result,
        type: type
    };
    
    operationLog.unshift(entry);
    
    // 只保留最近50条记录
    if (operationLog.length > 50) {
        operationLog = operationLog.slice(0, 50);
    }
    
    saveOperationLog();
    renderOperationLog();
}

// 渲染操作日志
function renderOperationLog() {
    const container = document.getElementById('operation-log');
    
    if (operationLog.length === 0) {
        container.innerHTML = '<div class="text-center text-muted"><p>暂无操作记录</p></div>';
        return;
    }
    
    let html = '';
    operationLog.forEach(entry => {
        html += `
            <div class="operation-log-entry">
                <div class="log-timestamp">${entry.timestamp}</div>
                <div class="log-action">${entry.action}</div>
                <div class="log-result-${entry.type}">${entry.result}</div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 保存操作日志到本地存储
function saveOperationLog() {
    localStorage.setItem('debug_operation_log', JSON.stringify(operationLog));
}

// 加载操作日志
function loadOperationLog() {
    const saved = localStorage.getItem('debug_operation_log');
    if (saved) {
        operationLog = JSON.parse(saved);
        renderOperationLog();
    }
}

// 清空操作日志
function clearOperationLog() {
    operationLog = [];
    saveOperationLog();
    renderOperationLog();
}

// 显示确认对话框
function showConfirmDialog(message, callback) {
    document.getElementById('confirm-message').textContent = message;
    confirmCallback = callback;
    
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
}

// 确认操作
document.getElementById('confirm-action').addEventListener('click', function() {
    if (confirmCallback) {
        confirmCallback();
        confirmCallback = null;
    }
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
    modal.hide();
});
</script>
{% endblock %}
