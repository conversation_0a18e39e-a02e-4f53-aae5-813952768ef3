{% extends "debug/base.html" %}

{% block title %}系统状态监控 - AI Gen Hub 调试仪表板{% endblock %}

{% block page_title %}
    <i class="fas fa-server"></i> 系统状态监控
    <small class="text-muted">- 实时系统资源监控</small>
{% endblock %}

{% block content %}
<!-- 系统概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-primary" id="cpu-usage">--</div>
                <div class="metric-label">CPU 使用率</div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar progress-bar-custom bg-primary" id="cpu-progress" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-1" id="cpu-details">--</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-success" id="memory-usage">--</div>
                <div class="metric-label">内存使用率</div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar progress-bar-custom bg-success" id="memory-progress" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-1" id="memory-details">--</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-warning" id="disk-usage">--</div>
                <div class="metric-label">磁盘使用率</div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar progress-bar-custom bg-warning" id="disk-progress" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-1" id="disk-details">--</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-info" id="uptime">--</div>
                <div class="metric-label">系统运行时间</div>
                <div class="mt-2">
                    <small class="text-muted" id="process-count">-- 个进程</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细信息和图表 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area"></i> 系统性能趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="systemChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-microchip"></i> CPU 详情
                </h5>
            </div>
            <div class="card-body" id="cpu-details-panel">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 内存和磁盘详情 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-memory"></i> 内存详情
                </h5>
            </div>
            <div class="card-body" id="memory-details-panel">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">加载中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-hdd"></i> 磁盘详情
                </h5>
            </div>
            <div class="card-body" id="disk-details-panel">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 进程列表 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> 系统进程
                </h5>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary" onclick="sortProcesses('cpu_percent')">按CPU排序</button>
                    <button class="btn btn-outline-secondary" onclick="sortProcesses('memory_percent')">按内存排序</button>
                    <button class="btn btn-outline-secondary" onclick="refreshProcesses()">刷新</button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-custom" id="processes-table">
                        <thead>
                            <tr>
                                <th>PID</th>
                                <th>进程名</th>
                                <th>CPU %</th>
                                <th>内存 %</th>
                                <th>状态</th>
                                <th>启动时间</th>
                                <th>命令行</th>
                            </tr>
                        </thead>
                        <tbody id="processes-tbody">
                            <tr>
                                <td colspan="7" class="text-center">
                                    <div class="loading-spinner"></div> 加载进程列表...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 网络和服务状态 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-network-wired"></i> 网络状态
                </h5>
            </div>
            <div class="card-body" id="network-details-panel">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">加载中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database"></i> 服务状态
                </h5>
            </div>
            <div class="card-body" id="services-status-panel">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let systemChart;
let updateInterval;
let currentSortBy = 'cpu_percent';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeSystemMonitor();
    initializeChart();
    
    // 每10秒更新一次数据
    updateInterval = setInterval(updateSystemData, 10000);
});

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
});

// 初始化系统监控
async function initializeSystemMonitor() {
    await updateSystemData();
    await updateDetailedInfo();
    await updateProcesses();
    await updateServicesStatus();
}

// 更新系统数据
async function updateSystemData() {
    try {
        const data = await fetchData('/debug/api/system/info');
        updateSystemMetrics(data.system);
        updateSystemChart(data.system);
    } catch (error) {
        console.error('更新系统数据失败:', error);
    }
}

// 更新系统指标
function updateSystemMetrics(system) {
    // CPU
    document.getElementById('cpu-usage').textContent = system.cpu_percent.toFixed(1) + '%';
    document.getElementById('cpu-progress').style.width = system.cpu_percent + '%';
    document.getElementById('cpu-details').textContent = `${system.cpu_percent.toFixed(1)}% 使用中`;
    
    // 内存
    document.getElementById('memory-usage').textContent = system.memory_percent.toFixed(1) + '%';
    document.getElementById('memory-progress').style.width = system.memory_percent + '%';
    document.getElementById('memory-details').textContent = 
        `${formatBytes(system.memory_total - system.memory_available)} / ${formatBytes(system.memory_total)}`;
    
    // 磁盘
    document.getElementById('disk-usage').textContent = system.disk_percent.toFixed(1) + '%';
    document.getElementById('disk-progress').style.width = system.disk_percent + '%';
    document.getElementById('disk-details').textContent = 
        `${formatBytes(system.disk_total - system.disk_free)} / ${formatBytes(system.disk_total)}`;
    
    // 运行时间
    document.getElementById('uptime').textContent = formatDuration(system.uptime);
    document.getElementById('process-count').textContent = system.process_count + ' 个进程';
}

// 更新详细信息
async function updateDetailedInfo() {
    try {
        const data = await fetchData('/debug/api/system/detailed');
        
        if (data.error) {
            showError('cpu-details-panel', data.error);
            showError('memory-details-panel', data.error);
            showError('disk-details-panel', data.error);
            showError('network-details-panel', data.error);
            return;
        }
        
        updateCPUDetails(data.cpu);
        updateMemoryDetails(data.memory);
        updateDiskDetails(data.disk);
        updateNetworkDetails(data.network);
        
    } catch (error) {
        console.error('更新详细信息失败:', error);
    }
}

// 更新CPU详情
function updateCPUDetails(cpu) {
    const panel = document.getElementById('cpu-details-panel');
    
    let html = `
        <div class="row">
            <div class="col-6">
                <strong>物理核心:</strong><br>
                <span class="text-primary">${cpu.count}</span>
            </div>
            <div class="col-6">
                <strong>逻辑核心:</strong><br>
                <span class="text-primary">${cpu.count_logical}</span>
            </div>
        </div>
    `;
    
    if (cpu.freq) {
        html += `
            <hr>
            <div class="row">
                <div class="col-12">
                    <strong>频率:</strong><br>
                    <small>当前: ${(cpu.freq.current / 1000).toFixed(2)} GHz</small><br>
                    <small>最大: ${(cpu.freq.max / 1000).toFixed(2)} GHz</small>
                </div>
            </div>
        `;
    }
    
    if (cpu.percent_per_cpu && cpu.percent_per_cpu.length > 0) {
        html += '<hr><strong>各核心使用率:</strong><br>';
        cpu.percent_per_cpu.forEach((percent, index) => {
            html += `
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <small>核心 ${index + 1}:</small>
                    <div class="progress progress-custom" style="width: 60px; height: 6px;">
                        <div class="progress-bar bg-primary" style="width: ${percent}%"></div>
                    </div>
                    <small>${percent.toFixed(1)}%</small>
                </div>
            `;
        });
    }
    
    panel.innerHTML = html;
}

// 更新内存详情
function updateMemoryDetails(memory) {
    const panel = document.getElementById('memory-details-panel');
    const vm = memory.virtual;
    
    const html = `
        <div class="mb-3">
            <strong>虚拟内存:</strong>
            <div class="progress progress-custom mt-1">
                <div class="progress-bar bg-success" style="width: ${vm.percent}%"></div>
            </div>
            <small class="text-muted">
                已用: ${formatBytes(vm.used)} / 总计: ${formatBytes(vm.total)}
            </small>
        </div>
        
        <div class="row">
            <div class="col-6">
                <strong>可用:</strong><br>
                <span class="text-success">${formatBytes(vm.available)}</span>
            </div>
            <div class="col-6">
                <strong>缓存:</strong><br>
                <span class="text-info">${formatBytes(vm.cached || 0)}</span>
            </div>
        </div>
        
        ${memory.swap ? `
            <hr>
            <div class="mb-2">
                <strong>交换分区:</strong>
                <div class="progress progress-custom mt-1">
                    <div class="progress-bar bg-warning" style="width: ${memory.swap.percent}%"></div>
                </div>
                <small class="text-muted">
                    已用: ${formatBytes(memory.swap.used)} / 总计: ${formatBytes(memory.swap.total)}
                </small>
            </div>
        ` : ''}
    `;
    
    panel.innerHTML = html;
}

// 更新磁盘详情
function updateDiskDetails(disk) {
    const panel = document.getElementById('disk-details-panel');
    
    let html = `
        <div class="mb-3">
            <strong>根分区 (/):</strong>
            <div class="progress progress-custom mt-1">
                <div class="progress-bar bg-warning" style="width: ${disk.usage.percent}%"></div>
            </div>
            <small class="text-muted">
                已用: ${formatBytes(disk.usage.used)} / 总计: ${formatBytes(disk.usage.total)}
            </small>
        </div>
    `;
    
    if (disk.io_counters) {
        html += `
            <div class="row">
                <div class="col-6">
                    <strong>读取:</strong><br>
                    <span class="text-primary">${formatBytes(disk.io_counters.read_bytes)}</span>
                </div>
                <div class="col-6">
                    <strong>写入:</strong><br>
                    <span class="text-danger">${formatBytes(disk.io_counters.write_bytes)}</span>
                </div>
            </div>
        `;
    }
    
    if (disk.partitions && disk.partitions.length > 1) {
        html += '<hr><strong>其他分区:</strong><br>';
        disk.partitions.slice(1, 4).forEach(partition => {
            html += `<small>${partition.device} (${partition.fstype})</small><br>`;
        });
    }
    
    panel.innerHTML = html;
}

// 更新网络详情
function updateNetworkDetails(network) {
    const panel = document.getElementById('network-details-panel');
    
    let html = '';
    
    if (network.io_counters) {
        html += `
            <div class="row mb-3">
                <div class="col-6">
                    <strong>接收:</strong><br>
                    <span class="text-success">${formatBytes(network.io_counters.bytes_recv)}</span>
                </div>
                <div class="col-6">
                    <strong>发送:</strong><br>
                    <span class="text-primary">${formatBytes(network.io_counters.bytes_sent)}</span>
                </div>
            </div>
        `;
    }
    
    html += `
        <div class="mb-3">
            <strong>活动连接:</strong>
            <span class="badge bg-info">${network.connections}</span>
        </div>
    `;
    
    if (network.interfaces) {
        html += '<strong>网络接口:</strong><br>';
        const interfaces = Object.keys(network.interfaces).slice(0, 3);
        interfaces.forEach(name => {
            html += `<small class="text-muted">${name}</small><br>`;
        });
    }
    
    panel.innerHTML = html;
}

// 更新进程列表
async function updateProcesses() {
    try {
        const data = await fetchData(`/debug/api/system/processes?sort_by=${currentSortBy}&limit=20`);
        
        if (data.error) {
            showError('processes-tbody', data.error);
            return;
        }
        
        const tbody = document.getElementById('processes-tbody');
        let html = '';
        
        data.processes.forEach(proc => {
            const statusClass = proc.status === 'running' ? 'success' : 
                               proc.status === 'sleeping' ? 'secondary' : 'warning';
            
            html += `
                <tr>
                    <td>${proc.pid}</td>
                    <td>${proc.name}</td>
                    <td><span class="badge bg-primary">${(proc.cpu_percent || 0).toFixed(1)}%</span></td>
                    <td><span class="badge bg-success">${(proc.memory_percent || 0).toFixed(1)}%</span></td>
                    <td><span class="badge bg-${statusClass}">${proc.status}</span></td>
                    <td><small>${new Date(proc.create_time).toLocaleString('zh-CN')}</small></td>
                    <td><small class="text-muted">${proc.cmdline || '-'}</small></td>
                </tr>
            `;
        });
        
        tbody.innerHTML = html;
        
    } catch (error) {
        console.error('更新进程列表失败:', error);
        showError('processes-tbody', '获取进程列表失败: ' + error.message);
    }
}

// 更新服务状态
async function updateServicesStatus() {
    try {
        const [dbStatus, cacheStatus] = await Promise.all([
            fetchData('/debug/api/database/status'),
            fetchData('/debug/api/cache/status')
        ]);
        
        const panel = document.getElementById('services-status-panel');
        
        let html = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <span><i class="fas fa-database"></i> 数据库</span>
                <span class="status-badge ${getStatusClass(dbStatus.status)}">${getStatusText(dbStatus.status)}</span>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mb-3">
                <span><i class="fas fa-memory"></i> 缓存</span>
                <span class="status-badge ${getStatusClass(cacheStatus.status)}">${getStatusText(cacheStatus.status)}</span>
            </div>
        `;
        
        if (cacheStatus.stats) {
            html += `
                <hr>
                <div class="row">
                    <div class="col-6">
                        <strong>缓存命中率:</strong><br>
                        <span class="text-success">${(cacheStatus.stats.hit_rate * 100).toFixed(1)}%</span>
                    </div>
                    <div class="col-6">
                        <strong>缓存大小:</strong><br>
                        <span class="text-info">${formatBytes(cacheStatus.stats.total_size)}</span>
                    </div>
                </div>
            `;
        }
        
        panel.innerHTML = html;
        
    } catch (error) {
        console.error('更新服务状态失败:', error);
        showError('services-status-panel', '获取服务状态失败: ' + error.message);
    }
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case 'available':
        case 'configured':
            return 'status-healthy';
        case 'not_configured':
        case 'not_available':
            return 'status-warning';
        default:
            return 'status-error';
    }
}

// 获取状态文本
function getStatusText(status) {
    switch (status) {
        case 'available':
            return '可用';
        case 'configured':
            return '已配置';
        case 'not_configured':
            return '未配置';
        case 'not_available':
            return '不可用';
        default:
            return '错误';
    }
}

// 排序进程
function sortProcesses(sortBy) {
    currentSortBy = sortBy;
    updateProcesses();
}

// 刷新进程列表
function refreshProcesses() {
    updateProcesses();
}

// 初始化图表
function initializeChart() {
    const ctx = document.getElementById('systemChart').getContext('2d');
    
    systemChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'CPU使用率 (%)',
                    data: [],
                    borderColor: 'rgb(37, 99, 235)',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4
                },
                {
                    label: '内存使用率 (%)',
                    data: [],
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                },
                {
                    label: '磁盘使用率 (%)',
                    data: [],
                    borderColor: 'rgb(245, 158, 11)',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
}

// 更新系统图表
function updateSystemChart(system) {
    if (!systemChart) return;
    
    const now = new Date().toLocaleTimeString('zh-CN');
    const maxDataPoints = 30;
    
    // 添加新数据点
    systemChart.data.labels.push(now);
    systemChart.data.datasets[0].data.push(system.cpu_percent);
    systemChart.data.datasets[1].data.push(system.memory_percent);
    systemChart.data.datasets[2].data.push(system.disk_percent);
    
    // 保持最大数据点数量
    if (systemChart.data.labels.length > maxDataPoints) {
        systemChart.data.labels.shift();
        systemChart.data.datasets[0].data.shift();
        systemChart.data.datasets[1].data.shift();
        systemChart.data.datasets[2].data.shift();
    }
    
    systemChart.update('none');
}
</script>
{% endblock %}
