{% extends "debug/base.html" %}

{% block title %}性能指标 - AI Gen Hub 调试仪表板{% endblock %}

{% block page_title %}
    <i class="fas fa-chart-line"></i> 性能指标
    <small class="text-muted">- 应用性能监控和分析</small>
{% endblock %}

{% block content %}
<!-- 性能概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-primary" id="avg-response-time">--</div>
                <div class="metric-label">平均响应时间 (ms)</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-success" id="requests-per-minute">--</div>
                <div class="metric-label">每分钟请求数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-warning" id="error-rate">--</div>
                <div class="metric-label">错误率 (%)</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-info" id="active-connections">--</div>
                <div class="metric-label">活跃连接数</div>
            </div>
        </div>
    </div>
</div>

<!-- 性能图表 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area"></i> 响应时间趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="responseTimeChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> 状态码分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusCodeChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 详细指标 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt"></i> 端点性能排行
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-custom">
                        <thead>
                            <tr>
                                <th>端点</th>
                                <th>平均响应时间</th>
                                <th>请求次数</th>
                                <th>错误率</th>
                            </tr>
                        </thead>
                        <tbody id="endpoint-metrics">
                            <tr>
                                <td colspan="4" class="text-center">
                                    <div class="loading-spinner"></div> 加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> 错误统计
                </h5>
            </div>
            <div class="card-body">
                <div id="error-stats">
                    <div class="text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载错误统计...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 缓存性能 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-memory"></i> 缓存性能
                </h5>
            </div>
            <div class="card-body">
                <canvas id="cacheChart" height="150"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database"></i> 数据库性能
                </h5>
            </div>
            <div class="card-body" id="database-metrics">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">加载数据库指标...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let responseTimeChart, statusCodeChart, cacheChart;
let metricsData = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeMetrics();
    initializeCharts();
    
    // 每30秒更新一次数据
    setInterval(updateMetrics, 30000);
});

// 初始化性能指标
async function initializeMetrics() {
    await updateMetrics();
}

// 更新性能指标
async function updateMetrics() {
    try {
        // 模拟性能数据
        const data = generateMockMetrics();
        metricsData = data;
        
        updateMetricCards(data);
        updateEndpointMetrics(data.endpoints);
        updateErrorStats(data.errors);
        updateDatabaseMetrics(data.database);
        updateCharts();
        
    } catch (error) {
        console.error('更新性能指标失败:', error);
    }
}

// 生成模拟性能数据
function generateMockMetrics() {
    return {
        overview: {
            avg_response_time: Math.random() * 500 + 100,
            requests_per_minute: Math.floor(Math.random() * 1000 + 500),
            error_rate: Math.random() * 5,
            active_connections: Math.floor(Math.random() * 100 + 50)
        },
        endpoints: [
            {
                path: '/api/v1/text/generate',
                avg_response_time: Math.random() * 1000 + 200,
                request_count: Math.floor(Math.random() * 1000 + 100),
                error_rate: Math.random() * 3
            },
            {
                path: '/api/v1/image/generate',
                avg_response_time: Math.random() * 2000 + 500,
                request_count: Math.floor(Math.random() * 500 + 50),
                error_rate: Math.random() * 5
            },
            {
                path: '/health',
                avg_response_time: Math.random() * 50 + 10,
                request_count: Math.floor(Math.random() * 200 + 50),
                error_rate: 0
            }
        ],
        errors: {
            total: Math.floor(Math.random() * 100 + 10),
            by_type: {
                '4xx': Math.floor(Math.random() * 50 + 5),
                '5xx': Math.floor(Math.random() * 20 + 2),
                'timeout': Math.floor(Math.random() * 10 + 1),
                'connection': Math.floor(Math.random() * 5)
            }
        },
        database: {
            avg_query_time: Math.random() * 100 + 20,
            active_connections: Math.floor(Math.random() * 20 + 5),
            slow_queries: Math.floor(Math.random() * 5)
        },
        cache: {
            hit_rate: Math.random() * 30 + 70,
            miss_rate: Math.random() * 30,
            eviction_rate: Math.random() * 10
        }
    };
}

// 更新指标卡片
function updateMetricCards(data) {
    document.getElementById('avg-response-time').textContent = 
        data.overview.avg_response_time.toFixed(1);
    document.getElementById('requests-per-minute').textContent = 
        data.overview.requests_per_minute.toLocaleString();
    document.getElementById('error-rate').textContent = 
        data.overview.error_rate.toFixed(2);
    document.getElementById('active-connections').textContent = 
        data.overview.active_connections;
}

// 更新端点指标
function updateEndpointMetrics(endpoints) {
    const tbody = document.getElementById('endpoint-metrics');
    
    let html = '';
    endpoints.forEach(endpoint => {
        const errorRateClass = endpoint.error_rate > 2 ? 'text-danger' : 
                              endpoint.error_rate > 1 ? 'text-warning' : 'text-success';
        
        html += `
            <tr>
                <td><code>${endpoint.path}</code></td>
                <td>${endpoint.avg_response_time.toFixed(1)} ms</td>
                <td>${endpoint.request_count.toLocaleString()}</td>
                <td class="${errorRateClass}">${endpoint.error_rate.toFixed(2)}%</td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// 更新错误统计
function updateErrorStats(errors) {
    const container = document.getElementById('error-stats');
    
    let html = `
        <div class="row mb-3">
            <div class="col-6">
                <h6>总错误数:</h6>
                <span class="fs-4 text-danger">${errors.total}</span>
            </div>
            <div class="col-6">
                <h6>错误类型分布:</h6>
            </div>
        </div>
        
        <div class="row">
    `;
    
    Object.entries(errors.by_type).forEach(([type, count]) => {
        const percentage = (count / errors.total * 100).toFixed(1);
        html += `
            <div class="col-6 mb-2">
                <div class="d-flex justify-content-between">
                    <span>${type}:</span>
                    <span>${count} (${percentage}%)</span>
                </div>
                <div class="progress progress-custom">
                    <div class="progress-bar bg-danger" style="width: ${percentage}%"></div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// 更新数据库指标
function updateDatabaseMetrics(database) {
    const container = document.getElementById('database-metrics');
    
    const html = `
        <div class="row">
            <div class="col-6">
                <div class="metric-item">
                    <span class="metric-key">平均查询时间:</span>
                    <span class="metric-value">${database.avg_query_time.toFixed(1)} ms</span>
                </div>
            </div>
            <div class="col-6">
                <div class="metric-item">
                    <span class="metric-key">活跃连接:</span>
                    <span class="metric-value">${database.active_connections}</span>
                </div>
            </div>
            <div class="col-6">
                <div class="metric-item">
                    <span class="metric-key">慢查询:</span>
                    <span class="metric-value text-warning">${database.slow_queries}</span>
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

// 初始化图表
function initializeCharts() {
    // 响应时间趋势图
    const responseCtx = document.getElementById('responseTimeChart').getContext('2d');
    responseTimeChart = new Chart(responseCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '平均响应时间 (ms)',
                data: [],
                borderColor: 'rgb(37, 99, 235)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 状态码分布图
    const statusCtx = document.getElementById('statusCodeChart').getContext('2d');
    statusCodeChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['2xx', '3xx', '4xx', '5xx'],
            datasets: [{
                data: [85, 10, 4, 1],
                backgroundColor: [
                    '#10b981',
                    '#3b82f6',
                    '#f59e0b',
                    '#ef4444'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 缓存性能图
    const cacheCtx = document.getElementById('cacheChart').getContext('2d');
    cacheChart = new Chart(cacheCtx, {
        type: 'bar',
        data: {
            labels: ['命中率', '未命中率', '淘汰率'],
            datasets: [{
                label: '百分比',
                data: [75, 20, 5],
                backgroundColor: [
                    '#10b981',
                    '#f59e0b',
                    '#ef4444'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// 更新图表
function updateCharts() {
    if (!metricsData.overview) return;
    
    // 更新响应时间趋势
    const now = new Date().toLocaleTimeString('zh-CN');
    const maxDataPoints = 20;
    
    responseTimeChart.data.labels.push(now);
    responseTimeChart.data.datasets[0].data.push(metricsData.overview.avg_response_time);
    
    if (responseTimeChart.data.labels.length > maxDataPoints) {
        responseTimeChart.data.labels.shift();
        responseTimeChart.data.datasets[0].data.shift();
    }
    
    responseTimeChart.update('none');
    
    // 更新缓存图表
    if (metricsData.cache) {
        cacheChart.data.datasets[0].data = [
            metricsData.cache.hit_rate,
            metricsData.cache.miss_rate,
            metricsData.cache.eviction_rate
        ];
        cacheChart.update('none');
    }
}
</script>
{% endblock %}
