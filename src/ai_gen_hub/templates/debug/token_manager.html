{% extends "debug/base.html" %}

{% block title %}Token管理 - AI Gen Hub 调试仪表板{% endblock %}

{% block page_title %}
    <i class="fas fa-key"></i> Token管理
    <small class="text-muted">- API认证令牌管理工具</small>
{% endblock %}

{% block content %}
<!-- Token生成面板 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle"></i> 生成新Token
                </h5>
            </div>
            <div class="card-body">
                <form id="token-form">
                    <div class="mb-3">
                        <label for="user-id" class="form-label">用户ID:</label>
                        <input type="text" class="form-control" id="user-id" value="api_user" placeholder="输入用户ID">
                    </div>
                    <div class="mb-3">
                        <label for="expire-minutes" class="form-label">过期时间 (分钟):</label>
                        <select class="form-select" id="expire-minutes">
                            <option value="60">1小时</option>
                            <option value="480">8小时</option>
                            <option value="1440" selected>24小时</option>
                            <option value="10080">7天</option>
                            <option value="43200">30天</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    <div class="mb-3" id="custom-expire" style="display: none;">
                        <label for="custom-minutes" class="form-label">自定义分钟数:</label>
                        <input type="number" class="form-control" id="custom-minutes" min="1" placeholder="输入分钟数">
                    </div>
                    <div class="mb-3">
                        <label for="token-scope" class="form-label">权限范围:</label>
                        <select class="form-select" id="token-scope">
                            <option value="api_access">API访问</option>
                            <option value="admin_access">管理员访问</option>
                            <option value="read_only">只读访问</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary btn-custom w-100">
                        <i class="fas fa-key"></i> 生成Token
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> 当前配置
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>JWT算法:</strong></td>
                        <td id="jwt-algorithm">HS256</td>
                    </tr>
                    <tr>
                        <td><strong>默认过期时间:</strong></td>
                        <td id="default-expire">1440分钟</td>
                    </tr>
                    <tr>
                        <td><strong>API Key:</strong></td>
                        <td id="api-key">dev-api-key</td>
                    </tr>
                    <tr>
                        <td><strong>认证方式:</strong></td>
                        <td>
                            <span class="badge bg-success">API Key</span>
                            <span class="badge bg-info">JWT Token</span>
                        </td>
                    </tr>
                </table>
                
                <div class="alert alert-info alert-custom mt-3">
                    <i class="fas fa-lightbulb"></i>
                    <strong>提示:</strong> 在开发环境中，调试端点无需认证。生产环境中请确保使用强密钥。
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 生成的Token显示 -->
<div class="row mb-4" id="token-result" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle"></i> 生成的Token
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">JWT Token:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="generated-token" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToken()">
                            <i class="fas fa-copy"></i> 复制
                        </button>
                    </div>
                </div>
                
                <div id="token-info"></div>
                
                <div class="mt-3">
                    <h6>使用示例:</h6>
                    <div class="code-block">
                        <div class="mb-2">
                            <strong>1. 使用Bearer Token:</strong><br>
                            <code id="bearer-example"></code>
                        </div>
                        <div class="mb-2">
                            <strong>2. 使用API Key:</strong><br>
                            <code id="apikey-example"></code>
                        </div>
                        <div>
                            <strong>3. 查询参数方式:</strong><br>
                            <code id="query-example"></code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Token验证面板 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search"></i> Token验证
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <label for="verify-token" class="form-label">输入要验证的Token:</label>
                        <textarea class="form-control" id="verify-token" rows="3" placeholder="粘贴JWT Token..."></textarea>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button class="btn btn-info btn-custom" onclick="verifyToken()">
                                <i class="fas fa-check"></i> 验证Token
                            </button>
                            <button class="btn btn-outline-secondary btn-custom" onclick="clearVerifyToken()">
                                <i class="fas fa-trash"></i> 清空
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="verify-result" class="mt-3" style="display: none;"></div>
            </div>
        </div>
    </div>
</div>

<!-- Token历史记录 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i> Token历史记录
                    </h5>
                    <button class="btn btn-sm btn-outline-danger" onclick="clearTokenHistory()">
                        <i class="fas fa-trash"></i> 清空历史
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="token-history">
                    <div class="text-center text-muted">
                        <p>暂无Token历史记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let tokenHistory = JSON.parse(localStorage.getItem('token_history') || '[]');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTokenManager();
    loadTokenHistory();
    
    // 绑定事件
    document.getElementById('expire-minutes').addEventListener('change', handleExpireChange);
    document.getElementById('token-form').addEventListener('submit', handleTokenGeneration);
});

// 初始化Token管理器
async function initializeTokenManager() {
    try {
        // 获取配置信息
        const config = await fetchData('/debug/api/config');
        
        if (config.security) {
            document.getElementById('jwt-algorithm').textContent = config.security.jwt_algorithm || 'HS256';
            document.getElementById('default-expire').textContent = (config.security.jwt_expire_minutes || 1440) + '分钟';
            
            if (config.security.api_key) {
                const maskedKey = config.security.api_key.substring(0, 8) + '***';
                document.getElementById('api-key').textContent = maskedKey;
            }
        }
    } catch (error) {
        console.error('获取配置失败:', error);
    }
}

// 处理过期时间选择变化
function handleExpireChange() {
    const select = document.getElementById('expire-minutes');
    const customDiv = document.getElementById('custom-expire');
    
    if (select.value === 'custom') {
        customDiv.style.display = 'block';
    } else {
        customDiv.style.display = 'none';
    }
}

// 处理Token生成
async function handleTokenGeneration(event) {
    event.preventDefault();
    
    const userId = document.getElementById('user-id').value;
    const expireSelect = document.getElementById('expire-minutes');
    const customMinutes = document.getElementById('custom-minutes').value;
    const scope = document.getElementById('token-scope').value;
    
    let expireMinutes;
    if (expireSelect.value === 'custom') {
        expireMinutes = parseInt(customMinutes);
        if (!expireMinutes || expireMinutes < 1) {
            alert('请输入有效的自定义分钟数');
            return;
        }
    } else {
        expireMinutes = parseInt(expireSelect.value);
    }
    
    try {
        // 调用后端API生成Token
        const response = await fetch('/debug/api/generate-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: userId,
                expire_minutes: expireMinutes,
                scope: scope
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        displayGeneratedToken(data);
        
        // 保存到历史记录
        saveTokenToHistory({
            user_id: userId,
            expire_minutes: expireMinutes,
            scope: scope,
            token: data.token,
            created_at: new Date().toISOString(),
            expires_at: data.expires_at
        });
        
    } catch (error) {
        console.error('生成Token失败:', error);
        alert('生成Token失败: ' + error.message);
    }
}

// 显示生成的Token
function displayGeneratedToken(data) {
    const resultDiv = document.getElementById('token-result');
    const tokenInput = document.getElementById('generated-token');
    const tokenInfo = document.getElementById('token-info');
    
    tokenInput.value = data.token;
    
    // 显示Token信息
    tokenInfo.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <small><strong>用户ID:</strong> ${data.user_id}</small><br>
                <small><strong>权限范围:</strong> ${data.scope}</small>
            </div>
            <div class="col-md-6">
                <small><strong>创建时间:</strong> ${new Date(data.created_at).toLocaleString()}</small><br>
                <small><strong>过期时间:</strong> ${new Date(data.expires_at).toLocaleString()}</small>
            </div>
        </div>
    `;
    
    // 更新使用示例
    updateUsageExamples(data.token);
    
    resultDiv.style.display = 'block';
    resultDiv.scrollIntoView({ behavior: 'smooth' });
}

// 更新使用示例
function updateUsageExamples(token) {
    const apiKey = document.getElementById('api-key').textContent;
    
    document.getElementById('bearer-example').textContent = 
        `curl -H 'Authorization: Bearer ${token}' http://localhost:8001/api/v1/text/models`;
    
    document.getElementById('apikey-example').textContent = 
        `curl -H 'X-API-Key: ${apiKey}' http://localhost:8001/api/v1/text/models`;
    
    document.getElementById('query-example').textContent = 
        `curl 'http://localhost:8001/api/v1/text/models?api_key=${apiKey}'`;
}

// 复制Token
function copyToken() {
    const tokenInput = document.getElementById('generated-token');
    tokenInput.select();
    document.execCommand('copy');
    
    // 显示复制成功提示
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i> 已复制';
    button.classList.add('btn-success');
    button.classList.remove('btn-outline-secondary');
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-secondary');
    }, 2000);
}

// 验证Token
async function verifyToken() {
    const token = document.getElementById('verify-token').value.trim();
    if (!token) {
        alert('请输入要验证的Token');
        return;
    }
    
    try {
        const response = await fetch('/debug/api/verify-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ token: token })
        });
        
        const data = await response.json();
        displayVerifyResult(data);
        
    } catch (error) {
        console.error('验证Token失败:', error);
        displayVerifyResult({
            valid: false,
            error: error.message
        });
    }
}

// 显示验证结果
function displayVerifyResult(data) {
    const resultDiv = document.getElementById('verify-result');
    
    if (data.valid) {
        resultDiv.innerHTML = `
            <div class="alert alert-success alert-custom">
                <i class="fas fa-check-circle"></i>
                <strong>Token有效</strong>
                <div class="mt-2">
                    <div class="row">
                        <div class="col-md-6">
                            <small><strong>用户ID:</strong> ${data.payload.sub}</small><br>
                            <small><strong>权限范围:</strong> ${data.payload.scope || 'api_access'}</small>
                        </div>
                        <div class="col-md-6">
                            <small><strong>签发时间:</strong> ${new Date(data.payload.iat * 1000).toLocaleString()}</small><br>
                            <small><strong>过期时间:</strong> ${new Date(data.payload.exp * 1000).toLocaleString()}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else {
        resultDiv.innerHTML = `
            <div class="alert alert-danger alert-custom">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Token无效</strong>
                <div class="mt-2">
                    <small>${data.error || '未知错误'}</small>
                </div>
            </div>
        `;
    }
    
    resultDiv.style.display = 'block';
}

// 清空验证Token
function clearVerifyToken() {
    document.getElementById('verify-token').value = '';
    document.getElementById('verify-result').style.display = 'none';
}

// 保存Token到历史记录
function saveTokenToHistory(tokenData) {
    tokenHistory.unshift(tokenData);
    
    // 只保留最近20个
    if (tokenHistory.length > 20) {
        tokenHistory = tokenHistory.slice(0, 20);
    }
    
    localStorage.setItem('token_history', JSON.stringify(tokenHistory));
    loadTokenHistory();
}

// 加载Token历史记录
function loadTokenHistory() {
    const container = document.getElementById('token-history');
    
    if (tokenHistory.length === 0) {
        container.innerHTML = '<div class="text-center text-muted"><p>暂无Token历史记录</p></div>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>用户ID</th><th>权限范围</th><th>创建时间</th><th>过期时间</th><th>状态</th><th>操作</th></tr></thead><tbody>';
    
    tokenHistory.forEach((item, index) => {
        const isExpired = new Date(item.expires_at) < new Date();
        const statusClass = isExpired ? 'danger' : 'success';
        const statusText = isExpired ? '已过期' : '有效';
        
        html += `
            <tr>
                <td>${item.user_id}</td>
                <td><span class="badge bg-secondary">${item.scope}</span></td>
                <td><small>${new Date(item.created_at).toLocaleString()}</small></td>
                <td><small>${new Date(item.expires_at).toLocaleString()}</small></td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="useHistoryToken(${index})">
                        <i class="fas fa-copy"></i> 使用
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// 使用历史Token
function useHistoryToken(index) {
    const token = tokenHistory[index];
    document.getElementById('generated-token').value = token.token;
    document.getElementById('token-result').style.display = 'block';
    updateUsageExamples(token.token);
}

// 清空Token历史
function clearTokenHistory() {
    if (confirm('确定要清空所有Token历史记录吗？')) {
        tokenHistory = [];
        localStorage.removeItem('token_history');
        loadTokenHistory();
    }
}
</script>
{% endblock %}
