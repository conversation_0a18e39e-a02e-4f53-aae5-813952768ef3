{% extends "debug/base.html" %}

{% block title %}配置信息 - AI Gen Hub 调试仪表板{% endblock %}

{% block page_title %}
    <i class="fas fa-cog"></i> 配置信息
    <small class="text-muted">- 应用配置和环境信息</small>
{% endblock %}

{% block content %}
<!-- 安全提示 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning alert-custom">
            <i class="fas fa-shield-alt"></i>
            <strong>安全提示:</strong> 敏感信息（如密码、密钥等）已进行脱敏处理，仅显示部分字符用于验证配置。
        </div>
    </div>
</div>

<!-- 配置导航标签 -->
<div class="row mb-4">
    <div class="col-12">
        <ul class="nav nav-tabs" id="configTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="app-tab" data-bs-toggle="tab" data-bs-target="#app-pane" type="button">
                    <i class="fas fa-desktop"></i> 应用配置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database-pane" type="button">
                    <i class="fas fa-database"></i> 数据库
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="providers-tab" data-bs-toggle="tab" data-bs-target="#providers-pane" type="button">
                    <i class="fas fa-cloud"></i> AI供应商
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="cache-tab" data-bs-toggle="tab" data-bs-target="#cache-pane" type="button">
                    <i class="fas fa-memory"></i> 缓存
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="env-tab" data-bs-toggle="tab" data-bs-target="#env-pane" type="button">
                    <i class="fas fa-list"></i> 环境变量
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="runtime-tab" data-bs-toggle="tab" data-bs-target="#runtime-pane" type="button">
                    <i class="fas fa-code"></i> 运行时
                </button>
            </li>
        </ul>
    </div>
</div>

<!-- 配置内容 -->
<div class="tab-content" id="configTabContent">
    <!-- 应用配置 -->
    <div class="tab-pane fade show active" id="app-pane" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> 基本信息</h6>
                    </div>
                    <div class="card-body" id="app-basic-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt"></i> 安全配置</h6>
                    </div>
                    <div class="card-body" id="app-security-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> 性能配置</h6>
                    </div>
                    <div class="card-body" id="app-performance-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-toggle-on"></i> 功能开关</h6>
                    </div>
                    <div class="card-body" id="app-features-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 数据库配置 -->
    <div class="tab-pane fade" id="database-pane" role="tabpanel">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-database"></i> 数据库连接配置</h6>
                    </div>
                    <div class="card-body" id="database-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 连接池配置</h6>
                    </div>
                    <div class="card-body" id="database-pool-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AI供应商配置 -->
    <div class="tab-pane fade" id="providers-pane" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fab fa-openai"></i> OpenAI</h6>
                    </div>
                    <div class="card-body" id="openai-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fab fa-google"></i> Google AI</h6>
                    </div>
                    <div class="card-body" id="google-ai-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-robot"></i> Anthropic</h6>
                    </div>
                    <div class="card-body" id="anthropic-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fab fa-microsoft"></i> Azure</h6>
                    </div>
                    <div class="card-body" id="azure-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 缓存配置 -->
    <div class="tab-pane fade" id="cache-pane" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-memory"></i> 缓存配置</h6>
                    </div>
                    <div class="card-body" id="cache-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fab fa-redis"></i> Redis配置</h6>
                    </div>
                    <div class="card-body" id="redis-config">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 环境变量 -->
    <div class="tab-pane fade" id="env-pane" role="tabpanel">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list"></i> 应用环境变量</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-custom">
                                <thead>
                                    <tr>
                                        <th>变量名</th>
                                        <th>值</th>
                                    </tr>
                                </thead>
                                <tbody id="app-env-vars">
                                    <tr>
                                        <td colspan="2" class="text-center">
                                            <div class="loading-spinner"></div> 加载中...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-desktop"></i> 系统环境变量</h6>
                    </div>
                    <div class="card-body" id="system-env-vars">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 运行时信息 -->
    <div class="tab-pane fade" id="runtime-pane" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fab fa-python"></i> Python信息</h6>
                    </div>
                    <div class="card-body" id="python-info">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-server"></i> 系统信息</h6>
                    </div>
                    <div class="card-body" id="system-info">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cube"></i> 已安装包版本</h6>
                    </div>
                    <div class="card-body" id="packages-info">
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.config-item:last-child {
    border-bottom: none;
}

.config-key {
    font-weight: 500;
    color: #374151;
}

.config-value {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #6b7280;
}

.config-value.sensitive {
    color: #ef4444;
    font-style: italic;
}

.config-value.boolean-true {
    color: #10b981;
    font-weight: bold;
}

.config-value.boolean-false {
    color: #ef4444;
    font-weight: bold;
}

.env-var-row {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.package-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.package-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.package-name {
    font-weight: 500;
    color: #374151;
}

.package-version {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #6b7280;
    background-color: #e2e8f0;
    padding: 2px 6px;
    border-radius: 4px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let configData = {};
let envData = {};
let runtimeData = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadConfigData();
    
    // 绑定标签切换事件
    document.querySelectorAll('#configTabs button').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            const target = event.target.getAttribute('data-bs-target');
            if (target === '#env-pane' && !envData.loaded) {
                loadEnvironmentData();
            } else if (target === '#runtime-pane' && !runtimeData.loaded) {
                loadRuntimeData();
            }
        });
    });
});

// 加载配置数据
async function loadConfigData() {
    try {
        const data = await fetchData('/debug/api/config');
        configData = data.config;
        
        renderApplicationConfig();
        renderDatabaseConfig();
        renderProvidersConfig();
        renderCacheConfig();
        
    } catch (error) {
        console.error('加载配置数据失败:', error);
        showConfigError('配置数据加载失败: ' + error.message);
    }
}

// 加载环境变量数据
async function loadEnvironmentData() {
    try {
        const data = await fetchData('/debug/api/config/environment');
        envData = data;
        envData.loaded = true;
        
        renderEnvironmentVariables();
        
    } catch (error) {
        console.error('加载环境变量失败:', error);
        showError('app-env-vars', '环境变量加载失败: ' + error.message);
    }
}

// 加载运行时数据
async function loadRuntimeData() {
    try {
        const data = await fetchData('/debug/api/config/runtime');
        runtimeData = data.runtime;
        runtimeData.loaded = true;
        
        renderRuntimeInfo();
        
    } catch (error) {
        console.error('加载运行时信息失败:', error);
        showError('python-info', '运行时信息加载失败: ' + error.message);
    }
}

// 渲染应用配置
function renderApplicationConfig() {
    if (!configData.application) return;
    
    // 基本信息
    const basicConfig = document.getElementById('app-basic-config');
    basicConfig.innerHTML = renderConfigSection(configData.application);
    
    // 安全配置
    const securityConfig = document.getElementById('app-security-config');
    securityConfig.innerHTML = renderConfigSection(configData.security || {});
    
    // 性能配置
    const performanceConfig = document.getElementById('app-performance-config');
    performanceConfig.innerHTML = renderConfigSection(configData.performance || {});
    
    // 功能开关
    const featuresConfig = document.getElementById('app-features-config');
    featuresConfig.innerHTML = renderConfigSection(configData.features || {});
}

// 渲染数据库配置
function renderDatabaseConfig() {
    const databaseConfig = document.getElementById('database-config');
    const poolConfig = document.getElementById('database-pool-config');
    
    if (!configData.database || configData.database.status === '未配置') {
        databaseConfig.innerHTML = '<div class="text-muted">数据库未配置</div>';
        poolConfig.innerHTML = '<div class="text-muted">数据库未配置</div>';
        return;
    }
    
    // 连接配置
    const connectionInfo = {
        'host': configData.database.host,
        'port': configData.database.port,
        'database': configData.database.database,
        'user': configData.database.user,
        'password': configData.database.password
    };
    databaseConfig.innerHTML = renderConfigSection(connectionInfo);
    
    // 连接池配置
    const poolInfo = {
        'pool_size': configData.database.pool_size,
        'max_overflow': configData.database.max_overflow,
        'pool_timeout': configData.database.pool_timeout,
        'pool_recycle': configData.database.pool_recycle
    };
    poolConfig.innerHTML = renderConfigSection(poolInfo);
}

// 渲染供应商配置
function renderProvidersConfig() {
    if (!configData.providers) return;
    
    // OpenAI
    const openaiConfig = document.getElementById('openai-config');
    openaiConfig.innerHTML = renderConfigSection(configData.providers.openai || {});
    
    // Google AI
    const googleConfig = document.getElementById('google-ai-config');
    googleConfig.innerHTML = renderConfigSection(configData.providers.google_ai || {});
    
    // Anthropic
    const anthropicConfig = document.getElementById('anthropic-config');
    anthropicConfig.innerHTML = renderConfigSection(configData.providers.anthropic || {});
    
    // Azure
    const azureConfig = document.getElementById('azure-config');
    azureConfig.innerHTML = renderConfigSection(configData.providers.azure || {});
}

// 渲染缓存配置
function renderCacheConfig() {
    // 缓存配置
    const cacheConfig = document.getElementById('cache-config');
    cacheConfig.innerHTML = renderConfigSection(configData.cache || {});
    
    // Redis配置
    const redisConfig = document.getElementById('redis-config');
    redisConfig.innerHTML = renderConfigSection(configData.redis || {});
}

// 渲染环境变量
function renderEnvironmentVariables() {
    // 应用环境变量
    const appEnvVars = document.getElementById('app-env-vars');
    let html = '';
    
    if (envData.application_vars && Object.keys(envData.application_vars).length > 0) {
        Object.entries(envData.application_vars).forEach(([key, value]) => {
            const isSensitive = value && value.toString().includes('***');
            html += `
                <tr class="env-var-row">
                    <td><code>${key}</code></td>
                    <td class="${isSensitive ? 'text-danger' : 'text-muted'}">
                        <code>${value || '(空)'}</code>
                    </td>
                </tr>
            `;
        });
    } else {
        html = '<tr><td colspan="2" class="text-center text-muted">没有相关环境变量</td></tr>';
    }
    
    appEnvVars.innerHTML = html;
    
    // 系统环境变量
    const systemEnvVars = document.getElementById('system-env-vars');
    let systemHtml = '';
    
    if (envData.system_vars) {
        Object.entries(envData.system_vars).forEach(([key, value]) => {
            systemHtml += `
                <div class="config-item">
                    <span class="config-key">${key}:</span>
                    <span class="config-value"><code>${value}</code></span>
                </div>
            `;
        });
    }
    
    systemHtml += `
        <div class="mt-3">
            <small class="text-muted">
                总环境变量数: ${envData.total_env_vars || 0}
            </small>
        </div>
    `;
    
    systemEnvVars.innerHTML = systemHtml;
}

// 渲染运行时信息
function renderRuntimeInfo() {
    // Python信息
    const pythonInfo = document.getElementById('python-info');
    if (runtimeData.python) {
        pythonInfo.innerHTML = `
            <div class="config-item">
                <span class="config-key">版本:</span>
                <span class="config-value">${runtimeData.python.version_info.major}.${runtimeData.python.version_info.minor}.${runtimeData.python.version_info.micro}</span>
            </div>
            <div class="config-item">
                <span class="config-key">实现:</span>
                <span class="config-value">${runtimeData.python.implementation}</span>
            </div>
            <div class="config-item">
                <span class="config-key">平台:</span>
                <span class="config-value">${runtimeData.python.platform}</span>
            </div>
            <div class="config-item">
                <span class="config-key">可执行文件:</span>
                <span class="config-value"><code>${runtimeData.python.executable}</code></span>
            </div>
        `;
    }
    
    // 系统信息
    const systemInfo = document.getElementById('system-info');
    if (runtimeData.system) {
        systemInfo.innerHTML = `
            <div class="config-item">
                <span class="config-key">系统:</span>
                <span class="config-value">${runtimeData.system.system}</span>
            </div>
            <div class="config-item">
                <span class="config-key">版本:</span>
                <span class="config-value">${runtimeData.system.release}</span>
            </div>
            <div class="config-item">
                <span class="config-key">架构:</span>
                <span class="config-value">${runtimeData.system.machine}</span>
            </div>
            <div class="config-item">
                <span class="config-key">处理器:</span>
                <span class="config-value">${runtimeData.system.processor || '未知'}</span>
            </div>
        `;
    }
    
    // 包信息
    const packagesInfo = document.getElementById('packages-info');
    if (runtimeData.packages) {
        let packagesHtml = '<div class="package-grid">';
        
        Object.entries(runtimeData.packages).forEach(([name, version]) => {
            packagesHtml += `
                <div class="package-item">
                    <span class="package-name">${name}</span>
                    <span class="package-version">${version}</span>
                </div>
            `;
        });
        
        packagesHtml += '</div>';
        packagesInfo.innerHTML = packagesHtml;
    }
}

// 渲染配置段落
function renderConfigSection(config) {
    if (!config || Object.keys(config).length === 0) {
        return '<div class="text-muted">未配置</div>';
    }
    
    let html = '';
    
    Object.entries(config).forEach(([key, value]) => {
        let valueClass = 'config-value';
        let displayValue = value;
        
        // 处理不同类型的值
        if (typeof value === 'boolean') {
            valueClass += value ? ' boolean-true' : ' boolean-false';
            displayValue = value ? '启用' : '禁用';
        } else if (value && value.toString().includes('***')) {
            valueClass += ' sensitive';
            displayValue = value;
        } else if (value === null || value === undefined) {
            displayValue = '未设置';
            valueClass += ' text-muted';
        } else if (typeof value === 'object') {
            displayValue = JSON.stringify(value);
        }
        
        html += `
            <div class="config-item">
                <span class="config-key">${key}:</span>
                <span class="${valueClass}">${displayValue}</span>
            </div>
        `;
    });
    
    return html;
}

// 显示配置错误
function showConfigError(message) {
    const sections = [
        'app-basic-config', 'app-security-config', 
        'app-performance-config', 'app-features-config'
    ];
    
    sections.forEach(sectionId => {
        showError(sectionId, message);
    });
}
</script>
{% endblock %}
