{% extends "debug/base.html" %}

{% block title %}AI Gen Hub 调试仪表板{% endblock %}

{% block page_title %}
    <i class="fas fa-tachometer-alt"></i> 调试仪表板
    <small class="text-muted">- 系统概览</small>
{% endblock %}

{% block content %}
<!-- 系统状态概览卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-primary" id="cpu-usage">--</div>
                <div class="metric-label">CPU 使用率</div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar progress-bar-custom bg-primary" id="cpu-progress" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-success" id="memory-usage">--</div>
                <div class="metric-label">内存使用率</div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar progress-bar-custom bg-success" id="memory-progress" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-warning" id="disk-usage">--</div>
                <div class="metric-label">磁盘使用率</div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar progress-bar-custom bg-warning" id="disk-progress" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-info" id="uptime">--</div>
                <div class="metric-label">运行时间</div>
                <div class="mt-2">
                    <small class="text-muted" id="process-count">-- 个进程</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 应用状态和健康检查 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> 应用信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>应用名称:</strong></td>
                        <td>{{ app_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>版本:</strong></td>
                        <td>{{ app_version }}</td>
                    </tr>
                    <tr>
                        <td><strong>环境:</strong></td>
                        <td>
                            <span class="badge bg-{% if environment == 'production' %}danger{% elif environment == 'staging' %}warning{% else %}success{% endif %}">
                                {{ environment }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>启动时间:</strong></td>
                        <td id="app-start-time">--</td>
                    </tr>
                    <tr>
                        <td><strong>调试模式:</strong></td>
                        <td>
                            <span class="badge bg-info" id="debug-mode">--</span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-heartbeat"></i> 健康检查
                </h5>
            </div>
            <div class="card-body" id="health-status">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">正在检查系统健康状态...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作面板 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> 快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 mb-3">
                        <a href="/debug/system" class="btn btn-outline-primary btn-custom w-100">
                            <i class="fas fa-server"></i><br>
                            <small>系统监控</small>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="/debug/api-test" class="btn btn-outline-success btn-custom w-100">
                            <i class="fas fa-code"></i><br>
                            <small>API测试</small>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="/debug/logs" class="btn btn-outline-info btn-custom w-100">
                            <i class="fas fa-file-alt"></i><br>
                            <small>日志查看</small>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="/debug/config" class="btn btn-outline-warning btn-custom w-100">
                            <i class="fas fa-cog"></i><br>
                            <small>配置信息</small>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="/debug/metrics" class="btn btn-outline-secondary btn-custom w-100">
                            <i class="fas fa-chart-line"></i><br>
                            <small>性能指标</small>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="/debug/tools" class="btn btn-outline-dark btn-custom w-100">
                            <i class="fas fa-tools"></i><br>
                            <small>开发工具</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动和统计 -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area"></i> 系统性能趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="performanceChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> 系统状态
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>数据库连接</span>
                    <span class="status-badge" id="db-status">检查中...</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Redis缓存</span>
                    <span class="status-badge" id="redis-status">检查中...</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>AI供应商</span>
                    <span class="status-badge" id="provider-status">检查中...</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>系统资源</span>
                    <span class="status-badge" id="system-status">检查中...</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let performanceChart;
let systemData = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    initializeChart();
    
    // 每30秒更新一次数据
    setInterval(updateSystemData, 30000);
});

// 初始化仪表板
async function initializeDashboard() {
    await updateSystemData();
}

// 更新系统数据
async function updateSystemData() {
    try {
        const data = await fetchData('/debug/api/system/info');
        systemData = data;
        
        updateSystemMetrics(data.system);
        updateApplicationInfo(data.application);
        updateHealthStatus(data.health);
        updatePerformanceChart();
        
    } catch (error) {
        console.error('更新系统数据失败:', error);
        showError('health-status', '获取系统信息失败: ' + error.message);
    }
}

// 更新系统指标
function updateSystemMetrics(system) {
    // CPU使用率
    document.getElementById('cpu-usage').textContent = system.cpu_percent.toFixed(1) + '%';
    document.getElementById('cpu-progress').style.width = system.cpu_percent + '%';
    
    // 内存使用率
    document.getElementById('memory-usage').textContent = system.memory_percent.toFixed(1) + '%';
    document.getElementById('memory-progress').style.width = system.memory_percent + '%';
    
    // 磁盘使用率
    document.getElementById('disk-usage').textContent = system.disk_percent.toFixed(1) + '%';
    document.getElementById('disk-progress').style.width = system.disk_percent + '%';
    
    // 运行时间
    document.getElementById('uptime').textContent = formatDuration(system.uptime);
    document.getElementById('process-count').textContent = system.process_count + ' 个进程';
}

// 更新应用信息
function updateApplicationInfo(app) {
    const startTime = new Date(app.start_time * 1000);
    document.getElementById('app-start-time').textContent = startTime.toLocaleString('zh-CN');
    document.getElementById('debug-mode').textContent = app.debug_mode ? '已启用' : '已禁用';
}

// 更新健康状态
function updateHealthStatus(health) {
    const container = document.getElementById('health-status');
    
    if (!health || health.error) {
        container.innerHTML = `
            <div class="alert alert-danger alert-custom">
                <i class="fas fa-exclamation-triangle"></i>
                健康检查失败: ${health?.error || '未知错误'}
            </div>
        `;
        return;
    }
    
    const statusClass = health.overall_status === 'healthy' ? 'success' : 
                       health.overall_status === 'degraded' ? 'warning' : 'danger';
    
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6>总体状态:</h6>
            <span class="status-badge status-${health.overall_status === 'healthy' ? 'healthy' : 
                                                health.overall_status === 'degraded' ? 'warning' : 'error'}">
                ${health.overall_status}
            </span>
        </div>
    `;
    
    if (health.checks && health.checks.length > 0) {
        html += '<div class="mt-3">';
        health.checks.forEach(check => {
            const checkStatus = check.status === 'healthy' ? 'healthy' : 
                               check.status === 'degraded' ? 'warning' : 'error';
            html += `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small>${check.name}</small>
                    <span class="status-badge status-${checkStatus}" style="font-size: 0.7rem;">
                        ${check.status}
                    </span>
                </div>
            `;
        });
        html += '</div>';
    }
    
    container.innerHTML = html;
    
    // 更新右侧状态指示器
    updateStatusIndicators(health.checks);
}

// 更新状态指示器
function updateStatusIndicators(checks) {
    const indicators = {
        'db-status': 'database',
        'redis-status': 'redis',
        'provider-status': 'provider',
        'system-status': 'system'
    };
    
    Object.entries(indicators).forEach(([elementId, checkType]) => {
        const element = document.getElementById(elementId);
        const check = checks?.find(c => c.name.toLowerCase().includes(checkType));
        
        if (check) {
            const statusClass = check.status === 'healthy' ? 'status-healthy' : 
                               check.status === 'degraded' ? 'status-warning' : 'status-error';
            element.className = `status-badge ${statusClass}`;
            element.textContent = check.status === 'healthy' ? '正常' : 
                                 check.status === 'degraded' ? '降级' : '异常';
        } else {
            element.className = 'status-badge status-warning';
            element.textContent = '未知';
        }
    });
}

// 初始化性能图表
function initializeChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'CPU使用率 (%)',
                    data: [],
                    borderColor: 'rgb(37, 99, 235)',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4
                },
                {
                    label: '内存使用率 (%)',
                    data: [],
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
}

// 更新性能图表
function updatePerformanceChart() {
    if (!performanceChart || !systemData.system) return;
    
    const now = new Date().toLocaleTimeString('zh-CN');
    const maxDataPoints = 20;
    
    // 添加新数据点
    performanceChart.data.labels.push(now);
    performanceChart.data.datasets[0].data.push(systemData.system.cpu_percent);
    performanceChart.data.datasets[1].data.push(systemData.system.memory_percent);
    
    // 保持最大数据点数量
    if (performanceChart.data.labels.length > maxDataPoints) {
        performanceChart.data.labels.shift();
        performanceChart.data.datasets[0].data.shift();
        performanceChart.data.datasets[1].data.shift();
    }
    
    performanceChart.update('none');
}
</script>
{% endblock %}
