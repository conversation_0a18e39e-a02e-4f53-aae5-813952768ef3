<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AI Gen Hub 调试仪表板{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
        }
        
        body {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 4px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
        }
        
        .status-healthy {
            background-color: var(--success-color);
            color: white;
        }
        
        .status-warning {
            background-color: var(--warning-color);
            color: white;
        }
        
        .status-error {
            background-color: var(--danger-color);
            color: white;
        }
        
        .metric-card {
            text-align: center;
            padding: 20px;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: var(--secondary-color);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .progress-custom {
            height: 8px;
            border-radius: 4px;
            background-color: #e2e8f0;
        }
        
        .progress-bar-custom {
            border-radius: 4px;
            transition: width 0.6s ease;
        }
        
        .btn-custom {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .table-custom {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table-custom th {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 15px;
        }
        
        .table-custom td {
            padding: 12px 15px;
            border-color: #e2e8f0;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert-custom {
            border: none;
            border-radius: 8px;
            padding: 15px 20px;
        }
        
        .code-block {
            background-color: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }
        
        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-bug"></i>
                            调试仪表板
                        </h4>
                        <small class="text-light opacity-75">AI Gen Hub Debug Console</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if page == 'dashboard' %}active{% endif %}" href="/debug/">
                                <i class="fas fa-tachometer-alt"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if page == 'system' %}active{% endif %}" href="/debug/system">
                                <i class="fas fa-server"></i>
                                系统监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if page == 'api-test' %}active{% endif %}" href="/debug/api-test">
                                <i class="fas fa-code"></i>
                                API测试
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if page == 'logs' %}active{% endif %}" href="/debug/logs">
                                <i class="fas fa-file-alt"></i>
                                日志查看
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if page == 'config' %}active{% endif %}" href="/debug/config">
                                <i class="fas fa-cog"></i>
                                配置信息
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if page == 'metrics' %}active{% endif %}" href="/debug/metrics">
                                <i class="fas fa-chart-line"></i>
                                性能指标
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if page == 'tools' %}active{% endif %}" href="/debug/tools">
                                <i class="fas fa-tools"></i>
                                开发工具
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if page == 'tokens' %}active{% endif %}" href="/debug/tokens">
                                <i class="fas fa-key"></i>
                                Token管理
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                    
                    <div class="text-center">
                        <small class="text-light opacity-75">
                            <i class="fas fa-shield-alt"></i>
                            仅开发环境可用
                        </small>
                    </div>
                </div>
            </nav>
            
            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}调试仪表板{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshPage()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/docs" target="_blank">API文档</a></li>
                                <li><a class="dropdown-item" href="/health" target="_blank">健康检查</a></li>
                                <li><a class="dropdown-item" href="/metrics/prometheus" target="_blank">Prometheus指标</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 通用JavaScript -->
    <script>
        // 刷新页面
        function refreshPage() {
            location.reload();
        }
        
        // 格式化字节数
        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }
        
        // 格式化时间
        function formatDuration(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            
            if (days > 0) {
                return `${days}天 ${hours}小时 ${minutes}分钟`;
            } else if (hours > 0) {
                return `${hours}小时 ${minutes}分钟`;
            } else if (minutes > 0) {
                return `${minutes}分钟 ${secs}秒`;
            } else {
                return `${secs}秒`;
            }
        }
        
        // 显示加载状态
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = '<div class="text-center"><div class="loading-spinner"></div> 加载中...</div>';
            }
        }
        
        // 显示错误信息
        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `<div class="alert alert-danger alert-custom"><i class="fas fa-exclamation-triangle"></i> ${message}</div>`;
            }
        }
        
        // AJAX请求封装
        async function fetchData(url) {
            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error('请求失败:', error);
                throw error;
            }
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
