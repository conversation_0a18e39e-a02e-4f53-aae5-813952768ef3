{% extends "debug/base.html" %}

{% block title %}API接口测试 - AI Gen Hub 调试仪表板{% endblock %}

{% block page_title %}
    <i class="fas fa-code"></i> API接口测试
    <small class="text-muted">- 交互式API测试工具</small>
{% endblock %}

{% block content %}
<!-- 控制面板 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i> 测试控制面板
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="endpoint-filter" class="form-label">端点过滤:</label>
                        <input type="text" class="form-control" id="endpoint-filter" placeholder="搜索端点...">
                    </div>
                    <div class="col-md-3">
                        <label for="method-filter" class="form-label">HTTP方法:</label>
                        <select class="form-select" id="method-filter">
                            <option value="">全部</option>
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                            <option value="PATCH">PATCH</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="tag-filter" class="form-label">标签:</label>
                        <select class="form-select" id="tag-filter">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary btn-custom w-100" onclick="refreshEndpoints()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容区域 -->
<div class="row">
    <!-- 端点列表 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> API端点列表
                    <span class="badge bg-secondary" id="endpoints-count">0</span>
                </h5>
            </div>
            <div class="card-body p-0" style="max-height: 600px; overflow-y: auto;">
                <div id="endpoints-list">
                    <div class="text-center p-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载端点列表...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 测试面板 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-play"></i> API测试面板
                </h5>
            </div>
            <div class="card-body">
                <div id="test-panel">
                    <div class="text-center text-muted">
                        <i class="fas fa-mouse-pointer fa-3x mb-3"></i>
                        <p>请从左侧选择一个API端点开始测试</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试结果 -->
        <div class="card mt-3" id="result-panel" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> 测试结果
                </h5>
            </div>
            <div class="card-body">
                <div id="test-result"></div>
            </div>
        </div>
    </div>
</div>

<!-- 测试历史 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> 测试历史
                </h5>
                <button class="btn btn-sm btn-outline-danger" onclick="clearHistory()">
                    <i class="fas fa-trash"></i> 清空历史
                </button>
            </div>
            <div class="card-body">
                <div id="test-history">
                    <div class="text-center text-muted">
                        <p>暂无测试历史</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.endpoint-item {
    border-bottom: 1px solid #e2e8f0;
    padding: 12px 15px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.endpoint-item:hover {
    background-color: #f8fafc;
}

.endpoint-item.active {
    background-color: #e0f2fe;
    border-left: 4px solid var(--primary-color);
}

.method-badge {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.method-get { background-color: #10b981; color: white; }
.method-post { background-color: #3b82f6; color: white; }
.method-put { background-color: #f59e0b; color: white; }
.method-delete { background-color: #ef4444; color: white; }
.method-patch { background-color: #8b5cf6; color: white; }

.test-form {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 20px;
}

.response-section {
    background-color: #1e293b;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    background-color: white;
}

.status-success { color: #10b981; }
.status-error { color: #ef4444; }
.status-warning { color: #f59e0b; }
</style>
{% endblock %}

{% block extra_js %}
<script>
let endpoints = [];
let filteredEndpoints = [];
let selectedEndpoint = null;
let testHistory = JSON.parse(localStorage.getItem('api_test_history') || '[]');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAPITest();
    loadTestHistory();
    
    // 绑定过滤器事件
    document.getElementById('endpoint-filter').addEventListener('input', filterEndpoints);
    document.getElementById('method-filter').addEventListener('change', filterEndpoints);
    document.getElementById('tag-filter').addEventListener('change', filterEndpoints);
});

// 初始化API测试
async function initializeAPITest() {
    await loadEndpoints();
    populateTagFilter();
    filterEndpoints();
}

// 加载端点列表
async function loadEndpoints() {
    try {
        const data = await fetchData('/debug/api/endpoints/detailed');
        endpoints = data.endpoints;
        filteredEndpoints = [...endpoints];
        
        console.log('加载了', endpoints.length, '个端点');
        
    } catch (error) {
        console.error('加载端点失败:', error);
        showError('endpoints-list', '加载端点失败: ' + error.message);
    }
}

// 填充标签过滤器
function populateTagFilter() {
    const tagFilter = document.getElementById('tag-filter');
    const tags = new Set();
    
    endpoints.forEach(endpoint => {
        if (endpoint.tags && endpoint.tags.length > 0) {
            endpoint.tags.forEach(tag => tags.add(tag));
        } else {
            tags.add('未分类');
        }
    });
    
    // 清空现有选项（保留"全部"）
    while (tagFilter.children.length > 1) {
        tagFilter.removeChild(tagFilter.lastChild);
    }
    
    // 添加标签选项
    Array.from(tags).sort().forEach(tag => {
        const option = document.createElement('option');
        option.value = tag;
        option.textContent = tag;
        tagFilter.appendChild(option);
    });
}

// 过滤端点
function filterEndpoints() {
    const searchTerm = document.getElementById('endpoint-filter').value.toLowerCase();
    const methodFilter = document.getElementById('method-filter').value;
    const tagFilter = document.getElementById('tag-filter').value;
    
    filteredEndpoints = endpoints.filter(endpoint => {
        // 搜索过滤
        const matchesSearch = !searchTerm || 
            endpoint.path.toLowerCase().includes(searchTerm) ||
            endpoint.description.toLowerCase().includes(searchTerm);
        
        // 方法过滤
        const matchesMethod = !methodFilter || endpoint.method === methodFilter;
        
        // 标签过滤
        const matchesTag = !tagFilter || 
            (endpoint.tags && endpoint.tags.includes(tagFilter)) ||
            (tagFilter === '未分类' && (!endpoint.tags || endpoint.tags.length === 0));
        
        return matchesSearch && matchesMethod && matchesTag;
    });
    
    renderEndpointsList();
}

// 渲染端点列表
function renderEndpointsList() {
    const container = document.getElementById('endpoints-list');
    const countBadge = document.getElementById('endpoints-count');
    
    countBadge.textContent = filteredEndpoints.length;
    
    if (filteredEndpoints.length === 0) {
        container.innerHTML = '<div class="text-center p-4 text-muted">没有找到匹配的端点</div>';
        return;
    }
    
    let html = '';
    filteredEndpoints.forEach((endpoint, index) => {
        const isActive = selectedEndpoint && 
            selectedEndpoint.path === endpoint.path && 
            selectedEndpoint.method === endpoint.method;
        
        html += `
            <div class="endpoint-item ${isActive ? 'active' : ''}" onclick="selectEndpoint(${index})">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <span class="method-badge method-${endpoint.method.toLowerCase()}">${endpoint.method}</span>
                    <small class="text-muted">${endpoint.tags ? endpoint.tags.join(', ') : '未分类'}</small>
                </div>
                <div class="fw-bold">${endpoint.path}</div>
                ${endpoint.description ? `<small class="text-muted">${endpoint.description}</small>` : ''}
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 选择端点
function selectEndpoint(index) {
    selectedEndpoint = filteredEndpoints[index];
    renderEndpointsList(); // 重新渲染以更新选中状态
    renderTestPanel();
}

// 渲染测试面板
function renderTestPanel() {
    if (!selectedEndpoint) return;
    
    const panel = document.getElementById('test-panel');
    
    let html = `
        <div class="test-form">
            <div class="row mb-3">
                <div class="col-md-2">
                    <label class="form-label">方法:</label>
                    <span class="method-badge method-${selectedEndpoint.method.toLowerCase()}">${selectedEndpoint.method}</span>
                </div>
                <div class="col-md-10">
                    <label for="test-url" class="form-label">URL:</label>
                    <input type="text" class="form-control" id="test-url" value="${selectedEndpoint.path}">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="test-headers" class="form-label">请求头 (JSON格式):</label>
                    <textarea class="form-control" id="test-headers" rows="4" placeholder='{"Content-Type": "application/json"}'></textarea>
                </div>
                <div class="col-md-6">
                    <label for="test-params" class="form-label">查询参数 (JSON格式):</label>
                    <textarea class="form-control" id="test-params" rows="4" placeholder='{"param1": "value1"}'></textarea>
                </div>
            </div>
    `;
    
    // 如果是POST/PUT/PATCH方法，显示请求体输入
    if (['POST', 'PUT', 'PATCH'].includes(selectedEndpoint.method)) {
        html += `
            <div class="mb-3">
                <label for="test-body" class="form-label">请求体:</label>
                <textarea class="form-control" id="test-body" rows="6" placeholder='{"key": "value"}'></textarea>
            </div>
        `;
    }
    
    // 显示参数信息
    if (selectedEndpoint.parameters && Object.keys(selectedEndpoint.parameters).length > 0) {
        html += `
            <div class="mb-3">
                <label class="form-label">参数信息:</label>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必需</th>
                                <th>默认值</th>
                            </tr>
                        </thead>
                        <tbody>
        `;
        
        Object.entries(selectedEndpoint.parameters).forEach(([name, param]) => {
            html += `
                <tr>
                    <td><code>${name}</code></td>
                    <td><small>${param.type}</small></td>
                    <td>${param.required ? '<span class="badge bg-danger">是</span>' : '<span class="badge bg-success">否</span>'}</td>
                    <td><small>${param.default || '-'}</small></td>
                </tr>
            `;
        });
        
        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }
    
    html += `
            <div class="d-flex justify-content-between">
                <button class="btn btn-primary btn-custom" onclick="executeTest()">
                    <i class="fas fa-play"></i> 执行测试
                </button>
                <button class="btn btn-outline-secondary btn-custom" onclick="clearTestForm()">
                    <i class="fas fa-eraser"></i> 清空表单
                </button>
            </div>
        </div>
    `;
    
    panel.innerHTML = html;
}

// 执行测试
async function executeTest() {
    if (!selectedEndpoint) return;
    
    try {
        // 收集测试数据
        const url = document.getElementById('test-url').value;
        const headersText = document.getElementById('test-headers').value;
        const paramsText = document.getElementById('test-params').value;
        const bodyText = document.getElementById('test-body')?.value || '';
        
        // 解析JSON
        let headers = {};
        let params = {};
        
        if (headersText.trim()) {
            try {
                headers = JSON.parse(headersText);
            } catch (e) {
                throw new Error('请求头JSON格式错误: ' + e.message);
            }
        }
        
        if (paramsText.trim()) {
            try {
                params = JSON.parse(paramsText);
            } catch (e) {
                throw new Error('查询参数JSON格式错误: ' + e.message);
            }
        }
        
        const testData = {
            url: url,
            method: selectedEndpoint.method,
            headers: headers,
            params: params,
            body: bodyText
        };
        
        // 显示加载状态
        const resultPanel = document.getElementById('result-panel');
        const resultDiv = document.getElementById('test-result');
        resultPanel.style.display = 'block';
        resultDiv.innerHTML = '<div class="text-center"><div class="loading-spinner"></div> 执行测试中...</div>';
        
        // 发送测试请求
        const result = await fetch('/debug/api/test-endpoint', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        }).then(response => response.json());
        
        // 显示结果
        renderTestResult(result);
        
        // 保存到历史
        saveToHistory(result);
        
    } catch (error) {
        console.error('测试执行失败:', error);
        const resultDiv = document.getElementById('test-result');
        resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ${error.message}</div>`;
    }
}

// 渲染测试结果
function renderTestResult(result) {
    const resultDiv = document.getElementById('test-result');
    
    if (!result.success) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> 测试失败</h6>
                <p>${result.error}</p>
            </div>
        `;
        return;
    }
    
    const statusClass = result.response.status_code < 300 ? 'status-success' : 
                       result.response.status_code < 400 ? 'status-warning' : 'status-error';
    
    let html = `
        <div class="row mb-3">
            <div class="col-md-3">
                <strong>状态码:</strong><br>
                <span class="${statusClass} fs-5">${result.response.status_code} ${result.response.status_text}</span>
            </div>
            <div class="col-md-3">
                <strong>响应时间:</strong><br>
                <span class="text-primary fs-5">${result.timing.response_time.toFixed(2)} ms</span>
            </div>
            <div class="col-md-3">
                <strong>响应大小:</strong><br>
                <span class="text-info fs-5">${formatBytes(result.response.size)}</span>
            </div>
            <div class="col-md-3">
                <strong>时间戳:</strong><br>
                <small class="text-muted">${new Date(result.timing.timestamp * 1000).toLocaleString('zh-CN')}</small>
            </div>
        </div>
        
        <ul class="nav nav-tabs" id="result-tabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="response-tab" data-bs-toggle="tab" data-bs-target="#response-pane" type="button">响应体</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="headers-tab" data-bs-toggle="tab" data-bs-target="#headers-pane" type="button">响应头</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="request-tab" data-bs-toggle="tab" data-bs-target="#request-pane" type="button">请求详情</button>
            </li>
        </ul>
        
        <div class="tab-content mt-3">
            <div class="tab-pane fade show active" id="response-pane">
                <div class="response-section">
                    <pre>${result.response.json ? JSON.stringify(result.response.json, null, 2) : result.response.body}</pre>
                </div>
            </div>
            <div class="tab-pane fade" id="headers-pane">
                <div class="response-section">
                    <pre>${JSON.stringify(result.response.headers, null, 2)}</pre>
                </div>
            </div>
            <div class="tab-pane fade" id="request-pane">
                <div class="response-section">
                    <pre>${JSON.stringify(result.request, null, 2)}</pre>
                </div>
            </div>
        </div>
    `;
    
    resultDiv.innerHTML = html;
}

// 保存到历史
function saveToHistory(result) {
    const historyItem = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        endpoint: selectedEndpoint,
        result: result
    };
    
    testHistory.unshift(historyItem);
    
    // 只保留最近50条记录
    if (testHistory.length > 50) {
        testHistory = testHistory.slice(0, 50);
    }
    
    localStorage.setItem('api_test_history', JSON.stringify(testHistory));
    loadTestHistory();
}

// 加载测试历史
function loadTestHistory() {
    const historyDiv = document.getElementById('test-history');
    
    if (testHistory.length === 0) {
        historyDiv.innerHTML = '<div class="text-center text-muted"><p>暂无测试历史</p></div>';
        return;
    }
    
    let html = '';
    testHistory.slice(0, 10).forEach(item => {
        const statusClass = item.result.success && item.result.response.status_code < 300 ? 'status-success' : 'status-error';
        const timestamp = new Date(item.timestamp).toLocaleString('zh-CN');
        
        html += `
            <div class="history-item">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <span class="method-badge method-${item.endpoint.method.toLowerCase()}">${item.endpoint.method}</span>
                        <strong class="ms-2">${item.endpoint.path}</strong>
                    </div>
                    <div>
                        ${item.result.success ? 
                            `<span class="${statusClass}">${item.result.response.status_code}</span>` :
                            '<span class="status-error">失败</span>'
                        }
                        <small class="text-muted ms-2">${timestamp}</small>
                    </div>
                </div>
                ${item.result.success ? 
                    `<small class="text-muted">响应时间: ${item.result.timing.response_time.toFixed(2)}ms</small>` :
                    `<small class="text-danger">错误: ${item.result.error}</small>`
                }
            </div>
        `;
    });
    
    historyDiv.innerHTML = html;
}

// 清空表单
function clearTestForm() {
    document.getElementById('test-headers').value = '';
    document.getElementById('test-params').value = '';
    if (document.getElementById('test-body')) {
        document.getElementById('test-body').value = '';
    }
}

// 清空历史
function clearHistory() {
    if (confirm('确定要清空所有测试历史吗？')) {
        testHistory = [];
        localStorage.removeItem('api_test_history');
        loadTestHistory();
    }
}

// 刷新端点列表
function refreshEndpoints() {
    initializeAPITest();
}
</script>
{% endblock %}
