"""
AI Gen Hub 健康检查系统

提供系统健康检查功能，包括：
- 应用健康状态
- 供应商健康状态
- 依赖服务健康状态
- 详细的健康报告
"""

import asyncio
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional

from ai_gen_hub.core.logging import LoggerMixin


class HealthStatus(str, Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"  # 健康
    DEGRADED = "degraded"  # 降级
    UNHEALTHY = "unhealthy"  # 不健康
    UNKNOWN = "unknown"  # 未知


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    name: str  # 检查项名称
    status: HealthStatus  # 健康状态
    message: str = ""  # 状态消息
    details: Dict[str, Any] = field(default_factory=dict)  # 详细信息
    duration: float = 0.0  # 检查耗时（秒）
    timestamp: float = field(default_factory=time.time)  # 检查时间戳
    
    @property
    def is_healthy(self) -> bool:
        """是否健康"""
        return self.status == HealthStatus.HEALTHY
    
    @property
    def is_degraded(self) -> bool:
        """是否降级"""
        return self.status == HealthStatus.DEGRADED
    
    @property
    def is_unhealthy(self) -> bool:
        """是否不健康"""
        return self.status == HealthStatus.UNHEALTHY


@dataclass
class HealthReport:
    """健康报告"""
    overall_status: HealthStatus  # 总体状态
    checks: List[HealthCheckResult]  # 检查结果列表
    timestamp: float = field(default_factory=time.time)  # 报告时间戳
    duration: float = 0.0  # 总检查耗时
    
    @property
    def healthy_count(self) -> int:
        """健康检查项数量"""
        return sum(1 for check in self.checks if check.is_healthy)
    
    @property
    def degraded_count(self) -> int:
        """降级检查项数量"""
        return sum(1 for check in self.checks if check.is_degraded)
    
    @property
    def unhealthy_count(self) -> int:
        """不健康检查项数量"""
        return sum(1 for check in self.checks if check.is_unhealthy)
    
    @property
    def total_count(self) -> int:
        """总检查项数量"""
        return len(self.checks)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "overall_status": self.overall_status,
            "timestamp": self.timestamp,
            "duration": self.duration,
            "summary": {
                "total": self.total_count,
                "healthy": self.healthy_count,
                "degraded": self.degraded_count,
                "unhealthy": self.unhealthy_count
            },
            "checks": [
                {
                    "name": check.name,
                    "status": check.status,
                    "message": check.message,
                    "details": check.details,
                    "duration": check.duration,
                    "timestamp": check.timestamp
                }
                for check in self.checks
            ]
        }


class HealthChecker(LoggerMixin):
    """健康检查器基类"""
    
    def __init__(self, name: str, timeout: float = 10.0):
        """初始化健康检查器
        
        Args:
            name: 检查器名称
            timeout: 检查超时时间
        """
        self.name = name
        self.timeout = timeout
    
    async def check(self) -> HealthCheckResult:
        """执行健康检查
        
        Returns:
            健康检查结果
        """
        start_time = time.time()
        
        try:
            # 使用超时执行检查
            result = await asyncio.wait_for(
                self._perform_check(),
                timeout=self.timeout
            )
            
            duration = time.time() - start_time
            result.duration = duration
            
            return result
            
        except asyncio.TimeoutError:
            duration = time.time() - start_time
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"健康检查超时（{self.timeout}秒）",
                duration=duration
            )
        except Exception as e:
            duration = time.time() - start_time
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"健康检查失败: {str(e)}",
                duration=duration
            )
    
    async def _perform_check(self) -> HealthCheckResult:
        """执行具体的健康检查（子类实现）"""
        raise NotImplementedError


class DatabaseHealthChecker(HealthChecker):
    """数据库健康检查器"""
    
    def __init__(self, db_pool, timeout: float = 5.0):
        """初始化数据库健康检查器
        
        Args:
            db_pool: 数据库连接池
            timeout: 检查超时时间
        """
        super().__init__("database", timeout)
        self.db_pool = db_pool
    
    async def _perform_check(self) -> HealthCheckResult:
        """执行数据库健康检查"""
        if not self.db_pool:
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message="数据库连接池未配置"
            )
        
        try:
            # 执行简单查询
            async with self.db_pool.acquire() as conn:
                await conn.execute("SELECT 1")
            
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.HEALTHY,
                message="数据库连接正常",
                details={
                    "pool_size": self.db_pool.get_size(),
                    "checked_out": self.db_pool.checked_out(),
                    "overflow": self.db_pool.overflow(),
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"数据库连接失败: {str(e)}"
            )


class RedisHealthChecker(HealthChecker):
    """Redis健康检查器"""
    
    def __init__(self, redis_client, timeout: float = 5.0):
        """初始化Redis健康检查器
        
        Args:
            redis_client: Redis客户端
            timeout: 检查超时时间
        """
        super().__init__("redis", timeout)
        self.redis_client = redis_client
    
    async def _perform_check(self) -> HealthCheckResult:
        """执行Redis健康检查"""
        if not self.redis_client:
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message="Redis客户端未配置"
            )
        
        try:
            # 执行ping命令
            pong = await self.redis_client.ping()
            
            if pong:
                # 获取Redis信息
                info = await self.redis_client.info()
                
                return HealthCheckResult(
                    name=self.name,
                    status=HealthStatus.HEALTHY,
                    message="Redis连接正常",
                    details={
                        "redis_version": info.get("redis_version"),
                        "used_memory_human": info.get("used_memory_human"),
                        "connected_clients": info.get("connected_clients"),
                        "uptime_in_seconds": info.get("uptime_in_seconds"),
                    }
                )
            else:
                return HealthCheckResult(
                    name=self.name,
                    status=HealthStatus.UNHEALTHY,
                    message="Redis ping失败"
                )
                
        except Exception as e:
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Redis连接失败: {str(e)}"
            )


class ProviderHealthChecker(HealthChecker):
    """供应商健康检查器"""
    
    def __init__(self, provider_manager, timeout: float = 10.0):
        """初始化供应商健康检查器
        
        Args:
            provider_manager: 供应商管理器
            timeout: 检查超时时间
        """
        super().__init__("providers", timeout)
        self.provider_manager = provider_manager
    
    async def _perform_check(self) -> HealthCheckResult:
        """执行供应商健康检查"""
        if not self.provider_manager:
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message="供应商管理器未配置"
            )
        
        try:
            # 获取所有供应商的健康状态
            health_results = await self.provider_manager.health_check_all()
            
            total_providers = len(health_results)
            healthy_providers = sum(1 for status in health_results.values() if status)
            
            if total_providers == 0:
                return HealthCheckResult(
                    name=self.name,
                    status=HealthStatus.UNHEALTHY,
                    message="没有配置的供应商"
                )
            
            # 计算健康比例
            health_ratio = healthy_providers / total_providers
            
            if health_ratio >= 0.8:
                status = HealthStatus.HEALTHY
                message = f"供应商状态良好 ({healthy_providers}/{total_providers})"
            elif health_ratio >= 0.5:
                status = HealthStatus.DEGRADED
                message = f"部分供应商不可用 ({healthy_providers}/{total_providers})"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"大部分供应商不可用 ({healthy_providers}/{total_providers})"
            
            return HealthCheckResult(
                name=self.name,
                status=status,
                message=message,
                details={
                    "total_providers": total_providers,
                    "healthy_providers": healthy_providers,
                    "health_ratio": health_ratio,
                    "provider_status": health_results
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"供应商健康检查失败: {str(e)}"
            )


class SystemHealthChecker(HealthChecker):
    """系统健康检查器"""
    
    def __init__(self, timeout: float = 5.0):
        """初始化系统健康检查器
        
        Args:
            timeout: 检查超时时间
        """
        super().__init__("system", timeout)
    
    async def _perform_check(self) -> HealthCheckResult:
        """执行系统健康检查"""
        try:
            import psutil
            
            # 获取系统信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 判断系统状态
            status = HealthStatus.HEALTHY
            issues = []
            
            if cpu_percent > 90:
                status = HealthStatus.DEGRADED
                issues.append(f"CPU使用率过高: {cpu_percent}%")
            
            if memory.percent > 90:
                status = HealthStatus.DEGRADED
                issues.append(f"内存使用率过高: {memory.percent}%")
            
            if disk.percent > 90:
                status = HealthStatus.DEGRADED
                issues.append(f"磁盘使用率过高: {disk.percent}%")
            
            if cpu_percent > 95 or memory.percent > 95 or disk.percent > 95:
                status = HealthStatus.UNHEALTHY
            
            message = "系统状态正常" if status == HealthStatus.HEALTHY else "; ".join(issues)
            
            return HealthCheckResult(
                name=self.name,
                status=status,
                message=message,
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available": memory.available,
                    "disk_percent": disk.percent,
                    "disk_free": disk.free,
                }
            )
            
        except ImportError:
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNKNOWN,
                message="psutil未安装，无法获取系统信息"
            )
        except Exception as e:
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"系统检查失败: {str(e)}"
            )


class HealthManager(LoggerMixin):
    """健康管理器"""
    
    def __init__(self):
        """初始化健康管理器"""
        self.checkers: List[HealthChecker] = []
        self.last_report: Optional[HealthReport] = None
    
    def add_checker(self, checker: HealthChecker) -> None:
        """添加健康检查器
        
        Args:
            checker: 健康检查器
        """
        self.checkers.append(checker)
        self.logger.info("添加健康检查器", name=checker.name)
    
    def remove_checker(self, name: str) -> None:
        """移除健康检查器
        
        Args:
            name: 检查器名称
        """
        self.checkers = [c for c in self.checkers if c.name != name]
        self.logger.info("移除健康检查器", name=name)
    
    async def check_health(self) -> HealthReport:
        """执行健康检查
        
        Returns:
            健康报告
        """
        start_time = time.time()
        
        # 并发执行所有健康检查
        check_tasks = [checker.check() for checker in self.checkers]
        check_results = await asyncio.gather(*check_tasks, return_exceptions=True)
        
        # 处理检查结果
        results = []
        for i, result in enumerate(check_results):
            if isinstance(result, Exception):
                # 检查器本身出错
                results.append(HealthCheckResult(
                    name=self.checkers[i].name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"检查器异常: {str(result)}"
                ))
            else:
                results.append(result)
        
        # 计算总体状态
        overall_status = self._calculate_overall_status(results)
        
        duration = time.time() - start_time
        
        # 创建健康报告
        report = HealthReport(
            overall_status=overall_status,
            checks=results,
            duration=duration
        )
        
        self.last_report = report
        
        self.logger.info(
            "健康检查完成",
            overall_status=overall_status,
            duration=duration,
            total_checks=len(results),
            healthy=report.healthy_count,
            degraded=report.degraded_count,
            unhealthy=report.unhealthy_count
        )
        
        return report
    
    def _calculate_overall_status(self, results: List[HealthCheckResult]) -> HealthStatus:
        """计算总体健康状态
        
        Args:
            results: 检查结果列表
            
        Returns:
            总体健康状态
        """
        if not results:
            return HealthStatus.UNKNOWN
        
        unhealthy_count = sum(1 for r in results if r.is_unhealthy)
        degraded_count = sum(1 for r in results if r.is_degraded)
        
        # 如果有任何不健康的检查项，总体状态为不健康
        if unhealthy_count > 0:
            return HealthStatus.UNHEALTHY
        
        # 如果有降级的检查项，总体状态为降级
        if degraded_count > 0:
            return HealthStatus.DEGRADED
        
        # 所有检查项都健康
        return HealthStatus.HEALTHY
    
    def get_last_report(self) -> Optional[HealthReport]:
        """获取最后一次健康报告
        
        Returns:
            最后一次健康报告
        """
        return self.last_report
