#!/usr/bin/env python3
"""
测试调试界面访问的脚本

用于验证认证中间件修复是否有效，确保在开发环境下可以正常访问调试界面。
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

async def test_debug_access():
    """测试调试界面访问"""
    print("🔍 测试调试界面访问...")
    
    # 设置环境变量
    os.environ.setdefault('ENVIRONMENT', 'development')
    os.environ.setdefault('DEBUG', 'true')
    
    try:
        # 导入必要的模块
        from ai_gen_hub.config import get_settings
        from ai_gen_hub.api.app import AIGenHubApp
        from ai_gen_hub.api.middleware import AuthenticationMiddleware
        from fastapi.testclient import TestClient
        
        print("✅ 模块导入成功")
        
        # 获取配置
        settings = get_settings()
        print(f"📋 配置信息:")
        print(f"   环境: {settings.environment}")
        print(f"   调试模式: {settings.debug}")
        print(f"   API密钥: {'已配置' if settings.security.api_key else '未配置'}")
        print(f"   JWT密钥: {'已配置' if settings.security.jwt_secret_key else '未配置'}")
        
        # 创建应用
        app_instance = AIGenHubApp()
        app = app_instance.create_app()
        print("✅ FastAPI应用创建成功")
        
        # 检查中间件
        middleware_names = [type(middleware).__name__ for middleware in app.user_middleware]
        print(f"📦 中间件列表: {middleware_names}")
        
        has_auth_middleware = any('Authentication' in name for name in middleware_names)
        print(f"🔐 认证中间件: {'已添加' if has_auth_middleware else '未添加'}")
        
        # 测试路径识别
        auth_middleware = AuthenticationMiddleware(app)
        test_paths = [
            "/",
            "/health",
            "/debug",
            "/debug/",
            "/debug/api/system/info",
            "/api/v1/text/generate",
        ]
        
        print("\n🛣️  路径识别测试:")
        for path in test_paths:
            is_public = auth_middleware._is_public_path(path)
            print(f"   {path}: {'公开' if is_public else '需要认证'}")
        
        # 使用测试客户端测试实际请求
        print("\n🌐 HTTP请求测试:")
        
        # 初始化应用状态
        await app_instance.initialize()
        
        # 手动设置应用状态
        app.state.settings = settings
        app.state.key_manager = app_instance.key_manager
        app.state.provider_manager = app_instance.provider_manager
        app.state.router = app_instance.router
        app.state.cache = app_instance.cache
        app.state.health_manager = app_instance.health_manager
        app.state.text_service = app_instance.text_service
        app.state.image_service = app_instance.image_service
        
        with TestClient(app) as client:
            # 测试健康检查
            response = client.get("/health")
            print(f"   GET /health: {response.status_code}")
            
            # 测试调试主页
            response = client.get("/debug/")
            print(f"   GET /debug/: {response.status_code}")
            if response.status_code != 200:
                print(f"     错误详情: {response.text}")
            
            # 测试调试API
            response = client.get("/debug/api/system/info")
            print(f"   GET /debug/api/system/info: {response.status_code}")
            if response.status_code != 200:
                print(f"     错误详情: {response.text}")
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

async def main():
    """主函数"""
    print("🚀 开始测试调试界面访问...")
    print("=" * 50)
    
    success = await test_debug_access()
    
    print("=" * 50)
    if success:
        print("🎉 测试成功！调试界面应该可以正常访问了。")
        print("\n💡 建议:")
        print("1. 启动服务器: python -m ai_gen_hub.main serve --reload")
        print("2. 访问调试界面: http://localhost:8000/debug/")
        print("3. 如果仍有问题，请检查服务器日志")
    else:
        print("💥 测试失败！请检查错误信息并修复问题。")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
