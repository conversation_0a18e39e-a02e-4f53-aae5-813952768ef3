# AI Gen Hub 图像生成使用指南

## 概述

AI Gen Hub 现在完全支持图像生成功能，集成了多个 AI 供应商的图像生成模型，包括 Google AI (Gemini) 和 OpenAI (DALL-E)。

## 支持的供应商和模型

### Google AI (Gemini)

#### 推荐模型
- `gemini-image` - 便捷别名，映射到最新的图像生成模型
- `gemini-img` - 简短别名
- `image-generation` - 通用别名

#### 专用图像生成模型
- `gemini-2.0-flash-preview-image-generation` - 图像生成预览版（推荐）
- `gemini-2.0-flash-image-generation` - 图像生成正式版
- `gemini-image-generation` - 通用图像生成别名

#### 多模态模型（支持图像生成）
- `gemini-2.0-flash` - 超快速度，支持文本+图像输出
- `gemini-2.0-flash-lite` - 轻量级版本

### OpenAI

- `dall-e-2` - DALL-E 2 图像生成模型
- `dall-e-3` - DALL-E 3 图像生成模型（推荐）

## API 使用示例

### 1. 基本图像生成

#### 使用 Google AI (推荐)

```python
import requests

# 使用便捷别名
response = requests.post("http://localhost:8000/api/v1/images/generate", json={
    "prompt": "一只在花园里玩耍的可爱小猫",
    "model": "gemini-image",
    "n": 1,
    "size": "1024x1024"
})

# 使用具体模型名称
response = requests.post("http://localhost:8000/api/v1/images/generate", json={
    "prompt": "一只在花园里玩耍的可爱小猫",
    "model": "gemini-2.0-flash-preview-image-generation",
    "n": 1,
    "size": "1024x1024"
})
```

#### 使用 OpenAI

```python
response = requests.post("http://localhost:8000/api/v1/images/generate", json={
    "prompt": "一只在花园里玩耍的可爱小猫",
    "model": "dall-e-3",
    "n": 1,
    "size": "1024x1024",
    "quality": "hd"
})
```

### 2. 自动模型选择

```python
# 不指定模型，系统会自动选择最佳可用供应商
response = requests.post("http://localhost:8000/api/v1/images/generate", json={
    "prompt": "一只在花园里玩耍的可爱小猫",
    "n": 1,
    "size": "1024x1024"
})
```

### 3. Gemini 多模态图像生成

```python
# 同时生成图像和文本描述
response = requests.post("http://localhost:8000/api/v1/images/generate", json={
    "prompt": "请生成一张美丽的风景图片并描述其内容",
    "model": "gemini-2.0-flash",
    "response_modalities": ["TEXT", "IMAGE"],
    "n": 1
})
```

### 4. 图像编辑（Gemini 特有功能）

```python
# 基于输入图像进行编辑
response = requests.post("http://localhost:8000/api/v1/images/generate", json={
    "prompt": "将这张图片的背景改为海滩",
    "model": "gemini-image",
    "input_images": ["base64_encoded_image_data"],
    "edit_instruction": "更换背景为海滩场景",
    "n": 1
})
```

## 响应格式

```json
{
    "created": **********,
    "data": [
        {
            "url": "https://example.com/generated-image.png",
            "b64_json": "base64_encoded_image_data",
            "revised_prompt": "修订后的提示词"
        }
    ],
    "provider": "google_ai",
    "request_id": "req_123456789",
    "processing_time": 2.5
}
```

## 参数说明

### 基本参数

- `prompt` (必需): 图像描述提示词
- `model` (可选): 指定使用的模型，不指定则自动选择
- `n` (可选): 生成图像数量，默认为 1，最大为 10
- `size` (可选): 图像尺寸，默认为 "1024x1024"
- `quality` (可选): 图像质量，"standard" 或 "hd"

### Google AI 特有参数

- `response_modalities` (可选): 响应模态，如 ["TEXT", "IMAGE"]
- `input_images` (可选): 输入图像列表（用于图像编辑）
- `edit_instruction` (可选): 图像编辑指令

### OpenAI 特有参数

- `style` (可选): 图像风格，如 "vivid" 或 "natural"
- `response_format` (可选): 响应格式，"url" 或 "b64_json"

## 错误处理

### 常见错误

1. **模型不支持**
```json
{
    "error": {
        "code": "MODEL_NOT_SUPPORTED",
        "message": "模型 'unsupported-model' 不受支持"
    }
}
```

2. **供应商不可用**
```json
{
    "error": {
        "code": "PROVIDER_UNAVAILABLE", 
        "message": "没有支持 image_generation 的可用供应商"
    }
}
```

3. **API 密钥无效**
```json
{
    "error": {
        "code": "AUTHENTICATION_ERROR",
        "message": "API 密钥无效或已过期"
    }
}
```

## 最佳实践

### 1. 模型选择建议

- **快速原型**: 使用 `gemini-image` 别名
- **高质量图像**: 使用 `dall-e-3`
- **多模态需求**: 使用 `gemini-2.0-flash`
- **成本敏感**: 使用 `gemini-2.0-flash-lite`

### 2. 提示词优化

- **具体描述**: 提供详细的视觉描述
- **风格指定**: 明确指定艺术风格或摄影风格
- **构图说明**: 描述画面构图和视角
- **色彩要求**: 指定色调和色彩偏好

### 3. 性能优化

- **批量生成**: 使用 `n` 参数一次生成多张图像
- **缓存利用**: 相同提示词会使用缓存结果
- **负载均衡**: 不指定模型让系统自动选择最佳供应商

## 配置要求

### 环境变量

确保在 `.env` 文件中配置了相应的 API 密钥：

```bash
# Google AI 配置
GOOGLE_AI_API_KEYS=your-google-ai-key-1,your-google-ai-key-2
GOOGLE_AI_ENABLED=true

# OpenAI 配置  
OPENAI_API_KEYS=your-openai-key-1,your-openai-key-2
OPENAI_ENABLED=true
```

### 功能开关

```bash
# 启用图像生成功能
ENABLE_IMAGE_GENERATION=true
```

## 故障排除

### 1. 检查供应商状态

```bash
# 健康检查
curl http://localhost:8000/api/v1/health

# 获取支持的模型
curl http://localhost:8000/api/v1/images/models
```

### 2. 查看日志

```bash
# 查看应用日志
tail -f logs/ai-gen-hub.log
```

### 3. 测试连接

```bash
# 测试 Google AI 连接
python -c "
import os
os.environ['GOOGLE_AI_API_KEYS'] = 'your-key'
from ai_gen_hub.main import cli
cli(['health-check', '--provider', 'google_ai'])
"
```

## 更新日志

- **2025-08-14**: 修复图像生成功能，添加 Google AI 支持
- **2025-08-14**: 添加模型别名和映射功能
- **2025-08-14**: 优化模型过滤和路由逻辑

## 支持

如有问题，请查看：
1. [图像生成功能修复报告](./图像生成功能修复报告.md)
2. [Google AI 供应商文档](./docs/providers/google_ai_provider.md)
3. [API 文档](./docs/api/)

---

**注意**: 使用图像生成功能需要有效的 API 密钥和足够的配额。请确保遵守各供应商的使用条款和限制。
