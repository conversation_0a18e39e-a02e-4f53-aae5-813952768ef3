---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-gen-hub-hpa
  namespace: ai-gen-hub
  labels:
    app: ai-gen-hub
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-gen-hub
  minReplicas: 2
  maxReplicas: 20
  metrics:
  # CPU 使用率
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  
  # 内存使用率
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  
  # 自定义指标 - 请求数量 (需要 Prometheus Adapter)
  - type: Pods
    pods:
      metric:
        name: ai_gen_hub_requests_per_second
      target:
        type: AverageValue
        averageValue: "50"
  
  # 扩缩行为配置
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 5分钟稳定窗口
      policies:
      - type: Percent
        value: 10  # 每次最多缩减10%
        periodSeconds: 60
      - type: Pods
        value: 2   # 每次最多缩减2个Pod
        periodSeconds: 60
      selectPolicy: Min  # 选择最保守的策略
    
    scaleUp:
      stabilizationWindowSeconds: 60   # 1分钟稳定窗口
      policies:
      - type: Percent
        value: 50  # 每次最多扩展50%
        periodSeconds: 60
      - type: Pods
        value: 4   # 每次最多增加4个Pod
        periodSeconds: 60
      selectPolicy: Max  # 选择最激进的策略

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: ai-gen-hub-pdb
  namespace: ai-gen-hub
  labels:
    app: ai-gen-hub
spec:
  minAvailable: 1  # 至少保持1个Pod可用
  selector:
    matchLabels:
      app: ai-gen-hub
