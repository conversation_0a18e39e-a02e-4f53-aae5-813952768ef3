#!/usr/bin/env python3
"""
AI Gen Hub JWT Token 生成工具

用于生成和验证JWT认证令牌
"""

import sys
import os
import jwt
from datetime import datetime, timedelta
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def load_env_file():
    """加载 .env 文件"""
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

def generate_jwt_token(user_id: str = "api_user", expire_minutes: int = None) -> str:
    """生成JWT token
    
    Args:
        user_id: 用户ID
        expire_minutes: 过期时间（分钟），默认使用配置值
    
    Returns:
        JWT token字符串
    """
    # 加载环境变量
    load_env_file()
    
    # 获取配置
    secret_key = os.environ.get('JWT_SECRET_KEY', 'dev-secret-key-change-in-production')
    algorithm = os.environ.get('JWT_ALGORITHM', 'HS256')
    default_expire = int(os.environ.get('JWT_EXPIRE_MINUTES', '1440'))
    
    if expire_minutes is None:
        expire_minutes = default_expire
    
    # 创建payload
    now = datetime.utcnow()
    payload = {
        'sub': user_id,  # subject (用户ID)
        'iat': now,      # issued at (签发时间)
        'exp': now + timedelta(minutes=expire_minutes),  # expiration time (过期时间)
        'type': 'access_token',
        'scope': 'api_access'
    }
    
    # 生成token
    token = jwt.encode(payload, secret_key, algorithm=algorithm)
    
    return token

def verify_jwt_token(token: str) -> dict:
    """验证JWT token
    
    Args:
        token: JWT token字符串
    
    Returns:
        解码后的payload
    
    Raises:
        jwt.ExpiredSignatureError: token已过期
        jwt.InvalidTokenError: token无效
    """
    # 加载环境变量
    load_env_file()
    
    # 获取配置
    secret_key = os.environ.get('JWT_SECRET_KEY', 'dev-secret-key-change-in-production')
    algorithm = os.environ.get('JWT_ALGORITHM', 'HS256')
    
    # 验证token
    payload = jwt.decode(token, secret_key, algorithms=[algorithm])
    
    return payload

def print_token_info(token: str):
    """打印token信息"""
    try:
        payload = verify_jwt_token(token)
        
        print("🎯 Token信息:")
        print(f"   用户ID: {payload.get('sub')}")
        print(f"   签发时间: {datetime.fromtimestamp(payload.get('iat')).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   过期时间: {datetime.fromtimestamp(payload.get('exp')).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Token类型: {payload.get('type')}")
        print(f"   权限范围: {payload.get('scope')}")
        
        # 检查是否过期
        exp_time = datetime.fromtimestamp(payload.get('exp'))
        if exp_time > datetime.utcnow():
            remaining = exp_time - datetime.utcnow()
            print(f"   剩余时间: {remaining}")
            print("   ✅ Token有效")
        else:
            print("   ❌ Token已过期")
            
    except jwt.ExpiredSignatureError:
        print("❌ Token已过期")
    except jwt.InvalidTokenError as e:
        print(f"❌ Token无效: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='AI Gen Hub JWT Token 生成工具')
    parser.add_argument('action', choices=['generate', 'verify'], help='操作类型')
    parser.add_argument('--user-id', default='api_user', help='用户ID (默认: api_user)')
    parser.add_argument('--expire-minutes', type=int, help='过期时间（分钟）')
    parser.add_argument('--token', help='要验证的token')
    
    args = parser.parse_args()
    
    if args.action == 'generate':
        print("🔐 生成JWT Token")
        print("=" * 50)
        
        # 显示配置信息
        load_env_file()
        secret_key = os.environ.get('JWT_SECRET_KEY', 'dev-secret-key-change-in-production')
        algorithm = os.environ.get('JWT_ALGORITHM', 'HS256')
        default_expire = int(os.environ.get('JWT_EXPIRE_MINUTES', '1440'))
        
        print(f"📋 配置信息:")
        print(f"   JWT密钥: {secret_key[:10]}...{secret_key[-4:]}")
        print(f"   算法: {algorithm}")
        print(f"   默认过期时间: {default_expire} 分钟")
        print()
        
        # 生成token
        token = generate_jwt_token(args.user_id, args.expire_minutes)
        
        print(f"🎯 生成的Token:")
        print(f"   用户ID: {args.user_id}")
        print(f"   过期时间: {args.expire_minutes or default_expire} 分钟")
        print()
        print(f"📝 JWT Token:")
        print(token)
        print()
        
        # 显示使用示例
        print("🚀 使用示例:")
        print("1. API Key 方式:")
        print(f"   curl -H 'X-API-Key: {os.environ.get('API_KEY', 'dev-api-key')}' http://localhost:8001/api/v1/text/models")
        print()
        print("2. JWT Token 方式:")
        print(f"   curl -H 'Authorization: Bearer {token}' http://localhost:8001/api/v1/text/models")
        print()
        print("3. 查询参数方式:")
        print(f"   curl 'http://localhost:8001/api/v1/text/models?api_key={os.environ.get('API_KEY', 'dev-api-key')}'")
        
    elif args.action == 'verify':
        if not args.token:
            print("❌ 请提供要验证的token (--token)")
            sys.exit(1)
        
        print("🔍 验证JWT Token")
        print("=" * 50)
        print(f"Token: {args.token[:20]}...{args.token[-10:]}")
        print()
        
        print_token_info(args.token)

if __name__ == "__main__":
    main()
