#!/bin/bash
# AI Gen Hub 一键启动脚本

set -e

echo "🌟 AI Gen Hub 一键启动脚本"
echo "=" * 50

# 项目目录
PROJECT_DIR="/root/workspace/git.atjog.com/aier/ai-gen-hub"
cd "$PROJECT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查虚拟环境
check_venv() {
    log_info "检查虚拟环境..."
    
    if [ ! -d "venv" ]; then
        log_warning "虚拟环境不存在，正在创建..."
        python3 -m venv venv
        log_success "虚拟环境创建完成"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    log_success "虚拟环境已激活"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 升级pip
    pip install --upgrade pip wheel setuptools
    
    # 安装基础依赖
    pip install fastapi uvicorn pydantic pydantic-settings click httpx
    pip install structlog aioredis jinja2 psutil
    
    # 如果存在requirements.txt，安装所有依赖
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
    fi
    
    log_success "依赖安装完成"
}

# 安装项目
install_project() {
    log_info "安装AI Gen Hub项目..."
    
    # 以开发模式安装项目
    pip install -e .
    
    log_success "项目安装完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 测试模块导入
    if python -c "import ai_gen_hub; print('模块导入成功')" 2>/dev/null; then
        log_success "模块导入验证通过"
        return 0
    else
        log_warning "模块导入失败，使用备用方案"
        return 1
    fi
}

# 创建配置文件
create_config() {
    log_info "创建配置文件..."
    
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# AI Gen Hub 开发环境配置
ENVIRONMENT=development
DEBUG=true
API_HOST=0.0.0.0
API_PORT=8001

# 安全配置
JWT_SECRET_KEY=dev-secret-key-change-in-production
API_KEY=dev-api-key

# 特性开关
ENABLE_TEXT_GENERATION=true
ENABLE_IMAGE_GENERATION=true
ENABLE_CACHING=true

# AI供应商配置（请填入真实的API密钥）
# OPENAI_API_KEYS=sk-your-openai-key
# GOOGLE_AI_API_KEYS=your-google-ai-key
# ANTHROPIC_API_KEYS=sk-ant-your-anthropic-key

# Redis缓存（可选）
# REDIS_URL=redis://localhost:6379/0

# 数据库（可选）
# DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/ai_gen_hub
EOF
        log_success "配置文件 .env 已创建"
        log_warning "请编辑 .env 文件，添加您的AI供应商API密钥"
    else
        log_info "配置文件 .env 已存在"
    fi
}

# 启动服务
start_services() {
    log_info "启动AI Gen Hub服务..."
    
    # 检查是否可以使用标准方式启动
    if verify_installation; then
        log_info "使用标准方式启动服务"
        echo ""
        echo "🚀 启动命令:"
        echo "   python -m ai_gen_hub.main serve"
        echo "   或者:"
        echo "   ai-gen-hub serve"
        echo ""
        echo "🔧 完整开发环境:"
        echo "   python start_with_debug.py"
        echo ""
        
        # 询问是否立即启动
        read -p "是否立即启动完整开发环境？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            python start_with_debug.py
        fi
    else
        log_info "使用备用方式启动服务"
        echo ""
        echo "🚀 启动命令:"
        echo "   python run_server.py serve"
        echo ""
        echo "🔧 调试页面:"
        echo "   python debug_standalone.py"
        echo ""
        
        # 询问是否立即启动
        read -p "是否立即启动服务？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            python run_server.py serve --host 0.0.0.0 --port 8001
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo ""
    echo "📖 使用说明:"
    echo ""
    echo "1. 配置AI供应商API密钥:"
    echo "   编辑 .env 文件，取消注释并填入真实的API密钥"
    echo ""
    echo "2. 启动服务:"
    echo "   - 完整环境: python start_with_debug.py"
    echo "   - 仅API服务: python run_server.py serve"
    echo "   - 仅调试页面: python debug_standalone.py"
    echo ""
    echo "3. 访问地址:"
    echo "   - API服务: http://localhost:8001"
    echo "   - API文档: http://localhost:8001/docs"
    echo "   - 调试页面: http://localhost:8000"
    echo ""
    echo "4. 故障排除:"
    echo "   - 查看日志获取详细错误信息"
    echo "   - 确保已配置至少一个AI供应商API密钥"
    echo "   - 检查端口是否被占用"
    echo ""
}

# 主函数
main() {
    # 检查是否在正确的目录
    if [ ! -f "pyproject.toml" ] || [ ! -d "src/ai_gen_hub" ]; then
        log_error "请在AI Gen Hub项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行安装步骤
    check_venv
    install_dependencies
    install_project
    create_config
    
    log_success "安装完成！"
    
    # 启动服务
    start_services
    
    # 显示帮助信息
    show_help
}

# 错误处理
trap 'log_error "脚本执行失败"; exit 1' ERR

# 运行主函数
main "$@"
