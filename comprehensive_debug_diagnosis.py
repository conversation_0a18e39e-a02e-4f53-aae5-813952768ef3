#!/usr/bin/env python3
"""
AI Gen Hub 调试界面综合诊断工具

专门诊断以下问题：
1. 端口配置分析
2. 404错误排查
3. 测试执行卡住问题
"""

import asyncio
import json
import os
import sys
import time
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

class DebugDiagnostic:
    """调试诊断器"""
    
    def __init__(self):
        self.results = {}
        self.issues = []
        self.recommendations = []
    
    def print_section(self, title: str):
        """打印章节标题"""
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print('='*60)
    
    def print_subsection(self, title: str):
        """打印子章节标题"""
        print(f"\n📋 {title}")
        print('-'*40)
    
    def add_issue(self, issue: str):
        """添加问题"""
        self.issues.append(issue)
        print(f"❌ {issue}")
    
    def add_recommendation(self, rec: str):
        """添加建议"""
        self.recommendations.append(rec)
    
    def check_port_configuration(self) -> Dict:
        """1. 端口配置分析"""
        self.print_section("端口配置分析")
        
        port_info = {
            'running_processes': [],
            'port_conflicts': [],
            'expected_ports': {
                8001: 'AI Gen Hub主服务端口（包含调试界面）',
                8002: '备用/测试端口'
            }
        }
        
        # 检查进程
        try:
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if any(keyword in line for keyword in ['uvicorn', 'ai_gen_hub', 'debug_standalone', 'start_with_debug']):
                        port_info['running_processes'].append(line.strip())
                        print(f"🔍 发现进程: {line.strip()}")
        except Exception as e:
            self.add_issue(f"无法检查进程: {e}")
        
        # 检查端口占用
        for port, description in port_info['expected_ports'].items():
            try:
                response = requests.get(f"http://localhost:{port}/", timeout=2)
                print(f"✅ 端口 {port} ({description}) 有服务运行 - 状态码: {response.status_code}")
                
                # 尝试获取服务信息
                try:
                    if response.headers.get('content-type', '').startswith('application/json'):
                        data = response.json()
                        print(f"   服务信息: {data.get('name', 'Unknown')}")
                except:
                    pass
                    
            except requests.exceptions.ConnectionError:
                print(f"⚪ 端口 {port} ({description}) 无服务")
            except requests.exceptions.Timeout:
                print(f"⏰ 端口 {port} ({description}) 连接超时")
            except Exception as e:
                print(f"❌ 端口 {port} 检查失败: {e}")
        
        self.results['port_configuration'] = port_info
        return port_info
    
    def check_debug_routes_404(self) -> Dict:
        """2. 404错误排查"""
        self.print_section("调试路由404错误排查")
        
        route_info = {
            'accessible_routes': [],
            'failed_routes': [],
            'route_registration_status': None
        }
        
        # 测试的调试路由
        debug_routes = [
            ('GET', '/debug/', '调试主页'),
            ('GET', '/debug/api/system/info', '系统信息'),
            ('GET', '/debug/api/config', '配置信息'),
            ('GET', '/debug/api/endpoints', '端点列表'),
            ('POST', '/debug/api/test-endpoint', 'API测试')
        ]
        
        # 尝试不同的端口
        test_ports = [8000, 8001, 8002]
        
        for port in test_ports:
            self.print_subsection(f"测试端口 {port}")
            
            for method, path, description in debug_routes:
                try:
                    if method == 'GET':
                        response = requests.get(f"http://localhost:{port}{path}", timeout=5)
                    elif method == 'POST':
                        # 为POST请求提供测试数据
                        test_data = {
                            "url": "/health",
                            "method": "GET",
                            "headers": {},
                            "body": "",
                            "params": {}
                        }
                        response = requests.post(f"http://localhost:{port}{path}", json=test_data, timeout=5)
                    
                    if response.status_code == 200:
                        print(f"✅ {method} {path} ({description}) - 成功")
                        route_info['accessible_routes'].append((port, method, path, description))
                    elif response.status_code == 404:
                        print(f"❌ {method} {path} ({description}) - 404 Not Found")
                        route_info['failed_routes'].append((port, method, path, description, '404'))
                        self.add_issue(f"端口{port}上的{path}返回404")
                    else:
                        print(f"⚠️ {method} {path} ({description}) - 状态码: {response.status_code}")
                        route_info['failed_routes'].append((port, method, path, description, response.status_code))
                        
                except requests.exceptions.ConnectionError:
                    print(f"⚪ {method} {path} ({description}) - 无连接")
                except requests.exceptions.Timeout:
                    print(f"⏰ {method} {path} ({description}) - 超时")
                except Exception as e:
                    print(f"❌ {method} {path} ({description}) - 错误: {e}")
                    route_info['failed_routes'].append((port, method, path, description, str(e)))
        
        self.results['debug_routes_404'] = route_info
        return route_info
    
    def check_test_execution_hang(self) -> Dict:
        """3. 测试执行卡住问题"""
        self.print_section("测试执行卡住问题诊断")
        
        hang_info = {
            'test_endpoint_status': None,
            'timeout_test_results': [],
            'async_issues': []
        }
        
        # 测试不同超时时间的请求
        timeout_tests = [1, 3, 5, 10]
        
        for port in [8000, 8001, 8002]:
            self.print_subsection(f"测试端口 {port} 的超时行为")
            
            for timeout in timeout_tests:
                try:
                    test_data = {
                        "url": "/health",
                        "method": "GET",
                        "headers": {},
                        "body": "",
                        "params": {}
                    }
                    
                    start_time = time.time()
                    response = requests.post(
                        f"http://localhost:{port}/debug/api/test-endpoint",
                        json=test_data,
                        timeout=timeout
                    )
                    end_time = time.time()
                    
                    duration = end_time - start_time
                    print(f"✅ 超时{timeout}s测试 - 耗时{duration:.2f}s - 状态码: {response.status_code}")
                    
                    hang_info['timeout_test_results'].append({
                        'port': port,
                        'timeout': timeout,
                        'duration': duration,
                        'status_code': response.status_code,
                        'success': True
                    })
                    
                    # 如果成功，检查响应内容
                    try:
                        data = response.json()
                        if not data.get('success'):
                            print(f"   ⚠️ API测试失败: {data.get('error', 'Unknown')}")
                    except:
                        pass
                    
                    break  # 成功了就不需要测试更长的超时
                    
                except requests.exceptions.Timeout:
                    print(f"⏰ 超时{timeout}s测试 - 请求超时")
                    hang_info['timeout_test_results'].append({
                        'port': port,
                        'timeout': timeout,
                        'duration': timeout,
                        'status_code': None,
                        'success': False,
                        'error': 'timeout'
                    })
                    
                except requests.exceptions.ConnectionError:
                    print(f"⚪ 超时{timeout}s测试 - 无连接")
                    break
                    
                except Exception as e:
                    print(f"❌ 超时{timeout}s测试 - 错误: {e}")
                    hang_info['timeout_test_results'].append({
                        'port': port,
                        'timeout': timeout,
                        'duration': None,
                        'status_code': None,
                        'success': False,
                        'error': str(e)
                    })
        
        self.results['test_execution_hang'] = hang_info
        return hang_info
    
    def analyze_results(self):
        """分析结果并提供建议"""
        self.print_section("问题分析和建议")
        
        # 分析端口配置问题
        port_config = self.results.get('port_configuration', {})
        if not port_config.get('running_processes'):
            self.add_recommendation("启动AI Gen Hub服务：python start_with_debug.py")
        
        # 分析404问题
        route_404 = self.results.get('debug_routes_404', {})
        accessible_routes = route_404.get('accessible_routes', [])
        failed_routes = route_404.get('failed_routes', [])
        
        if not accessible_routes:
            self.add_recommendation("调试路由完全不可访问，检查服务是否正确启动")
        elif failed_routes:
            # 按端口分组分析
            port_failures = {}
            for port, method, path, desc, error in failed_routes:
                if port not in port_failures:
                    port_failures[port] = []
                port_failures[port].append((method, path, desc, error))
            
            for port, failures in port_failures.items():
                if len(failures) > 3:  # 大部分路由都失败
                    self.add_recommendation(f"端口{port}的调试路由注册可能有问题，检查路由配置")
        
        # 分析测试卡住问题
        hang_info = self.results.get('test_execution_hang', {})
        timeout_results = hang_info.get('timeout_test_results', [])
        
        hanging_ports = []
        for result in timeout_results:
            if not result['success'] and result.get('error') == 'timeout':
                hanging_ports.append(result['port'])
        
        if hanging_ports:
            self.add_recommendation(f"端口{hanging_ports}的test-endpoint接口存在挂起问题，需要修复异步处理")
        
        # 输出建议
        if self.recommendations:
            self.print_subsection("修复建议")
            for i, rec in enumerate(self.recommendations, 1):
                print(f"{i}. {rec}")
        else:
            print("✅ 未发现明显问题")
    
    def generate_report(self):
        """生成诊断报告"""
        self.print_section("诊断报告")
        
        print(f"诊断时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"发现问题数量: {len(self.issues)}")
        print(f"修复建议数量: {len(self.recommendations)}")
        
        if self.issues:
            self.print_subsection("发现的问题")
            for i, issue in enumerate(self.issues, 1):
                print(f"{i}. {issue}")
        
        # 保存详细结果到文件
        report_file = "debug_diagnosis_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'issues': self.issues,
                'recommendations': self.recommendations,
                'detailed_results': self.results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

def main():
    """主诊断函数"""
    print("🚀 AI Gen Hub 调试界面综合诊断")
    print("=" * 60)
    
    diagnostic = DebugDiagnostic()
    
    try:
        # 1. 端口配置分析
        diagnostic.check_port_configuration()
        
        # 2. 404错误排查
        diagnostic.check_debug_routes_404()
        
        # 3. 测试执行卡住问题
        diagnostic.check_test_execution_hang()
        
        # 4. 分析结果
        diagnostic.analyze_results()
        
        # 5. 生成报告
        diagnostic.generate_report()
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 诊断被用户中断")
    except Exception as e:
        print(f"\n❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
