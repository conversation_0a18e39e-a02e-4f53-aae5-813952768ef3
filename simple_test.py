#!/usr/bin/env python3
"""
简单的调试页面测试

使用增强的配置管理器和通用测试工具进行简单测试。
"""

import sys
import time
from pathlib import Path

# 使用通用测试工具设置环境
sys.path.insert(0, str(Path(__file__).parent / "src"))
from ai_gen_hub.utils.test_utils import (
    setup_test_environment, TestRunner, AppTestUtils, ConfigTestUtils
)


def test_basic_endpoints():
    """测试基础端点

    Returns:
        测试结果字典
    """
    try:
        app = AppTestUtils.create_test_app()

        # 测试根端点
        root_result = AppTestUtils.test_endpoint(app, "/", "GET")

        # 测试健康检查端点
        health_result = AppTestUtils.test_endpoint(app, "/health", "GET")

        # 测试调试主页
        debug_result = AppTestUtils.test_endpoint(app, "/debug/", "GET")

        results = {
            'root_endpoint': root_result['success'],
            'health_endpoint': health_result['success'],
            'debug_endpoint': debug_result['success'],
            'total_tests': 3,
            'passed_tests': sum([
                root_result['success'],
                health_result['success'],
                debug_result['success']
            ])
        }

        results['success'] = results['passed_tests'] == results['total_tests']

        if not results['success']:
            failed_endpoints = []
            if not root_result['success']:
                failed_endpoints.append(f"根端点: {root_result.get('error', '未知错误')}")
            if not health_result['success']:
                failed_endpoints.append(f"健康检查: {health_result.get('error', '未知错误')}")
            if not debug_result['success']:
                failed_endpoints.append(f"调试页面: {debug_result.get('error', '未知错误')}")

            results['error'] = f"失败的端点: {'; '.join(failed_endpoints)}"

        return results

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def test_debug_routes_loading():
    """测试调试路由加载

    Returns:
        测试结果字典
    """
    try:
        from ai_gen_hub.api.app import create_app

        app = create_app()

        # 检查路由注册
        debug_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and '/debug' in route.path:
                debug_routes.append(route.path)

        return {
            'success': len(debug_routes) > 0,
            'debug_routes_count': len(debug_routes),
            'debug_routes': debug_routes[:10],  # 只显示前10个
            'error': '没有找到调试路由' if len(debug_routes) == 0 else None
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def main():
    """主函数"""
    # 设置测试环境
    setup_test_environment(debug=True, load_env=True)

    # 创建测试运行器
    runner = TestRunner("简单调试页面测试")
    runner.start()

    # 运行配置测试
    runner.run_test("配置加载测试", ConfigTestUtils.test_config_loading, True)

    # 运行基础测试
    runner.run_test("调试路由加载测试", test_debug_routes_loading)
    runner.run_test("基础端点测试", test_basic_endpoints)

    # 完成测试
    all_passed = runner.finish()

    if all_passed:
        print("\n💡 简单测试总结:")
        print("1. ✅ 配置系统正常工作")
        print("2. ✅ 调试路由正确加载")
        print("3. ✅ 基础端点正常响应")
        print("\n🚀 可以启动服务器进行手动测试:")
        print("   python run_server.py")
        print("   访问: http://localhost:8001/debug/")

    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
