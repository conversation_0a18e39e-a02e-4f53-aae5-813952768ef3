#!/usr/bin/env python3
"""
AI Gen Hub 调试页面测试应用

这是一个简化的测试应用，用于验证调试页面的功能
"""

import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

# 模拟配置类
class MockSettings:
    def __init__(self):
        self.app_name = "AI Gen Hub"
        self.app_version = "0.1.0"
        self.environment = "development"
        self.debug = True
        self.api_host = "0.0.0.0"
        self.api_port = 8000
        self.api_workers = 1
        
        # 模拟数据库配置
        self.database = MockDatabaseConfig()
        
        # 模拟Redis配置
        self.redis = MockRedisConfig()
        
        # 模拟其他配置
        self.cache = MockCacheConfig()
        self.monitoring = MockMonitoringConfig()
        self.security = MockSecurityConfig()
        self.storage = MockStorageConfig()
        self.performance = MockPerformanceConfig()
        self.features = MockFeatureFlags()
        
        # 模拟供应商配置
        self.openai = MockProviderConfig()
        self.google_ai = MockProviderConfig()
        self.anthropic = MockProviderConfig()
        self.azure = MockProviderConfig()

class MockDatabaseConfig:
    def __init__(self):
        self.host = "localhost"
        self.port = 5432
        self.database = "ai_gen_hub"
        self.user = "postgres"
        self.password = "***word123"
        self.pool_size = 10
        self.max_overflow = 20
        self.pool_timeout = 30
        self.pool_recycle = 3600

class MockRedisConfig:
    def __init__(self):
        self.host = "localhost"
        self.port = 6379
        self.db = 0
        self.password = "***redis123"
        self.max_connections = 100

class MockCacheConfig:
    def __init__(self):
        self.enabled = True
        self.ttl = 3600
        self.max_size = 1000

class MockMonitoringConfig:
    def __init__(self):
        self.log_level = "INFO"
        self.log_format = "json"
        self.prometheus_enabled = True

class MockSecurityConfig:
    def __init__(self):
        self.secret_key = "***secret123"
        self.api_key_header = "X-API-Key"

class MockStorageConfig:
    def __init__(self):
        self.type = "local"
        self.path = "/tmp/ai_gen_hub"

class MockPerformanceConfig:
    def __init__(self):
        self.max_requests_per_minute = 100
        self.timeout = 30

class MockFeatureFlags:
    def __init__(self):
        self.enable_text_generation = True
        self.enable_image_generation = True
        self.enable_caching = True
        self.enable_monitoring = True

class MockProviderConfig:
    def __init__(self):
        self.enabled = True
        self.api_key = "***key123"
        self.base_url = "https://api.example.com"
        self.timeout = 30

# 模拟健康管理器
class MockHealthManager:
    async def check_health(self):
        return MockHealthReport()

class MockHealthReport:
    def __init__(self):
        self.overall_status = "healthy"
        self.checks = [
            MockHealthCheck("database", "healthy", "数据库连接正常"),
            MockHealthCheck("redis", "healthy", "Redis连接正常"),
            MockHealthCheck("system", "healthy", "系统资源正常")
        ]
        self.duration = 0.1
    
    def to_dict(self):
        return {
            "overall_status": self.overall_status,
            "checks": [check.to_dict() for check in self.checks],
            "duration": self.duration
        }

class MockHealthCheck:
    def __init__(self, name, status, message):
        self.name = name
        self.status = status
        self.message = message
        self.duration = 0.05
    
    def to_dict(self):
        return {
            "name": self.name,
            "status": self.status,
            "message": self.message,
            "duration": self.duration
        }

# 创建FastAPI应用
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化
    app.state.settings = MockSettings()
    app.state.health_manager = MockHealthManager()
    app.state.start_time = time.time()
    
    yield
    
    # 关闭时清理
    pass

app = FastAPI(
    title="AI Gen Hub Debug Test",
    description="调试页面测试应用",
    version="0.1.0",
    lifespan=lifespan
)

# 设置模板
templates = Jinja2Templates(directory="src/ai_gen_hub/templates")

# 导入调试路由
from src.ai_gen_hub.api.routers.debug import router as debug_router

# 添加调试路由
app.include_router(
    debug_router,
    prefix="/debug",
    tags=["调试工具"]
)

# 添加一些测试API端点
@app.get("/")
async def root():
    """根路径"""
    return {"message": "AI Gen Hub Debug Test", "status": "running"}

@app.get("/api/test")
async def test_endpoint():
    """测试端点"""
    return {"message": "这是一个测试端点", "timestamp": time.time()}

@app.post("/api/echo")
async def echo_endpoint(data: dict):
    """回显端点"""
    return {"echo": data, "timestamp": time.time()}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": time.time()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, debug=True)
