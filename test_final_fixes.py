#!/usr/bin/env python3
"""
测试最终修复效果
"""

import requests
import time
import json

def test_debug_interface():
    """测试调试界面的所有功能"""
    print("🚀 测试调试界面最终修复效果")
    print("=" * 60)
    
    base_url = "http://localhost:8001"
    
    # 测试用例
    test_cases = [
        {
            "name": "调试主页",
            "method": "GET",
            "url": "/debug/",
            "expected_status": 200
        },
        {
            "name": "系统信息API",
            "method": "GET", 
            "url": "/debug/api/system/info",
            "expected_status": 200
        },
        {
            "name": "配置信息API",
            "method": "GET",
            "url": "/debug/api/config", 
            "expected_status": 200
        },
        {
            "name": "端点列表API",
            "method": "GET",
            "url": "/debug/api/endpoints",
            "expected_status": 200
        },
        {
            "name": "API测试端点（修复后）",
            "method": "POST",
            "url": "/debug/api/test-endpoint",
            "data": {
                "url": "/health",
                "method": "GET",
                "headers": {},
                "body": "",
                "params": {}
            },
            "expected_status": 200
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n📋 测试: {test_case['name']}")
        print("-" * 40)
        
        try:
            start_time = time.time()
            
            if test_case['method'] == 'GET':
                response = requests.get(
                    f"{base_url}{test_case['url']}", 
                    timeout=10
                )
            elif test_case['method'] == 'POST':
                response = requests.post(
                    f"{base_url}{test_case['url']}", 
                    json=test_case.get('data', {}),
                    timeout=10
                )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应时间: {duration:.3f}秒")
            
            success = response.status_code == test_case['expected_status']
            
            if success:
                print("   结果: ✅ 成功")
                
                # 对于API测试端点，检查响应内容
                if test_case['name'] == "API测试端点（修复后）":
                    try:
                        data = response.json()
                        api_success = data.get('success', False)
                        print(f"   API测试结果: {'✅ 成功' if api_success else '❌ 失败'}")
                        if not api_success:
                            print(f"   错误信息: {data.get('error', 'Unknown')}")
                        else:
                            target_status = data.get('response', {}).get('status_code')
                            print(f"   目标API状态码: {target_status}")
                    except:
                        print("   ⚠️ 响应解析失败")
                
                # 对于系统信息，显示关键数据
                elif test_case['name'] == "系统信息API":
                    try:
                        data = response.json()
                        print(f"   CPU使用率: {data.get('cpu_percent', 0):.1f}%")
                        print(f"   内存使用率: {data.get('memory_percent', 0):.1f}%")
                    except:
                        print("   ⚠️ 系统信息解析失败")
                        
            else:
                print("   结果: ❌ 失败")
                print(f"   期望状态码: {test_case['expected_status']}")
                if response.status_code >= 400:
                    print(f"   错误内容: {response.text[:200]}...")
            
            results.append({
                'name': test_case['name'],
                'success': success,
                'status_code': response.status_code,
                'duration': duration
            })
            
        except requests.exceptions.Timeout:
            print("   结果: ❌ 超时")
            results.append({
                'name': test_case['name'],
                'success': False,
                'error': 'timeout',
                'duration': 10.0
            })
            
        except requests.exceptions.ConnectionError:
            print("   结果: ❌ 连接失败")
            results.append({
                'name': test_case['name'],
                'success': False,
                'error': 'connection_error',
                'duration': 0
            })
            
        except Exception as e:
            print(f"   结果: ❌ 异常 - {e}")
            results.append({
                'name': test_case['name'],
                'success': False,
                'error': str(e),
                'duration': 0
            })
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    
    print(f"总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    print("\n详细结果:")
    for result in results:
        status = "✅" if result['success'] else "❌"
        duration = result.get('duration', 0)
        print(f"  {status} {result['name']} - {duration:.3f}秒")
        if not result['success'] and 'error' in result:
            print(f"     错误: {result['error']}")
    
    # 性能分析
    avg_duration = sum(r.get('duration', 0) for r in results if r['success']) / max(success_count, 1)
    print(f"\n平均响应时间: {avg_duration:.3f}秒")
    
    # 问题分析
    if success_count < total_count:
        print("\n⚠️ 发现的问题:")
        for result in results:
            if not result['success']:
                print(f"  - {result['name']}: {result.get('error', '状态码错误')}")
    
    # 修复验证
    print("\n🎯 修复验证:")
    
    # 检查端口配置问题
    if any(r['name'] == '调试主页' and r['success'] for r in results):
        print("  ✅ 端口配置问题已解决 - 调试界面可正常访问")
    else:
        print("  ❌ 端口配置问题仍存在")
    
    # 检查404错误
    api_routes = [r for r in results if 'API' in r['name'] and r['name'] != 'API测试端点（修复后）']
    if all(r['success'] for r in api_routes):
        print("  ✅ 404错误已解决 - 所有调试API路由正常")
    else:
        print("  ❌ 部分调试API路由仍有404错误")
    
    # 检查test-endpoint挂起问题
    test_endpoint_result = next((r for r in results if r['name'] == 'API测试端点（修复后）'), None)
    if test_endpoint_result and test_endpoint_result['success'] and test_endpoint_result['duration'] < 5:
        print("  ✅ test-endpoint挂起问题已解决 - 响应正常且快速")
    elif test_endpoint_result and test_endpoint_result.get('error') == 'timeout':
        print("  ❌ test-endpoint挂起问题仍存在")
    else:
        print("  ⚠️ test-endpoint状态不明确")
    
    if success_count == total_count:
        print("\n🎉 所有问题已成功修复！调试界面完全正常！")
        return True
    else:
        print(f"\n⚠️ 还有 {total_count - success_count} 个问题需要解决")
        return False

def main():
    """主函数"""
    success = test_debug_interface()
    
    print("\n💡 使用建议:")
    print("1. 访问调试界面: http://localhost:8001/debug/")
    print("2. 如有问题，检查AI Gen Hub服务是否在端口8001正常运行")
    print("3. 参考 DEBUG_INTERFACE_GUIDE.md 获取详细说明")
    
    return success

if __name__ == "__main__":
    main()
