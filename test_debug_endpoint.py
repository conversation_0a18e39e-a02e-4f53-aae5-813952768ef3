#!/usr/bin/env python3
"""
测试单个调试端点的详细错误信息

使用增强的配置管理器和通用测试工具进行调试端点测试。
"""

import sys
from pathlib import Path

# 使用通用测试工具设置环境
sys.path.insert(0, str(Path(__file__).parent / "src"))
from ai_gen_hub.utils.test_utils import (
    setup_test_environment, TestRunner, AppTestUtils, ConfigTestUtils
)

def test_debug_system_info_endpoint():
    """测试调试系统信息端点

    Returns:
        测试结果字典
    """
    try:
        app = AppTestUtils.create_test_app()
        result = AppTestUtils.test_endpoint(app, "/debug/api/system/info", "GET")

        if result['success'] and result['status_code'] == 200:
            response_data = result['response_data']

            # 提取系统信息
            system_info = {
                'system': response_data.get('system', {}),
                'app': response_data.get('app', {}),
                'providers': response_data.get('providers', {}),
                'health': response_data.get('health', {})
            }

            result['system_info'] = system_info

            # 验证关键信息
            if not system_info['system']:
                result['success'] = False
                result['error'] = "缺少系统信息"

        return result

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def test_debug_config_endpoint():
    """测试调试配置端点

    Returns:
        测试结果字典
    """
    try:
        app = AppTestUtils.create_test_app()
        result = AppTestUtils.test_endpoint(app, "/debug/api/config", "GET")

        if result['success'] and result['status_code'] == 200:
            response_data = result['response_data']

            # 提取配置信息
            config_info = {
                'app_settings': response_data.get('app_settings', {}),
                'providers': response_data.get('providers', {}),
                'security': response_data.get('security', {})
            }

            result['config_info'] = config_info

            # 验证关键配置
            if not config_info['app_settings']:
                result['success'] = False
                result['error'] = "缺少应用配置信息"

        return result

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def main():
    """主函数"""
    # 设置测试环境
    setup_test_environment(debug=True, load_env=True)

    # 创建测试运行器
    runner = TestRunner("调试端点详细测试")
    runner.start()

    # 运行配置测试
    runner.run_test("配置加载测试", ConfigTestUtils.test_config_loading, True)

    # 运行调试端点测试
    runner.run_test("调试系统信息端点测试", test_debug_system_info_endpoint)
    runner.run_test("调试配置端点测试", test_debug_config_endpoint)

    # 完成测试
    all_passed = runner.finish()

    if all_passed:
        print("\n💡 调试端点测试总结:")
        print("1. ✅ 调试系统信息端点正常工作")
        print("2. ✅ 调试配置端点正常工作")
        print("3. ✅ 所有调试功能通过主应用端口访问")
        print("\n🚀 访问调试界面:")
        print("   主应用启动后访问: http://localhost:8001/debug/")

    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
