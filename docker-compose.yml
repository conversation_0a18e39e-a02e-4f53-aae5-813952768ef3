# AI Gen Hub Docker Compose配置
# 提供完整的开发和测试环境

version: '3.8'

services:
  # =============================================================================
  # AI Gen Hub 主服务
  # =============================================================================
  ai-gen-hub:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: ai-gen-hub
    ports:
      - "8001:8001"  # API服务端口
      - "9090:9090"  # Prometheus监控端口
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/ai_gen_hub
      - REDIS_URL=redis://redis:6379/0
      - LOG_LEVEL=DEBUG
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
      - ./storage:/app/storage
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - ai-gen-hub-network
    restart: unless-stopped

  # =============================================================================
  # PostgreSQL 数据库
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: ai-gen-hub-postgres
    environment:
      POSTGRES_DB: ai_gen_hub
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - ai-gen-hub-network
    restart: unless-stopped

  # =============================================================================
  # Redis 缓存
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: ai-gen-hub-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-gen-hub-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # =============================================================================
  # MinIO 对象存储
  # =============================================================================
  minio:
    image: minio/minio:latest
    container_name: ai-gen-hub-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    networks:
      - ai-gen-hub-network
    restart: unless-stopped
    command: server /data --console-address ":9001"

  # =============================================================================
  # Prometheus 监控
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-gen-hub-prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - ai-gen-hub-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # =============================================================================
  # Grafana 仪表板
  # =============================================================================
  grafana:
    image: grafana/grafana:latest
    container_name: ai-gen-hub-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-gen-hub-network
    restart: unless-stopped

  # =============================================================================
  # Nginx 反向代理（可选）
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: ai-gen-hub-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - ai-gen-hub
    networks:
      - ai-gen-hub-network
    restart: unless-stopped
    profiles:
      - production

# =============================================================================
# 网络配置
# =============================================================================
networks:
  ai-gen-hub-network:
    driver: bridge

# =============================================================================
# 数据卷配置
# =============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
