#!/usr/bin/env python3
"""
阿里云百炼 DashScope Provider 使用示例

本示例展示如何使用 DashScope Provider 进行文本生成，包括：
- 基础文本生成
- 流式文本生成
- 多轮对话
- 代码生成
- 不同模型的使用

使用前请确保：
1. 已安装 AI Gen Hub
2. 设置了 DASHSCOPE_API_KEY 环境变量
3. 或在配置文件中配置了 DashScope API 密钥

运行方式：
python examples/dashscope_example.py
"""

import asyncio
import os
import sys
from typing import List

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from ai_gen_hub.config.settings import Settings, ProviderConfig
from ai_gen_hub.core.interfaces import (
    TextGenerationRequest,
    Message,
    MessageRole
)
from ai_gen_hub.providers.dashscope_provider import DashScopeProvider
from ai_gen_hub.utils.key_manager import Key<PERSON>ana<PERSON>, APIKey
from unittest.mock import MagicMock, AsyncMock


class DashScopeExample:
    """DashScope Provider 使用示例类"""
    
    def __init__(self):
        """初始化示例"""
        self.api_key = self._get_api_key()
        self.provider = self._create_provider()
    
    def _get_api_key(self) -> str:
        """获取 API 密钥"""
        api_key = os.getenv('DASHSCOPE_API_KEY')
        if not api_key:
            print("⚠️  警告: 未设置 DASHSCOPE_API_KEY 环境变量")
            print("   本示例将使用模拟模式运行")
            return "sk-mock-dashscope-key-for-demo"
        return api_key
    
    def _create_provider(self) -> DashScopeProvider:
        """创建 DashScope Provider"""
        # 创建配置
        config = ProviderConfig(
            api_keys=[self.api_key],
            timeout=30,
            max_retries=3,
            enabled=True
        )
        
        # 创建密钥管理器
        key_manager = MagicMock(spec=KeyManager)
        api_key_obj = APIKey(
            key=self.api_key,
            provider="dashscope",
            rate_limit=1000
        )
        key_manager.get_key = AsyncMock(return_value=api_key_obj)
        key_manager.record_request = AsyncMock()
        
        # 创建 Provider
        provider = DashScopeProvider(config, key_manager)
        return provider
    
    async def basic_text_generation(self):
        """基础文本生成示例"""
        print("\n🔤 基础文本生成示例")
        print("=" * 50)
        
        request = TextGenerationRequest(
            model="qwen-plus",
            messages=[
                Message(
                    role=MessageRole.SYSTEM,
                    content="你是一个有用的AI助手，请用中文回答问题。"
                ),
                Message(
                    role=MessageRole.USER,
                    content="请介绍一下人工智能的发展历程。"
                )
            ],
            max_tokens=500,
            temperature=0.7
        )
        
        print(f"🤖 模型: {request.model}")
        print(f"💬 用户问题: {request.messages[-1].content}")
        print(f"⚙️  参数: max_tokens={request.max_tokens}, temperature={request.temperature}")
        
        if self.api_key.startswith("sk-mock"):
            print("\n📝 模拟响应:")
            print("人工智能的发展历程可以分为几个重要阶段...")
            print("（这是模拟响应，请设置真实的 API 密钥以获得实际结果）")
        else:
            try:
                response = await self.provider._generate_text_impl(request, self.api_key)
                print(f"\n📝 AI 回答:")
                print(response.choices[0].message.content)
                print(f"\n📊 使用统计: {response.usage}")
            except Exception as e:
                print(f"❌ 生成失败: {e}")
    
    async def streaming_text_generation(self):
        """流式文本生成示例"""
        print("\n🌊 流式文本生成示例")
        print("=" * 50)
        
        request = TextGenerationRequest(
            model="qwen-turbo",
            messages=[
                Message(
                    role=MessageRole.USER,
                    content="请写一个关于春天的短诗。"
                )
            ],
            max_tokens=200,
            temperature=0.8,
            stream=True
        )
        
        print(f"🤖 模型: {request.model}")
        print(f"💬 用户问题: {request.messages[-1].content}")
        print(f"🌊 流式输出: 是")
        
        if self.api_key.startswith("sk-mock"):
            print("\n📝 模拟流式响应:")
            mock_chunks = [
                "春风", "轻抚", "大地", "，", "万物", "复苏", "绿意", "浓", "。\n",
                "花开", "满园", "香", "，", "鸟语", "伴", "晨曦", "。\n",
                "生机", "盎然", "春光", "好", "，", "诗意", "满", "人间", "。"
            ]
            
            for chunk in mock_chunks:
                print(chunk, end="", flush=True)
                await asyncio.sleep(0.1)
            print("\n\n（这是模拟响应，请设置真实的 API 密钥以获得实际结果）")
        else:
            try:
                print("\n📝 AI 创作:")
                async for chunk in await self.provider._generate_text_impl(request, self.api_key):
                    if chunk.choices and chunk.choices[0].get('delta', {}).get('content'):
                        content = chunk.choices[0]['delta']['content']
                        print(content, end="", flush=True)
                print("\n")
            except Exception as e:
                print(f"❌ 生成失败: {e}")
    
    async def multi_turn_conversation(self):
        """多轮对话示例"""
        print("\n💬 多轮对话示例")
        print("=" * 50)
        
        # 初始化对话历史
        conversation: List[Message] = [
            Message(
                role=MessageRole.SYSTEM,
                content="你是一个友好的AI助手，擅长回答各种问题。"
            )
        ]
        
        # 模拟多轮对话
        user_inputs = [
            "你好！",
            "你能帮我解释一下什么是机器学习吗？",
            "那深度学习和机器学习有什么区别？"
        ]
        
        for i, user_input in enumerate(user_inputs, 1):
            print(f"\n🔄 第 {i} 轮对话:")
            print(f"👤 用户: {user_input}")
            
            # 添加用户消息
            conversation.append(Message(
                role=MessageRole.USER,
                content=user_input
            ))
            
            # 创建请求
            request = TextGenerationRequest(
                model="qwen-max",
                messages=conversation,
                max_tokens=300,
                temperature=0.7
            )
            
            if self.api_key.startswith("sk-mock"):
                # 模拟响应
                mock_responses = [
                    "你好！我是通义千问，很高兴为您服务！",
                    "机器学习是人工智能的一个分支，它让计算机能够从数据中学习...",
                    "深度学习是机器学习的一个子集，它使用神经网络..."
                ]
                ai_response = mock_responses[i-1] if i <= len(mock_responses) else "这是一个模拟回答。"
                print(f"🤖 AI: {ai_response}")
                
                # 添加AI响应到对话历史
                conversation.append(Message(
                    role=MessageRole.ASSISTANT,
                    content=ai_response
                ))
            else:
                try:
                    response = await self.provider._generate_text_impl(request, self.api_key)
                    ai_response = response.choices[0].message.content
                    print(f"🤖 AI: {ai_response}")
                    
                    # 添加AI响应到对话历史
                    conversation.append(response.choices[0].message)
                except Exception as e:
                    print(f"❌ 生成失败: {e}")
                    break
        
        if self.api_key.startswith("sk-mock"):
            print("\n（这是模拟对话，请设置真实的 API 密钥以获得实际结果）")
    
    async def code_generation_example(self):
        """代码生成示例"""
        print("\n💻 代码生成示例")
        print("=" * 50)
        
        request = TextGenerationRequest(
            model="qwen2.5-coder-32b-instruct",  # 使用代码专用模型
            messages=[
                Message(
                    role=MessageRole.SYSTEM,
                    content="你是一个专业的编程助手，请提供高质量的代码实现。"
                ),
                Message(
                    role=MessageRole.USER,
                    content="请用 Python 实现一个快速排序算法，并添加详细注释。"
                )
            ],
            max_tokens=800,
            temperature=0.2  # 代码生成使用较低的温度
        )
        
        print(f"🤖 模型: {request.model} (代码专用)")
        print(f"💬 用户需求: {request.messages[-1].content}")
        print(f"⚙️  参数: temperature={request.temperature} (低温度确保代码准确性)")
        
        if self.api_key.startswith("sk-mock"):
            print("\n📝 模拟代码生成:")
            mock_code = '''def quick_sort(arr):
    """
    快速排序算法实现
    
    Args:
        arr: 待排序的列表
    
    Returns:
        排序后的列表
    """
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quick_sort(left) + middle + quick_sort(right)

# 使用示例
if __name__ == "__main__":
    test_array = [3, 6, 8, 10, 1, 2, 1]
    sorted_array = quick_sort(test_array)
    print(f"原数组: {test_array}")
    print(f"排序后: {sorted_array}")'''
            print(mock_code)
            print("\n（这是模拟代码，请设置真实的 API 密钥以获得实际结果）")
        else:
            try:
                response = await self.provider._generate_text_impl(request, self.api_key)
                print(f"\n📝 生成的代码:")
                print(response.choices[0].message.content)
            except Exception as e:
                print(f"❌ 生成失败: {e}")
    
    def show_model_info(self):
        """显示模型信息"""
        print("\n📋 DashScope 支持的模型")
        print("=" * 50)
        
        models_by_category = {
            "通义千问最新系列（推荐）": [
                "qwen-max - 最强性能模型",
                "qwen-plus - 平衡性能和成本",
                "qwen-turbo - 快速响应模型"
            ],
            "Qwen3 系列（最新一代）": [
                "qwen3-235b-a22b - 超大规模模型",
                "qwen3-32b - 大规模模型",
                "qwen3-8b - 中等规模模型"
            ],
            "Qwen2.5-Coder 系列（代码专用）": [
                "qwen2.5-coder-32b-instruct - 大规模代码模型",
                "qwen2.5-coder-14b-instruct - 中等规模代码模型",
                "qwen2.5-coder-7b-instruct - 小规模代码模型"
            ],
            "便捷别名": [
                "qwen-latest → qwen-max",
                "qwen-fast → qwen-turbo",
                "qwen-balanced → qwen-plus",
                "qwen-coder → qwen2.5-coder-32b-instruct"
            ]
        }
        
        for category, models in models_by_category.items():
            print(f"\n📂 {category}:")
            for model in models:
                print(f"   • {model}")
        
        print(f"\n📊 总计支持 {len(self.provider._supported_models)} 个模型")
    
    async def run_all_examples(self):
        """运行所有示例"""
        print("🚀 DashScope Provider 使用示例")
        print("=" * 60)
        
        # 显示模型信息
        self.show_model_info()
        
        # 运行各种示例
        await self.basic_text_generation()
        await self.streaming_text_generation()
        await self.multi_turn_conversation()
        await self.code_generation_example()
        
        print("\n✅ 所有示例运行完成！")
        print("\n💡 提示:")
        print("   • 设置 DASHSCOPE_API_KEY 环境变量以获得真实结果")
        print("   • 访问 https://dashscope.console.aliyun.com/ 获取 API 密钥")
        print("   • 查看 docs/providers/dashscope_provider.md 了解更多详情")


async def main():
    """主函数"""
    example = DashScopeExample()
    await example.run_all_examples()


if __name__ == "__main__":
    asyncio.run(main())
