# AI Gen Hub 开发环境配置建议

## 最小可运行配置

创建 `.env` 文件，仅包含必要配置：

```bash
# 基础配置
ENVIRONMENT=development
DEBUG=true
APP_NAME=AI Gen Hub
API_HOST=0.0.0.0
API_PORT=8000

# 安全配置（开发环境）
JWT_SECRET_KEY=dev-secret-key-change-in-production
API_KEY=dev-api-key

# AI供应商配置（至少配置一个）
OPENAI_API_KEYS=sk-your-openai-key
# 或者
# GOOGLE_AI_API_KEYS=your-google-ai-key
# 或者
# ANTHROPIC_API_KEYS=sk-ant-your-anthropic-key

# 特性开关
ENABLE_TEXT_GENERATION=true
ENABLE_IMAGE_GENERATION=true
ENABLE_CACHING=true
```

## 各服务配置分析

### 1. PostgreSQL 数据库 ❌ 非必需

**开发环境建议：**
- **跳过配置**：可以完全不配置数据库
- **Docker快速启动**：如需测试持久化功能
  ```bash
  docker run -d --name postgres \
    -e POSTGRES_DB=ai_gen_hub \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=password \
    -p 5432:5432 postgres:15
  ```
- **配置示例**：
  ```bash
  DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/ai_gen_hub
  ```

**影响分析：**
- ✅ 所有AI生成功能正常
- ❌ 无法持久化用户数据、请求历史
- ❌ 无法使用数据库相关的监控和统计功能

### 2. Redis 缓存 ⚠️ 建议配置

**开发环境建议：**
- **推荐配置**：提升开发体验
  ```bash
  # Docker快速启动
  docker run -d --name redis -p 6379:6379 redis:7-alpine
  
  # 环境变量
  REDIS_URL=redis://localhost:6379/0
  ```
- **不配置的后果**：功能正常，但性能下降

**影响分析：**
- ✅ 应用正常启动（自动降级到内存缓存）
- ✅ 所有功能正常工作
- ❌ 缓存不持久，重启后丢失
- ❌ 多实例开发时缓存不共享

### 3. S3 对象存储 ❌ 非必需

**开发环境建议：**
- **跳过配置**：图像生成使用外部URL
- **如需本地化存储**：
  ```bash
  LOCAL_STORAGE_PATH=./storage
  ```

**影响分析：**
- ✅ 图像生成功能完全正常
- ✅ 返回AI供应商提供的图像URL
- ❌ 无法实现图像本地化存储
- ❌ 无法提供图像代理服务

### 4. MinIO 存储 ❌ 非必需

**开发环境建议：**
- **跳过配置**：除非需要测试对象存储功能
- **Docker快速启动**：如需测试
  ```bash
  docker run -d --name minio \
    -p 9000:9000 -p 9001:9001 \
    -e MINIO_ROOT_USER=minioadmin \
    -e MINIO_ROOT_PASSWORD=minioadmin \
    minio/minio server /data --console-address ":9001"
  ```

## 推荐的开发环境配置

### 方案一：最小配置（快速开始）

```bash
# .env
ENVIRONMENT=development
DEBUG=true
JWT_SECRET_KEY=dev-secret-key
OPENAI_API_KEYS=sk-your-openai-key
```

**适用场景：**
- 快速功能开发和测试
- 专注于AI功能实现
- 不需要持久化和缓存

### 方案二：标准开发配置（推荐）

```bash
# .env
ENVIRONMENT=development
DEBUG=true
JWT_SECRET_KEY=dev-secret-key

# AI供应商
OPENAI_API_KEYS=sk-your-openai-key
GOOGLE_AI_API_KEYS=your-google-ai-key

# Redis缓存
REDIS_URL=redis://localhost:6379/0

# 启动Redis
docker run -d --name redis -p 6379:6379 redis:7-alpine
```

**适用场景：**
- 完整功能开发
- 性能测试
- 缓存功能验证

### 方案三：完整开发环境

```bash
# 使用 docker-compose
docker-compose up -d postgres redis minio

# .env
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/ai_gen_hub
REDIS_URL=redis://localhost:6379/0
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
```

**适用场景：**
- 完整系统测试
- 集成测试
- 生产环境模拟

## 常见错误和解决方案

### 1. 数据库连接错误
```
错误：数据库连接失败
解决：检查DATABASE_URL或直接移除数据库配置
```

### 2. Redis连接错误
```
错误：Redis连接失败，降级到内存缓存
解决：启动Redis服务或禁用Redis缓存
```

### 3. AI供应商配置错误
```
错误：没有可用的AI供应商
解决：至少配置一个有效的API密钥
```

## 开发环境启动检查清单

- [ ] 环境变量配置正确
- [ ] 至少配置一个AI供应商API密钥
- [ ] Redis服务运行（可选但推荐）
- [ ] 存储目录权限正确
- [ ] 端口8000未被占用

## 性能优化建议

1. **启用Redis缓存**：显著提升响应速度
2. **配置多个AI供应商**：提供负载均衡和容错
3. **调整缓存TTL**：根据开发需求优化缓存时间
4. **启用调试日志**：便于问题排查

## 总结

在开发环境下，AI Gen Hub具有很好的容错性和降级能力：

- **数据库**：完全可选，不影响核心功能
- **Redis**：可选但推荐，提升性能和开发体验  
- **S3/MinIO**：可选，图像功能使用外部URL
- **AI供应商**：必需至少一个，是核心功能依赖

推荐使用**方案二（标准开发配置）**，既保证了功能完整性，又避免了不必要的复杂性。
