#!/usr/bin/env python3
"""
测试修复后的调试界面接口

验证以下修复：
1. system/info 接口响应时间优化
2. test-endpoint 接口挂起问题修复
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

async def test_system_info_performance():
    """测试 system/info 接口性能"""
    print("🔍 测试 system/info 接口性能...")
    
    try:
        # 设置环境变量
        os.environ.setdefault('ENVIRONMENT', 'development')
        os.environ.setdefault('DEBUG', 'true')
        
        from fastapi.testclient import TestClient
        from ai_gen_hub.api.app import create_app
        
        # 创建应用
        app = create_app()
        
        with TestClient(app) as client:
            # 进行多次测试以获得平均响应时间
            response_times = []
            
            for i in range(3):
                print(f"   第 {i+1} 次测试...")
                start_time = time.time()
                
                response = client.get("/debug/api/system/info")
                
                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)
                
                print(f"   响应时间: {response_time:.2f}秒, 状态码: {response.status_code}")
                
                if response.status_code != 200:
                    print(f"   错误: {response.text}")
                    return False
            
            avg_time = sum(response_times) / len(response_times)
            print(f"✅ 平均响应时间: {avg_time:.2f}秒")
            
            # 检查是否有显著改善（应该小于1.5秒）
            if avg_time < 1.5:
                print("✅ 响应时间优化成功")
                return True
            else:
                print("⚠️  响应时间仍然较慢")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_test_endpoint_fix():
    """测试 test-endpoint 接口修复"""
    print("\n🔍 测试 test-endpoint 接口修复...")
    
    try:
        from fastapi.testclient import TestClient
        from ai_gen_hub.api.app import create_app
        
        # 创建应用
        app = create_app()
        
        # 测试数据
        test_cases = [
            {
                "name": "测试健康检查端点",
                "data": {
                    "url": "/health",
                    "method": "GET",
                    "headers": {},
                    "body": "",
                    "params": {}
                }
            },
            {
                "name": "测试调试端点",
                "data": {
                    "url": "/debug/api/system/info",
                    "method": "GET",
                    "headers": {},
                    "body": "",
                    "params": {}
                }
            },
            {
                "name": "测试无效URL",
                "data": {
                    "url": "",
                    "method": "GET",
                    "headers": {},
                    "body": "",
                    "params": {}
                }
            }
        ]
        
        with TestClient(app) as client:
            for test_case in test_cases:
                print(f"   {test_case['name']}...")
                start_time = time.time()
                
                try:
                    response = client.post(
                        "/debug/api/test-endpoint",
                        json=test_case['data'],
                        timeout=20  # 20秒超时
                    )
                    
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    print(f"   响应时间: {response_time:.2f}秒, 状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('success') is not None:
                            success = data.get('success')
                            print(f"   测试结果: {'成功' if success else '失败'}")
                            if not success:
                                print(f"   错误信息: {data.get('error', 'Unknown')}")
                        else:
                            print("   ⚠️  响应格式异常")
                    else:
                        print(f"   ❌ HTTP错误: {response.text}")
                        
                except Exception as e:
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    if "timeout" in str(e).lower() or response_time >= 19:
                        print(f"   ❌ 请求超时 (耗时: {response_time:.2f}秒)")
                        return False
                    else:
                        print(f"   ❌ 请求异常: {e}")
                        return False
            
            print("✅ test-endpoint 接口修复成功")
            return True
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_direct_function_calls():
    """直接测试函数调用"""
    print("\n🔍 测试直接函数调用...")
    
    try:
        from ai_gen_hub.api.routers.debug import get_system_info
        
        # 测试 get_system_info 函数
        print("   测试 get_system_info 函数...")
        start_time = time.time()
        
        system_info = get_system_info()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"   执行时间: {execution_time:.2f}秒")
        print(f"   CPU使用率: {system_info.cpu_percent}%")
        print(f"   内存使用率: {system_info.memory_percent}%")
        
        if execution_time < 0.5:  # 应该在0.5秒内完成
            print("✅ get_system_info 函数优化成功")
            return True
        else:
            print("⚠️  get_system_info 函数仍然较慢")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试修复效果...")
    print("=" * 60)
    
    results = {}
    
    # 测试1: 直接函数调用
    results['direct_functions'] = await test_direct_function_calls()
    
    # 测试2: system/info 接口性能
    results['system_info_performance'] = await test_system_info_performance()
    
    # 测试3: test-endpoint 接口修复
    results['test_endpoint_fix'] = await test_test_endpoint_fix()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    all_passed = all(results.values())
    
    print("\n🎯 总体结果:")
    if all_passed:
        print("🎉 所有测试通过！修复成功！")
        print("\n💡 修复总结:")
        print("1. ✅ system/info 接口响应时间优化 - 移除阻塞的 CPU 采样")
        print("2. ✅ test-endpoint 接口挂起修复 - 添加超时保护和错误处理")
        print("3. ✅ 增强日志记录和错误处理")
    else:
        print("💥 部分测试失败，需要进一步调试")
        
        failed_tests = [name for name, success in results.items() if not success]
        print(f"失败的测试: {', '.join(failed_tests)}")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
