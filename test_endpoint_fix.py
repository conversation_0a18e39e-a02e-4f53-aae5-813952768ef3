
# 修复test-endpoint挂起问题的代码片段
# 需要添加到 src/ai_gen_hub/api/routers/debug.py

@router.post("/api/test-endpoint-fixed")
async def test_api_endpoint_fixed(
    request: Request,
    endpoint_data: dict,
    _: bool = Depends(check_debug_access)
):
    """修复后的API端点测试（使用同步HTTP客户端）"""
    import requests  # 使用同步requests而不是异步httpx
    
    try:
        url = endpoint_data.get('url', '')
        method = endpoint_data.get('method', 'GET').upper()
        headers = endpoint_data.get('headers', {})
        body = endpoint_data.get('body', '')
        params = endpoint_data.get('params', {})

        logger.debug("开始API端点测试", url=url, method=method)

        # 验证输入参数
        if not url:
            return {
                "success": False,
                "error": "URL不能为空",
                "request": endpoint_data,
                "timestamp": time.time()
            }

        # 检测循环调用
        if url.endswith('/debug/api/test-endpoint') or url.endswith('/debug/api/test-endpoint-fixed'):
            return {
                "success": False,
                "error": "不能测试 test-endpoint 接口自身，这会导致循环调用",
                "request": endpoint_data,
                "timestamp": time.time()
            }

        # 如果是相对URL，添加基础URL
        if url.startswith('/'):
            base_url = f"{request.url.scheme}://{request.url.netloc}"
            url = base_url + url
            logger.debug("转换相对URL", original_url=endpoint_data.get('url'), full_url=url)

        # 准备请求数据
        request_data = {
            'method': method,
            'url': url,
            'headers': headers,
            'params': params,
            'timeout': 10  # 使用同步客户端的超时
        }

        # 处理请求体
        if body and method in ['POST', 'PUT', 'PATCH']:
            try:
                request_data['json'] = json.loads(body)
                logger.debug("请求体解析为JSON")
            except json.JSONDecodeError:
                request_data['data'] = body
                if 'content-type' not in [k.lower() for k in headers.keys()]:
                    headers['Content-Type'] = 'text/plain'
                logger.debug("请求体作为文本发送")

        start_time = time.time()
        logger.debug("发送HTTP请求", request_data=request_data)

        # 使用同步requests客户端
        response = requests.request(**request_data)
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000

        logger.debug("HTTP请求完成", status_code=response.status_code, response_time=response_time)

        # 尝试解析响应体
        try:
            response_json = response.json()
        except:
            response_json = None

        # 限制响应体大小
        response_text = response.text
        if len(response_text) > 10000:
            response_text = response_text[:10000] + "... (响应体过长，已截断)"

        return {
            "success": True,
            "request": {
                "method": method,
                "url": url,
                "headers": dict(headers),
                "params": params,
                "body": body
            },
            "response": {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "body": response_text,
                "json": response_json,
                "size": len(response.content)
            },
            "timing": {
                "response_time": response_time,
                "timestamp": time.time()
            }
        }

    except requests.exceptions.Timeout as e:
        logger.warning("HTTP请求超时", error=str(e), url=url)
        return {
            "success": False,
            "error": f"请求超时: {str(e)}",
            "request": endpoint_data,
            "timestamp": time.time()
        }
    except requests.exceptions.RequestException as e:
        logger.warning("HTTP请求错误", error=str(e), url=url)
        return {
            "success": False,
            "error": f"请求错误: {str(e)}",
            "request": endpoint_data,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error("API端点测试失败", error=str(e), endpoint=endpoint_data)
        return {
            "success": False,
            "error": str(e),
            "request": endpoint_data,
            "timestamp": time.time()
        }
