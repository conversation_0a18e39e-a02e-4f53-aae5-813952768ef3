# AI Gen Hub 调试仪表板 - 快速开始

## 🚀 功能概览

AI Gen Hub 调试仪表板是一个功能强大的开发和调试工具，提供以下核心功能：

- 🖥️ **系统状态监控** - 实时监控CPU、内存、磁盘、网络等系统资源
- 🔧 **API接口测试** - 交互式API测试工具，自动发现端点并提供测试界面
- 📋 **日志查看器** - 实时日志流显示，支持过滤、搜索和统计分析
- ⚙️ **配置信息展示** - 安全地显示应用配置，自动脱敏敏感信息
- 📊 **性能指标** - 监控响应时间、请求频率、错误率等关键指标
- 🛠️ **开发工具** - 缓存管理、配置重载、数据库维护等实用工具

## 🔒 安全特性

- ✅ **环境控制** - 仅在开发/测试环境可用，生产环境自动禁用
- ✅ **数据脱敏** - 自动识别并隐藏密码、API密钥等敏感信息
- ✅ **访问控制** - 支持基于角色的访问控制机制

## 📦 安装和配置

### 1. 安装依赖

```bash
# 安装必要的Python包
pip install jinja2 psutil

# 或者添加到requirements.txt
echo "jinja2>=3.1.2" >> requirements.txt
echo "psutil>=5.9.6" >> requirements.txt
```

### 2. 集成到FastAPI应用

在你的FastAPI应用中添加调试路由：

```python
from fastapi import FastAPI
from ai_gen_hub.api.routers.debug import router as debug_router

app = FastAPI()

# 仅在非生产环境添加调试路由
if settings.debug or settings.environment.lower() != "production":
    app.include_router(
        debug_router,
        prefix="/debug",
        tags=["调试工具"]
    )
```

### 3. 配置模板目录

确保模板目录正确配置：

```python
from fastapi.templating import Jinja2Templates

# 在debug.py中已经配置
templates = Jinja2Templates(directory="src/ai_gen_hub/templates")
```

### 4. 环境变量设置

```bash
# 开发环境
export DEBUG=true
export ENVIRONMENT=development

# 生产环境（调试页面将被禁用）
export DEBUG=false
export ENVIRONMENT=production
```

## 🎯 快速使用

### 启动应用

```bash
# 使用uvicorn启动
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 或者使用提供的测试应用
python debug_standalone.py
```

### 访问调试仪表板

打开浏览器访问：

```
http://localhost:8000/debug/
```

### 主要页面导航

| 页面 | URL | 功能描述 |
|------|-----|----------|
| 仪表板 | `/debug/` | 系统概览和快速操作 |
| 系统监控 | `/debug/system` | 详细的系统资源监控 |
| API测试 | `/debug/api-test` | 交互式API测试工具 |
| 日志查看 | `/debug/logs` | 实时日志查看和分析 |
| 配置信息 | `/debug/config` | 应用配置和环境信息 |
| 性能指标 | `/debug/metrics` | 性能监控和分析 |
| 开发工具 | `/debug/tools` | 各种开发和维护工具 |

## 📖 使用示例

### 1. 系统监控

```javascript
// 获取系统信息
fetch('/debug/api/system/info')
  .then(response => response.json())
  .then(data => {
    console.log('CPU使用率:', data.system.cpu_percent);
    console.log('内存使用率:', data.system.memory_percent);
  });
```

### 2. API测试

```javascript
// 测试API端点
const testData = {
  url: '/api/v1/text/generate',
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({prompt: 'Hello, AI!'})
};

fetch('/debug/api/test-endpoint', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify(testData)
})
.then(response => response.json())
.then(result => {
  console.log('测试结果:', result);
});
```

### 3. 日志查询

```javascript
// 获取错误级别的日志
fetch('/debug/api/logs?level=ERROR&limit=50')
  .then(response => response.json())
  .then(data => {
    console.log('错误日志:', data.logs);
  });
```

## 🔧 API参考

### 系统监控API

```
GET /debug/api/system/info          # 基础系统信息
GET /debug/api/system/detailed      # 详细系统信息
GET /debug/api/system/processes     # 进程列表
GET /debug/api/database/status      # 数据库状态
GET /debug/api/cache/status         # 缓存状态
```

### API测试API

```
GET /debug/api/endpoints            # 端点列表
GET /debug/api/endpoints/detailed   # 详细端点信息
POST /debug/api/test-endpoint       # 执行API测试
```

### 日志API

```
GET /debug/api/logs                 # 日志列表
GET /debug/api/logs/levels          # 日志级别
GET /debug/api/logs/stats           # 日志统计
```

### 配置API

```
GET /debug/api/config               # 应用配置
GET /debug/api/config/environment   # 环境变量
GET /debug/api/config/runtime       # 运行时信息
```

## 🎨 界面预览

调试仪表板采用现代化的响应式设计：

- 🎨 **Bootstrap 5** - 美观的UI组件
- 📊 **Chart.js** - 交互式图表
- 🎯 **Font Awesome** - 丰富的图标
- 📱 **响应式设计** - 支持移动设备

## 🛠️ 自定义和扩展

### 添加自定义监控指标

```python
@router.get("/api/custom/metrics")
async def get_custom_metrics():
    return {
        "custom_metric": "value",
        "business_kpi": calculate_kpi()
    }
```

### 集成外部监控系统

```python
# 导出到Prometheus
from prometheus_client import Counter, Histogram

request_count = Counter('requests_total', 'Total requests')
response_time = Histogram('response_time_seconds', 'Response time')
```

### 添加认证中间件

```python
from fastapi import Depends, HTTPException
from fastapi.security import HTTPBearer

security = HTTPBearer()

def verify_token(token: str = Depends(security)):
    if not is_valid_token(token):
        raise HTTPException(status_code=401, detail="Invalid token")
    return token

# 在路由中使用
@router.get("/protected")
async def protected_endpoint(token: str = Depends(verify_token)):
    return {"message": "Access granted"}
```

## 🐛 故障排除

### 常见问题

1. **无法访问调试页面**
   ```bash
   # 检查环境变量
   echo $DEBUG
   echo $ENVIRONMENT
   
   # 确保不是生产环境
   export ENVIRONMENT=development
   export DEBUG=true
   ```

2. **系统信息显示为0**
   ```bash
   # 安装psutil
   pip install psutil
   ```

3. **模板加载失败**
   ```python
   # 检查模板路径
   import os
   print(os.path.exists("src/ai_gen_hub/templates"))
   ```

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查路由注册
for route in app.routes:
    print(f"{route.methods} {route.path}")
```

## 📚 更多资源

- 📖 [完整文档](docs/debug_dashboard.md)
- 🎯 [演示页面](debug_demo.html)
- 🔧 [API文档](http://localhost:8000/docs)
- 💡 [最佳实践指南](docs/best_practices.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进调试仪表板！

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**⚠️ 重要提醒：请确保调试页面仅在开发和测试环境中使用，切勿在生产环境中启用！**
