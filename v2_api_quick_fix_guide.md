# V2 API 快速修复指南

## 🚨 问题总结

您遇到的问题：
- **API路径**：`/api/v1/text/v2/generate` (实际上是正确的)
- **症状**：请求挂起，无响应，缺少调试日志
- **请求ID**：9781c21f-4dd2-47d5-8531-c62f6a23e007

## ⚡ 立即修复步骤

### 1. 确认API路径 ✅
您使用的路径 `/api/v1/text/v2/generate` 是**正确的**！
- 路由器前缀：`/api/v1/text`
- V2端点：`/v2/generate`
- 完整路径：`/api/v1/text/v2/generate`

### 2. 检查服务状态
```bash
# 检查服务是否运行
curl http://localhost:8000/health

# 检查基础功能
curl -X POST "http://localhost:8000/api/v1/text/generate" \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"test"}],"model":"gpt-4"}'
```

### 3. 安装缺失依赖
```bash
# 安装可能缺失的依赖
pip install structlog pydantic fastapi aiohttp

# 或者重新安装所有依赖
pip install -r requirements.txt
```

### 4. 重启服务
```bash
# 停止当前服务
pkill -f "ai_gen_hub"

# 设置调试模式重启
export DEBUG=true
export LOG_LEVEL=DEBUG
python -m ai_gen_hub.api.main
```

## 🔧 已实施的修复

我已经对代码进行了以下修复：

### 1. 增强的错误处理和日志
- 添加了详细的请求处理日志
- 增加了异常捕获和错误信息
- 添加了请求ID追踪

### 2. 超时保护
- 为所有异步操作添加了5分钟超时
- 防止请求无限挂起
- 提供明确的超时错误信息

### 3. 请求格式验证
- 改进了请求格式适配逻辑
- 添加了类型检查和转换日志
- 增强了错误消息的可读性

## 🧪 验证修复

运行测试脚本验证修复效果：

```bash
# 运行完整的验证测试
python test_v2_api_fix.py
```

或者手动测试：

```bash
# 测试V2 API
curl -X POST "http://localhost:8000/api/v1/text/v2/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}],
    "model": "gpt-4",
    "generation": {"temperature": 0.7, "max_tokens": 100},
    "stream": {"enabled": false}
  }'

# 测试兼容性验证
curl -X POST "http://localhost:8000/api/v1/text/v2/validate?provider_name=openai" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "test"}],
    "model": "gpt-4"
  }'
```

## 📋 预期的日志输出

修复后，您应该看到类似这样的日志：

```
INFO: V2 API收到请求: request_id=9781c21f-4dd2-47d5-8531-c62f6a23e007, type=dict, user_id=user123
INFO: 转换字典格式为优化版本
INFO: 请求适配完成: model=gpt-4, stream=False
INFO: 开始处理同步响应
INFO: 开始处理优化版本文本生成请求
INFO: 正在为供应商 openai 生成特定参数
INFO: 同步响应生成完成
```

## 🔍 如果仍然有问题

### 检查清单：

1. **服务器状态**
   ```bash
   # 检查进程
   ps aux | grep ai_gen_hub
   
   # 检查端口
   netstat -tlnp | grep 8000
   ```

2. **依赖检查**
   ```bash
   # 检查关键依赖
   python -c "import structlog, pydantic, fastapi; print('Dependencies OK')"
   ```

3. **配置检查**
   ```bash
   # 检查环境变量
   echo $DEBUG
   echo $LOG_LEVEL
   
   # 检查配置文件
   cat config/development.yaml
   ```

4. **日志检查**
   ```bash
   # 查看应用日志
   tail -f logs/app.log
   
   # 或者如果使用systemd
   journalctl -u ai-gen-hub -f
   ```

### 常见问题解决：

**问题1：ImportError**
```bash
# 解决方案：重新安装依赖
pip uninstall -y structlog pydantic fastapi
pip install structlog pydantic fastapi
```

**问题2：端口被占用**
```bash
# 解决方案：更换端口或杀死占用进程
lsof -ti:8000 | xargs kill -9
```

**问题3：权限问题**
```bash
# 解决方案：检查文件权限
chmod +x src/ai_gen_hub/api/main.py
```

**问题4：配置文件缺失**
```bash
# 解决方案：创建默认配置
cp config/development.yaml.example config/development.yaml
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **完整的错误日志**（设置DEBUG=true后的日志）
2. **服务器启动日志**
3. **环境信息**：
   ```bash
   python --version
   pip list | grep -E "(fastapi|pydantic|structlog)"
   ```
4. **系统信息**：
   ```bash
   uname -a
   free -h
   df -h
   ```

## 🎯 预期结果

修复后，您应该能够：
- ✅ 成功调用 `/api/v1/text/v2/generate` 端点
- ✅ 看到详细的处理日志
- ✅ 在合理时间内收到响应（通常<30秒）
- ✅ 获得正确格式的JSON响应
- ✅ 使用兼容性验证端点

如果所有步骤都完成但问题仍然存在，可能是更深层的环境或配置问题，需要进一步的系统级诊断。
