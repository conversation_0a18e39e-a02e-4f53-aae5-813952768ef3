#!/usr/bin/env python3
"""
AI Gen Hub 开发环境依赖分析脚本

分析在开发环境下各个服务的必要性和缺失时的影响
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加源代码路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def analyze_database_dependency():
    """分析数据库依赖"""
    print("=== 1. PostgreSQL 数据库分析 ===")
    
    try:
        # 测试不配置数据库的情况
        os.environ.pop('DATABASE_URL', None)
        
        from ai_gen_hub.config import get_settings
        settings = get_settings()
        
        print(f"✓ 数据库配置: {settings.database}")
        print("✓ 应用可以在没有数据库配置的情况下启动")
        print("✓ 数据库在开发环境下是可选的")
        
        # 检查应用初始化
        from ai_gen_hub.api.app import AIGenHubApp
        app = AIGenHubApp()
        
        print("✓ 应用初始化成功，数据库为可选依赖")
        
    except Exception as e:
        print(f"✗ 数据库依赖错误: {e}")
        return False
    
    return True

async def analyze_redis_dependency():
    """分析Redis依赖"""
    print("\n=== 2. Redis 缓存分析 ===")
    
    try:
        # 测试Redis连接失败的情况
        os.environ['REDIS_URL'] = 'redis://nonexistent:6379/0'
        
        from ai_gen_hub.config import get_settings
        from ai_gen_hub.cache.multi_level_cache import MultiLevelCache
        
        settings = get_settings()
        print(f"✓ Redis配置: {settings.redis.url}")
        
        # 测试缓存初始化
        cache = MultiLevelCache(settings.cache, settings.redis)
        print("✓ 多级缓存初始化成功")
        
        # 测试缓存操作（会失败但不会崩溃）
        try:
            await cache.set("test", "value")
            value = await cache.get("test")
            print(f"✓ 缓存操作: {value}")
        except Exception as cache_error:
            print(f"⚠ Redis连接失败，但应用仍可运行: {cache_error}")
            print("✓ 系统会降级到仅使用内存缓存")
        
        await cache.cleanup()
        
    except Exception as e:
        print(f"✗ Redis依赖错误: {e}")
        return False
    
    return True

async def analyze_storage_dependency():
    """分析存储服务依赖"""
    print("\n=== 3. S3/MinIO 存储分析 ===")
    
    try:
        from ai_gen_hub.config import get_settings
        
        # 清除存储配置
        for key in list(os.environ.keys()):
            if key.startswith(('S3_', 'MINIO_')):
                os.environ.pop(key, None)
        
        settings = get_settings()
        
        print(f"✓ S3配置: bucket={settings.storage.s3_bucket}")
        print(f"✓ MinIO配置: endpoint={settings.storage.minio_endpoint}")
        print(f"✓ 本地存储路径: {settings.storage.local_storage_path}")
        
        # 检查存储配置的使用
        print("✓ 存储服务在当前实现中主要用于配置，不是启动必需的")
        print("✓ 应用可以在没有S3/MinIO配置的情况下运行")
        
    except Exception as e:
        print(f"✗ 存储依赖错误: {e}")
        return False
    
    return True

async def analyze_ai_providers():
    """分析AI供应商依赖"""
    print("\n=== 4. AI供应商分析 ===")
    
    try:
        # 清除AI供应商配置
        for key in list(os.environ.keys()):
            if any(provider in key for provider in ['OPENAI', 'GOOGLE_AI', 'ANTHROPIC']):
                os.environ.pop(key, None)
        
        from ai_gen_hub.config import get_settings
        from ai_gen_hub.services.provider_manager import AIProviderManager
        from ai_gen_hub.utils.key_manager import KeyManager
        
        settings = get_settings()
        
        print(f"✓ OpenAI配置: enabled={settings.openai.enabled}, keys={len(settings.openai.api_keys)}")
        print(f"✓ Google AI配置: enabled={settings.google_ai.enabled}, keys={len(settings.google_ai.api_keys)}")
        print(f"✓ Anthropic配置: enabled={settings.anthropic.enabled}, keys={len(settings.anthropic.api_keys)}")
        
        # 测试供应商管理器初始化
        key_manager = KeyManager(settings)
        await key_manager.initialize()
        
        provider_manager = AIProviderManager(settings, key_manager)
        await provider_manager.initialize()
        
        enabled_providers = settings.get_enabled_providers()
        print(f"✓ 启用的供应商: {enabled_providers}")
        
        if not enabled_providers:
            print("⚠ 没有配置AI供应商API密钥，但应用仍可启动")
            print("✓ AI供应商配置是功能性的，不是启动必需的")
        
        await provider_manager.cleanup()
        await key_manager.cleanup()
        
    except Exception as e:
        print(f"✗ AI供应商依赖错误: {e}")
        return False
    
    return True

async def test_app_startup():
    """测试应用完整启动"""
    print("\n=== 5. 应用启动测试 ===")
    
    try:
        from ai_gen_hub.api.app import AIGenHubApp
        
        app = AIGenHubApp()
        
        # 测试应用初始化
        await app.initialize()
        print("✓ 应用初始化成功")
        
        # 检查各组件状态
        print(f"✓ 密钥管理器: {app.key_manager is not None}")
        print(f"✓ 供应商管理器: {app.provider_manager is not None}")
        print(f"✓ 请求路由器: {app.router is not None}")
        print(f"✓ 缓存系统: {app.cache is not None}")
        print(f"✓ 健康管理器: {app.health_manager is not None}")
        
        # 清理资源
        await app.cleanup()
        print("✓ 应用清理成功")
        
    except Exception as e:
        print(f"✗ 应用启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

async def main():
    """主函数"""
    print("AI Gen Hub 开发环境依赖分析")
    print("=" * 50)
    
    # 设置开发环境
    os.environ['ENVIRONMENT'] = 'development'
    os.environ['DEBUG'] = 'true'
    
    results = []
    
    # 分析各个依赖
    results.append(await analyze_database_dependency())
    results.append(await analyze_redis_dependency())
    results.append(await analyze_storage_dependency())
    results.append(await analyze_ai_providers())
    results.append(await test_app_startup())
    
    # 总结
    print("\n" + "=" * 50)
    print("=== 分析总结 ===")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"✓ 成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有测试通过！应用可以在最小配置下运行")
    else:
        print("⚠ 部分测试失败，请检查配置")

if __name__ == "__main__":
    asyncio.run(main())
