#!/usr/bin/env python3
"""
V2 API问题诊断脚本

诊断优化版本文本生成API的问题，包括路由注册、依赖注入、异步处理等。
"""

import os
import sys
import asyncio
import json
from typing import Dict, Any

# 添加源码路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_route_registration():
    """检查路由注册情况"""
    print("🔍 检查路由注册情况...")
    
    try:
        # 设置环境变量
        os.environ['DEBUG'] = 'true'
        os.environ['ENVIRONMENT'] = 'development'
        
        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        print("✅ FastAPI应用创建成功")
        
        # 收集所有路由
        all_routes = []
        text_routes = []
        v2_routes = []
        
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                for method in route.methods:
                    if method != 'HEAD':
                        route_str = f"{method} {route.path}"
                        all_routes.append(route_str)
                        
                        if '/text' in route.path:
                            text_routes.append(route_str)
                        
                        if '/v2' in route.path:
                            v2_routes.append(route_str)
        
        print(f"   总路由数量: {len(all_routes)}")
        print(f"   文本相关路由: {len(text_routes)}")
        print(f"   V2路由: {len(v2_routes)}")
        
        print("\n📋 文本相关路由列表:")
        for route in sorted(text_routes):
            print(f"   {route}")
        
        print("\n📋 V2路由列表:")
        for route in sorted(v2_routes):
            print(f"   {route}")
        
        # 检查目标路由是否存在
        target_route = "POST /api/v1/text/v2/generate"
        if target_route in all_routes:
            print(f"\n✅ 目标路由 {target_route} 已正确注册")
        else:
            print(f"\n❌ 目标路由 {target_route} 未找到")
            print("   可能的原因:")
            print("   1. 路由定义有误")
            print("   2. 路由器未正确包含")
            print("   3. 路径前缀配置错误")
        
        return app, all_routes
        
    except Exception as e:
        print(f"❌ 路由检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None, []


def check_dependencies():
    """检查依赖注入"""
    print("\n🔧 检查依赖注入...")
    
    try:
        from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest
        from ai_gen_hub.services.text_generation import TextGenerationService
        
        print("✅ 核心接口导入成功")
        
        # 检查OptimizedTextGenerationRequest是否可以正常创建
        from ai_gen_hub.core.interfaces import Message, MessageRole, GenerationConfig
        
        test_request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="test")],
            model="gpt-4",
            generation=GenerationConfig(temperature=0.7)
        )
        print("✅ OptimizedTextGenerationRequest创建成功")
        
        # 检查方法是否存在
        if hasattr(TextGenerationService, 'generate_text_optimized'):
            print("✅ TextGenerationService.generate_text_optimized方法存在")
        else:
            print("❌ TextGenerationService.generate_text_optimized方法不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 依赖检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_request_parsing():
    """检查请求解析"""
    print("\n📝 检查请求解析...")
    
    try:
        from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest, Message, MessageRole
        
        # 测试字典格式解析
        test_dict = {
            "messages": [{"role": "user", "content": "Hello"}],
            "model": "gpt-4",
            "generation": {
                "temperature": 0.8,
                "max_tokens": 1000
            },
            "stream": {
                "enabled": False
            }
        }
        
        try:
            request = OptimizedTextGenerationRequest(**test_dict)
            print("✅ 字典格式解析成功")
        except Exception as e:
            print(f"❌ 字典格式解析失败: {e}")
        
        # 测试传统格式转换
        legacy_dict = {
            "messages": [{"role": "user", "content": "Hello"}],
            "model": "gpt-4",
            "temperature": 0.8,
            "max_tokens": 1000,
            "stream": False
        }
        
        try:
            optimized = OptimizedTextGenerationRequest.from_legacy_request(legacy_dict)
            print("✅ 传统格式转换成功")
        except Exception as e:
            print(f"❌ 传统格式转换失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 请求解析检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_service_method():
    """测试服务方法"""
    print("\n🧪 测试服务方法...")
    
    try:
        from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest, Message, MessageRole
        from ai_gen_hub.services.text_generation import TextGenerationService
        
        # 创建测试请求
        test_request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Hello")],
            model="gpt-4"
        )
        
        # 检查服务方法是否可调用（不实际调用，只检查签名）
        service = TextGenerationService.__new__(TextGenerationService)
        
        if hasattr(service, 'generate_text_optimized'):
            print("✅ generate_text_optimized方法存在")
            
            # 检查方法签名
            import inspect
            sig = inspect.signature(service.generate_text_optimized)
            print(f"   方法签名: {sig}")
            
            # 检查是否是异步方法
            if inspect.iscoroutinefunction(service.generate_text_optimized):
                print("✅ 方法是异步的")
            else:
                print("❌ 方法不是异步的")
        else:
            print("❌ generate_text_optimized方法不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_potential_issues():
    """检查潜在问题"""
    print("\n⚠️  检查潜在问题...")
    
    issues = []
    
    # 检查导入问题
    try:
        from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest
        from ai_gen_hub.core.optimized_request_utils import OptimizedRequestMixin
        from ai_gen_hub.core.provider_adapter import ProviderAdapterMixin
    except ImportError as e:
        issues.append(f"导入错误: {e}")
    
    # 检查循环导入
    try:
        import ai_gen_hub.api.routers.text
        import ai_gen_hub.services.text_generation
        import ai_gen_hub.core.interfaces
    except Exception as e:
        issues.append(f"可能的循环导入: {e}")
    
    # 检查异步上下文
    try:
        import asyncio
        loop = asyncio.get_event_loop()
        if loop.is_running():
            print("✅ 异步事件循环正在运行")
        else:
            issues.append("异步事件循环未运行")
    except Exception as e:
        issues.append(f"异步上下文问题: {e}")
    
    if issues:
        print("❌ 发现潜在问题:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ 未发现明显的潜在问题")
    
    return len(issues) == 0


def generate_debug_request():
    """生成调试请求"""
    print("\n📤 生成调试请求...")
    
    # 生成测试请求
    test_request = {
        "messages": [
            {"role": "user", "content": "Hello, this is a test message"}
        ],
        "model": "gpt-4",
        "generation": {
            "temperature": 0.7,
            "max_tokens": 100
        },
        "stream": {
            "enabled": False
        }
    }
    
    print("📋 测试请求JSON:")
    print(json.dumps(test_request, indent=2, ensure_ascii=False))
    
    print("\n🌐 cURL命令:")
    curl_cmd = f"""curl -X POST "http://localhost:8000/api/v1/text/v2/generate" \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(test_request, ensure_ascii=False)}'"""
    print(curl_cmd)
    
    return test_request


def provide_solutions():
    """提供解决方案"""
    print("\n💡 问题解决方案:")
    
    solutions = [
        "1. 确认API路径: 使用 /api/v1/text/v2/generate 而不是 /api/v2/text/generate",
        "2. 检查服务启动: 确保所有依赖服务（Redis、数据库等）正常运行",
        "3. 检查日志级别: 设置 DEBUG=true 以获取详细日志",
        "4. 验证请求格式: 确保请求体符合OptimizedTextGenerationRequest格式",
        "5. 检查异步处理: 确保所有异步方法正确使用await",
        "6. 验证依赖注入: 确保TextGenerationService正确初始化",
        "7. 检查供应商配置: 确保至少有一个AI供应商正确配置",
        "8. 监控资源使用: 检查内存和CPU使用情况",
        "9. 检查网络连接: 确保能够访问AI供应商API",
        "10. 查看完整日志: 检查应用启动日志中的错误信息"
    ]
    
    for solution in solutions:
        print(f"   {solution}")
    
    print("\n🔧 调试步骤:")
    debug_steps = [
        "1. 启动应用时设置环境变量: DEBUG=true LOG_LEVEL=DEBUG",
        "2. 检查 /health 端点确认服务正常",
        "3. 先测试 /api/v1/text/generate 端点确认基础功能",
        "4. 使用 /api/v1/text/v2/validate 端点验证请求格式",
        "5. 逐步增加请求复杂度进行测试"
    ]
    
    for step in debug_steps:
        print(f"   {step}")


async def main():
    """主函数"""
    print("🚀 开始V2 API问题诊断\n")
    
    # 执行所有检查
    app, routes = check_route_registration()
    deps_ok = check_dependencies()
    parsing_ok = check_request_parsing()
    service_ok = await test_service_method()
    issues_ok = check_potential_issues()
    
    # 生成调试信息
    generate_debug_request()
    provide_solutions()
    
    # 总结
    print(f"\n📊 诊断总结:")
    print(f"   路由注册: {'✅' if app else '❌'}")
    print(f"   依赖注入: {'✅' if deps_ok else '❌'}")
    print(f"   请求解析: {'✅' if parsing_ok else '❌'}")
    print(f"   服务方法: {'✅' if service_ok else '❌'}")
    print(f"   潜在问题: {'✅' if issues_ok else '❌'}")
    
    if all([app, deps_ok, parsing_ok, service_ok, issues_ok]):
        print("\n🎉 所有检查通过！问题可能在运行时环境或配置。")
    else:
        print("\n⚠️  发现问题，请根据上述信息进行修复。")


if __name__ == "__main__":
    asyncio.run(main())
