# AI Gen Hub 环境配置示例文件
# 复制此文件为 .env 并填入实际的配置值

# =============================================================================
# 基础配置
# =============================================================================

# 应用环境 (development, staging, production)
ENVIRONMENT=development

# 应用名称
APP_NAME=AI Gen Hub

# 应用版本
APP_VERSION=0.1.0

# 调试模式
DEBUG=true

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# =============================================================================
# 数据库配置
# =============================================================================

# PostgreSQL数据库连接
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/ai_gen_hub

# 数据库连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# =============================================================================
# Redis配置
# =============================================================================

# Redis连接配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# Redis连接池配置
REDIS_POOL_SIZE=10
REDIS_POOL_TIMEOUT=10

# =============================================================================
# AI供应商配置
# =============================================================================

# OpenAI配置
OPENAI_API_KEYS=sk-xxx,sk-yyy,sk-zzz
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_TIMEOUT=60
OPENAI_MAX_RETRIES=3

# Google AI配置
GOOGLE_AI_API_KEYS=AIzaSyXXX,AIzaSyYYY
GOOGLE_AI_TIMEOUT=60
GOOGLE_AI_MAX_RETRIES=3

# Anthropic配置
ANTHROPIC_API_KEYS=sk-ant-xxx,sk-ant-yyy
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_TIMEOUT=60
ANTHROPIC_MAX_RETRIES=3

# =============================================================================
# 缓存配置
# =============================================================================

# 内存缓存配置
MEMORY_CACHE_SIZE=1000
MEMORY_CACHE_TTL=3600

# Redis缓存配置
REDIS_CACHE_TTL=7200
REDIS_CACHE_PREFIX=ai_gen_hub:cache:

# =============================================================================
# 监控配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志格式 (json, text)
LOG_FORMAT=json

# Prometheus监控
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# 健康检查配置
HEALTH_CHECK_INTERVAL=60
HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# 安全配置
# =============================================================================

# JWT密钥
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# API密钥
API_KEY=your-api-key-here

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOW_CREDENTIALS=true

# =============================================================================
# 文件存储配置
# =============================================================================

# 本地存储路径
LOCAL_STORAGE_PATH=./storage

# S3配置
S3_BUCKET=ai-gen-hub-storage
S3_REGION=us-east-1
S3_ACCESS_KEY=your-access-key
S3_SECRET_KEY=your-secret-key
S3_ENDPOINT_URL=

# MinIO配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=ai-gen-hub
MINIO_SECURE=false

# =============================================================================
# 性能配置
# =============================================================================

# 请求限制
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 超时配置
REQUEST_TIMEOUT=300
STREAM_TIMEOUT=600

# 并发配置
MAX_CONCURRENT_REQUESTS=100
MAX_QUEUE_SIZE=1000

# =============================================================================
# 特性开关
# =============================================================================

# 启用功能标志
ENABLE_TEXT_GENERATION=true
ENABLE_IMAGE_GENERATION=true
ENABLE_STREAMING=true
ENABLE_CACHING=true
ENABLE_MONITORING=true
ENABLE_RATE_LIMITING=true
