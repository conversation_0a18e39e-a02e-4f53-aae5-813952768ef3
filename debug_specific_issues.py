#!/usr/bin/env python3
"""
诊断调试界面的具体问题

专门用于分析和解决：
1. test-endpoint接口挂起问题
2. system/info接口404错误
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

async def test_system_info_endpoint():
    """测试 system/info 端点"""
    print("🔍 测试 /debug/api/system/info 端点...")
    
    try:
        # 设置环境变量
        os.environ.setdefault('ENVIRONMENT', 'development')
        os.environ.setdefault('DEBUG', 'true')
        
        from fastapi.testclient import TestClient
        from ai_gen_hub.api.app import create_app
        
        # 创建应用
        app = create_app()
        
        # 检查路由是否存在
        system_info_route = None
        for route in app.routes:
            if hasattr(route, 'path') and route.path == '/debug/api/system/info':
                system_info_route = route
                break
        
        if system_info_route:
            print("✅ 路由已注册")
            print(f"   路径: {system_info_route.path}")
            print(f"   方法: {getattr(system_info_route, 'methods', 'Unknown')}")
        else:
            print("❌ 路由未找到")
            return False
        
        # 使用TestClient测试
        with TestClient(app) as client:
            print("\n📡 发送测试请求...")
            start_time = time.time()
            
            try:
                response = client.get("/debug/api/system/info", timeout=10)
                end_time = time.time()
                
                print(f"⏱️  响应时间: {(end_time - start_time):.2f}秒")
                print(f"📊 状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 请求成功")
                    data = response.json()
                    print(f"📋 响应数据键: {list(data.keys())}")
                    return True
                elif response.status_code == 404:
                    print("❌ 404 Not Found")
                    print(f"响应内容: {response.text}")
                    return False
                elif response.status_code == 500:
                    print("❌ 500 Internal Server Error")
                    print(f"错误详情: {response.text}")
                    return False
                else:
                    print(f"⚠️  意外状态码: {response.status_code}")
                    print(f"响应内容: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
                import traceback
                traceback.print_exc()
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_test_endpoint_api():
    """测试 test-endpoint 接口"""
    print("\n🔍 测试 /debug/api/test-endpoint 端点...")
    
    try:
        from fastapi.testclient import TestClient
        from ai_gen_hub.api.app import create_app
        
        # 创建应用
        app = create_app()
        
        # 检查路由是否存在
        test_endpoint_route = None
        for route in app.routes:
            if hasattr(route, 'path') and route.path == '/debug/api/test-endpoint':
                test_endpoint_route = route
                break
        
        if test_endpoint_route:
            print("✅ 路由已注册")
            print(f"   路径: {test_endpoint_route.path}")
            print(f"   方法: {getattr(test_endpoint_route, 'methods', 'Unknown')}")
        else:
            print("❌ 路由未找到")
            return False
        
        # 准备测试数据
        test_data = {
            "url": "/debug/api/system/info",
            "method": "GET",
            "headers": {},
            "body": "",
            "params": {}
        }
        
        # 使用TestClient测试
        with TestClient(app) as client:
            print("\n📡 发送测试请求...")
            print(f"📋 测试数据: {json.dumps(test_data, indent=2)}")
            
            start_time = time.time()
            
            try:
                # 设置较短的超时时间来检测挂起问题
                response = client.post(
                    "/debug/api/test-endpoint", 
                    json=test_data,
                    timeout=15  # 15秒超时
                )
                end_time = time.time()
                
                print(f"⏱️  响应时间: {(end_time - start_time):.2f}秒")
                print(f"📊 状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 请求成功")
                    data = response.json()
                    print(f"📋 响应数据键: {list(data.keys())}")
                    
                    # 检查是否成功
                    if data.get('success'):
                        print("✅ API测试成功")
                        if 'response' in data:
                            print(f"   目标API状态码: {data['response'].get('status_code')}")
                            print(f"   响应时间: {data['timing'].get('response_time', 0):.2f}ms")
                    else:
                        print("❌ API测试失败")
                        print(f"   错误: {data.get('error', 'Unknown error')}")
                    
                    return True
                else:
                    print(f"❌ 意外状态码: {response.status_code}")
                    print(f"响应内容: {response.text}")
                    return False
                    
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                
                if "timeout" in str(e).lower() or duration >= 14:
                    print(f"⏰ 请求超时 (耗时: {duration:.2f}秒)")
                    print("🔍 这可能表明存在挂起问题")
                else:
                    print(f"❌ 请求异常: {e}")
                    import traceback
                    traceback.print_exc()
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def analyze_system_info_function():
    """分析 get_system_info 函数的潜在问题"""
    print("\n🔍 分析 get_system_info 函数...")
    
    try:
        # 直接测试 get_system_info 函数
        from ai_gen_hub.api.routers.debug import get_system_info
        
        print("📊 直接调用 get_system_info()...")
        start_time = time.time()
        
        try:
            system_info = get_system_info()
            end_time = time.time()
            
            print(f"⏱️  执行时间: {(end_time - start_time):.2f}秒")
            print("✅ 函数执行成功")
            print(f"📋 返回数据: CPU={system_info.cpu_percent}%, 内存={system_info.memory_percent}%")
            
            # 检查是否有阻塞调用
            if (end_time - start_time) > 2:
                print("⚠️  函数执行时间较长，可能存在阻塞调用")
                print("🔍 检查 psutil.cpu_percent(interval=1) 调用")
            
            return True
            
        except Exception as e:
            end_time = time.time()
            print(f"❌ 函数执行失败: {e}")
            print(f"⏱️  执行时间: {(end_time - start_time):.2f}秒")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_httpx_client():
    """测试 httpx 客户端是否正常工作"""
    print("\n🔍 测试 httpx 客户端...")
    
    try:
        import httpx
        
        print("📡 测试 httpx 异步客户端...")
        start_time = time.time()
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 测试一个简单的HTTP请求
            response = await client.get("https://httpbin.org/get")
            
        end_time = time.time()
        
        print(f"⏱️  请求时间: {(end_time - start_time):.2f}秒")
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ httpx 客户端工作正常")
            return True
        else:
            print("❌ httpx 客户端响应异常")
            return False
            
    except Exception as e:
        print(f"❌ httpx 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始诊断调试界面的具体问题...")
    print("=" * 60)
    
    results = {}
    
    # 测试1: system/info 端点
    results['system_info'] = await test_system_info_endpoint()
    
    # 测试2: get_system_info 函数分析
    results['system_info_function'] = await analyze_system_info_function()
    
    # 测试3: test-endpoint 接口
    results['test_endpoint'] = await test_test_endpoint_api()
    
    # 测试4: httpx 客户端
    results['httpx_client'] = await test_httpx_client()
    
    print("\n" + "=" * 60)
    print("📊 诊断结果总结:")
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 分析问题
    print("\n🔍 问题分析:")
    
    if not results.get('system_info'):
        print("❌ system/info 接口存在问题")
        if not results.get('system_info_function'):
            print("   - get_system_info 函数执行失败")
            print("   - 建议检查 psutil 依赖和系统权限")
        else:
            print("   - get_system_info 函数正常，可能是路由或中间件问题")
    
    if not results.get('test_endpoint'):
        print("❌ test-endpoint 接口存在问题")
        if not results.get('httpx_client'):
            print("   - httpx 客户端异常")
            print("   - 建议检查网络连接和 httpx 依赖")
        else:
            print("   - httpx 客户端正常，可能是接口逻辑问题")
    
    print("\n💡 修复建议:")
    if not results.get('system_info'):
        print("1. 修复 system/info 接口的阻塞问题")
    if not results.get('test_endpoint'):
        print("2. 修复 test-endpoint 接口的挂起问题")
    
    return all(results.values())

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
