# AI Gen Hub 快速启动指南

## 问题解决方案

### 1. 模块导入问题修复

#### 方法一：安装项目到虚拟环境（推荐）

```bash
# 进入项目目录
cd /root/workspace/git.atjog.com/aier/ai-gen-hub

# 激活虚拟环境
source venv/bin/activate

# 运行修复脚本
chmod +x fix_module_import.sh
./fix_module_import.sh
```

#### 方法二：直接运行（临时解决）

```bash
# 进入项目目录
cd /root/workspace/git.atjog.com/aier/ai-gen-hub

# 激活虚拟环境
source venv/bin/activate

# 安装基础依赖
pip install -r requirements.txt

# 使用直接运行脚本
python run_server.py serve --host 0.0.0.0 --port 8001
```

### 2. 完整开发环境启动

#### 启动AI Gen Hub + 调试页面

```bash
# 进入项目目录
cd /root/workspace/git.atjog.com/aier/ai-gen-hub

# 激活虚拟环境
source venv/bin/activate

# 启动完整环境（AI Gen Hub + 调试页面）
python start_with_debug.py
```

这将启动：
- **AI Gen Hub服务**: http://localhost:8001
- **调试仪表板**: http://localhost:8000
- **API文档**: http://localhost:8001/docs

#### 仅启动调试页面

```bash
# 启动独立调试页面
python debug_standalone.py
```

访问: http://localhost:8000

## 配置说明

### 最小配置

创建 `.env` 文件：

```bash
# 基础配置
ENVIRONMENT=development
DEBUG=true
API_PORT=8001

# 安全配置
JWT_SECRET_KEY=dev-secret-key
API_KEY=dev-api-key

# AI供应商配置（至少配置一个）
OPENAI_API_KEYS=sk-your-openai-key
# GOOGLE_AI_API_KEYS=your-google-ai-key
# ANTHROPIC_API_KEYS=sk-ant-your-anthropic-key
```

### 可选服务配置

```bash
# Redis缓存（推荐）
REDIS_URL=redis://localhost:6379/0

# 数据库（可选）
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/ai_gen_hub

# 存储（可选）
LOCAL_STORAGE_PATH=./storage
```

## 功能特性

### 调试页面功能

1. **系统监控**
   - 实时系统资源监控
   - AI Gen Hub服务状态
   - 健康检查结果

2. **API测试**
   - 文本生成测试
   - 图像生成测试
   - 供应商状态检查

3. **配置查看**
   - 当前配置信息
   - 环境变量状态
   - 服务连接状态

### AI Gen Hub API

1. **文本生成**
   ```bash
   curl -X POST http://localhost:8001/api/v1/text/generate \
     -H "Content-Type: application/json" \
     -d '{
       "model": "gpt-3.5-turbo",
       "messages": [{"role": "user", "content": "你好"}]
     }'
   ```

2. **图像生成**
   ```bash
   curl -X POST http://localhost:8001/api/v1/image/generate \
     -H "Content-Type: application/json" \
     -d '{
       "prompt": "一只可爱的小猫",
       "model": "dall-e-3"
     }'
   ```

## 故障排除

### 常见问题

1. **模块导入错误**
   ```
   ModuleNotFoundError: No module named 'ai_gen_hub'
   ```
   **解决**: 运行 `./fix_module_import.sh` 或使用 `python run_server.py`

2. **端口占用**
   ```
   OSError: [Errno 98] Address already in use
   ```
   **解决**: 更改端口或停止占用进程
   ```bash
   # 查找占用进程
   lsof -i :8000
   lsof -i :8001
   
   # 停止进程
   kill -9 <PID>
   ```

3. **依赖缺失**
   ```
   ModuleNotFoundError: No module named 'xxx'
   ```
   **解决**: 安装依赖
   ```bash
   pip install -r requirements.txt
   ```

4. **AI供应商API错误**
   ```
   没有可用的AI供应商
   ```
   **解决**: 配置至少一个有效的API密钥

### 调试技巧

1. **查看日志**
   ```bash
   # 启动时查看详细日志
   python run_server.py serve --debug
   ```

2. **检查配置**
   ```bash
   # 生成配置模板
   python run_server.py generate-config --output config.yaml
   ```

3. **健康检查**
   ```bash
   # 检查所有供应商
   python run_server.py health-check
   
   # 检查特定供应商
   python run_server.py health-check --provider openai
   ```

## 开发建议

### 推荐工作流

1. **启动开发环境**
   ```bash
   python start_with_debug.py
   ```

2. **配置AI供应商**
   - 在 `.env` 文件中添加API密钥
   - 通过调试页面测试连接

3. **开发和测试**
   - 使用调试页面测试API
   - 查看实时监控数据
   - 检查日志和错误

4. **部署准备**
   - 使用生产配置
   - 运行健康检查
   - 验证所有功能

### 性能优化

1. **启用Redis缓存**
   ```bash
   docker run -d --name redis -p 6379:6379 redis:7-alpine
   ```

2. **配置多个AI供应商**
   - 提供负载均衡
   - 增强容错能力

3. **调整并发设置**
   ```bash
   # 生产环境
   python run_server.py serve --workers 4
   ```

## 总结

通过以上步骤，您可以：

1. ✅ 解决模块导入问题
2. ✅ 启动完整的开发环境
3. ✅ 使用集成的调试页面
4. ✅ 测试所有AI功能
5. ✅ 监控系统状态

如果遇到问题，请检查：
- Python环境和依赖
- 配置文件和环境变量
- 网络连接和API密钥
- 端口占用情况
