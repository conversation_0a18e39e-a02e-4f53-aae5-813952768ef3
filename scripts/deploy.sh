#!/bin/bash

# AI Gen Hub 部署脚本
# 支持 Docker、Kubernetes 和传统部署方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AI Gen Hub 部署脚本

用法: $0 [选项] <部署类型>

部署类型:
  docker          使用 Docker Compose 部署
  k8s             部署到 Kubernetes 集群
  local           本地开发环境部署
  production      生产环境部署

选项:
  -h, --help      显示此帮助信息
  -v, --verbose   详细输出
  -d, --dry-run   模拟运行，不执行实际操作
  --no-build      跳过构建步骤
  --no-test       跳过测试步骤
  --config FILE   指定配置文件路径

示例:
  $0 docker                    # Docker 部署
  $0 k8s --config prod.yaml    # Kubernetes 生产部署
  $0 local --no-test           # 本地部署，跳过测试

EOF
}

# 默认参数
DEPLOYMENT_TYPE=""
VERBOSE=false
DRY_RUN=false
NO_BUILD=false
NO_TEST=false
CONFIG_FILE=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        --no-build)
            NO_BUILD=true
            shift
            ;;
        --no-test)
            NO_TEST=true
            shift
            ;;
        --config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        docker|k8s|local|production)
            DEPLOYMENT_TYPE="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查部署类型
if [[ -z "$DEPLOYMENT_TYPE" ]]; then
    log_error "请指定部署类型"
    show_help
    exit 1
fi

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    case $DEPLOYMENT_TYPE in
        docker)
            if ! command -v docker &> /dev/null; then
                log_error "Docker 未安装"
                exit 1
            fi
            if ! command -v docker-compose &> /dev/null; then
                log_error "Docker Compose 未安装"
                exit 1
            fi
            ;;
        k8s)
            if ! command -v kubectl &> /dev/null; then
                log_error "kubectl 未安装"
                exit 1
            fi
            if ! kubectl cluster-info &> /dev/null; then
                log_error "无法连接到 Kubernetes 集群"
                exit 1
            fi
            ;;
        local|production)
            if ! command -v python3 &> /dev/null; then
                log_error "Python 3 未安装"
                exit 1
            fi
            ;;
    esac
    
    log_success "依赖检查通过"
}

# 运行测试
run_tests() {
    if [[ "$NO_TEST" == "true" ]]; then
        log_warning "跳过测试"
        return
    fi
    
    log_info "运行测试..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将运行测试"
        return
    fi
    
    python3 tests/run_tests.py all --coverage
    
    log_success "测试通过"
}

# 构建应用
build_app() {
    if [[ "$NO_BUILD" == "true" ]]; then
        log_warning "跳过构建"
        return
    fi
    
    log_info "构建应用..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将构建 Docker 镜像"
        return
    fi
    
    case $DEPLOYMENT_TYPE in
        docker|k8s)
            # 构建 Docker 镜像
            docker build -t ai-gen-hub:latest .
            
            # 如果是 Kubernetes 部署，可能需要推送到镜像仓库
            if [[ "$DEPLOYMENT_TYPE" == "k8s" ]]; then
                if [[ -n "$DOCKER_REGISTRY" ]]; then
                    docker tag ai-gen-hub:latest $DOCKER_REGISTRY/ai-gen-hub:latest
                    docker push $DOCKER_REGISTRY/ai-gen-hub:latest
                fi
            fi
            ;;
        local|production)
            # 安装 Python 依赖
            pip install -e .
            ;;
    esac
    
    log_success "构建完成"
}

# Docker 部署
deploy_docker() {
    log_info "使用 Docker Compose 部署..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将执行: docker-compose up -d"
        return
    fi
    
    # 检查配置文件
    if [[ ! -f "docker-compose.yml" ]]; then
        log_error "docker-compose.yml 文件不存在"
        exit 1
    fi
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "Docker 部署成功"
        log_info "服务地址: http://localhost:8000"
        log_info "健康检查: http://localhost:8000/health"
        log_info "API 文档: http://localhost:8000/docs"
    else
        log_error "Docker 部署失败"
        docker-compose logs
        exit 1
    fi
}

# Kubernetes 部署
deploy_k8s() {
    log_info "部署到 Kubernetes 集群..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将应用 Kubernetes 配置"
        return
    fi
    
    # 检查配置文件
    if [[ ! -d "k8s" ]]; then
        log_error "k8s 目录不存在"
        exit 1
    fi
    
    # 应用配置
    log_info "创建命名空间..."
    kubectl apply -f k8s/namespace.yaml
    
    log_info "应用配置..."
    kubectl apply -f k8s/secrets.yaml
    kubectl apply -f k8s/configmap.yaml
    
    log_info "部署 Redis..."
    kubectl apply -f k8s/redis.yaml
    
    log_info "部署应用..."
    kubectl apply -f k8s/deployment.yaml
    
    log_info "配置自动扩缩..."
    kubectl apply -f k8s/hpa.yaml
    
    # 等待部署完成
    log_info "等待部署完成..."
    kubectl rollout status deployment/ai-gen-hub -n ai-gen-hub --timeout=300s
    
    # 检查服务状态
    if kubectl get pods -n ai-gen-hub | grep -q "Running"; then
        log_success "Kubernetes 部署成功"
        
        # 获取服务信息
        INGRESS_IP=$(kubectl get ingress ai-gen-hub-ingress -n ai-gen-hub -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
        if [[ -n "$INGRESS_IP" ]]; then
            log_info "服务地址: http://$INGRESS_IP"
        else
            log_info "使用 kubectl port-forward 访问服务:"
            log_info "kubectl port-forward -n ai-gen-hub service/ai-gen-hub-service 8000:80"
        fi
    else
        log_error "Kubernetes 部署失败"
        kubectl get pods -n ai-gen-hub
        kubectl logs -n ai-gen-hub deployment/ai-gen-hub
        exit 1
    fi
}

# 本地部署
deploy_local() {
    log_info "本地开发环境部署..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动本地开发服务器"
        return
    fi
    
    # 设置环境变量
    export ENVIRONMENT=development
    export DEBUG=true
    
    # 启动开发服务器
    log_info "启动开发服务器..."
    python -m ai_gen_hub.api.app &
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    if curl -f http://localhost:8000/health/live &> /dev/null; then
        log_success "本地部署成功"
        log_info "服务地址: http://localhost:8000"
        log_info "API 文档: http://localhost:8000/docs"
    else
        log_error "本地部署失败"
        exit 1
    fi
}

# 生产部署
deploy_production() {
    log_info "生产环境部署..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将执行生产环境部署"
        return
    fi
    
    # 检查配置文件
    if [[ -n "$CONFIG_FILE" && ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    # 设置环境变量
    export ENVIRONMENT=production
    export DEBUG=false
    
    if [[ -n "$CONFIG_FILE" ]]; then
        export CONFIG_FILE="$CONFIG_FILE"
    fi
    
    # 使用 Gunicorn 启动生产服务器
    log_info "启动生产服务器..."
    gunicorn ai_gen_hub.api.app:create_app \
        --factory \
        --bind 0.0.0.0:8000 \
        --workers 4 \
        --worker-class uvicorn.workers.UvicornWorker \
        --access-logfile - \
        --error-logfile - \
        --log-level info \
        --daemon
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    if curl -f http://localhost:8000/health/live &> /dev/null; then
        log_success "生产部署成功"
        log_info "服务地址: http://localhost:8000"
    else
        log_error "生产部署失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始 AI Gen Hub 部署 ($DEPLOYMENT_TYPE)"
    
    # 检查依赖
    check_dependencies
    
    # 运行测试
    run_tests
    
    # 构建应用
    build_app
    
    # 根据部署类型执行相应的部署
    case $DEPLOYMENT_TYPE in
        docker)
            deploy_docker
            ;;
        k8s)
            deploy_k8s
            ;;
        local)
            deploy_local
            ;;
        production)
            deploy_production
            ;;
        *)
            log_error "不支持的部署类型: $DEPLOYMENT_TYPE"
            exit 1
            ;;
    esac
    
    log_success "部署完成！"
}

# 执行主函数
main
