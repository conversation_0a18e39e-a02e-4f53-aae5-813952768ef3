#!/usr/bin/env python3
"""
V2 API修复验证脚本

验证修复后的V2 API是否能正常工作
"""

import asyncio
import aiohttp
import json
import time
import sys

async def test_v2_api_endpoint():
    """测试V2 API端点"""
    print("🧪 测试V2 API端点...")
    
    # 测试请求
    test_request = {
        "messages": [
            {"role": "user", "content": "Hello, this is a test message for V2 API"}
        ],
        "model": "gpt-4",
        "generation": {
            "temperature": 0.7,
            "max_tokens": 100
        },
        "stream": {
            "enabled": False
        }
    }
    
    url = "http://localhost:8000/api/v1/text/v2/generate"
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"📤 发送请求到: {url}")
            print(f"📋 请求体: {json.dumps(test_request, indent=2, ensure_ascii=False)}")
            
            start_time = time.time()
            
            async with session.post(
                url,
                json=test_request,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=60)  # 60秒超时
            ) as response:
                elapsed = time.time() - start_time
                
                print(f"📊 响应状态: {response.status}")
                print(f"⏱️  响应时间: {elapsed:.2f}s")
                print(f"📄 响应头: {dict(response.headers)}")
                
                if response.status == 200:
                    try:
                        result = await response.json()
                        print("✅ 请求成功!")
                        print("📋 响应内容:")
                        print(json.dumps(result, indent=2, ensure_ascii=False))
                        return True
                    except Exception as e:
                        print(f"❌ 响应解析失败: {e}")
                        response_text = await response.text()
                        print(f"原始响应: {response_text}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ 请求失败 (状态码: {response.status})")
                    print(f"错误信息: {error_text}")
                    return False
                    
    except asyncio.TimeoutError:
        print("❌ 请求超时 (60秒)")
        return False
    except aiohttp.ClientConnectorError:
        print("❌ 连接失败 - 请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


async def test_v2_validate_endpoint():
    """测试V2验证端点"""
    print("\n🔍 测试V2验证端点...")
    
    test_request = {
        "messages": [{"role": "user", "content": "test"}],
        "model": "gpt-4",
        "generation": {"temperature": 0.7}
    }
    
    url = "http://localhost:8000/api/v1/text/v2/validate"
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试OpenAI兼容性
            async with session.post(
                f"{url}?provider_name=openai",
                json=test_request,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ OpenAI兼容性验证成功")
                    print(f"   兼容性: {result.get('is_compatible', 'unknown')}")
                    if result.get('validation', {}).get('warnings'):
                        print(f"   警告: {result['validation']['warnings']}")
                else:
                    print(f"❌ OpenAI兼容性验证失败: {response.status}")
                
            # 测试Anthropic兼容性
            async with session.post(
                f"{url}?provider_name=anthropic",
                json=test_request,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ Anthropic兼容性验证成功")
                    print(f"   兼容性: {result.get('is_compatible', 'unknown')}")
                    if result.get('validation', {}).get('errors'):
                        print(f"   错误: {result['validation']['errors']}")
                else:
                    print(f"❌ Anthropic兼容性验证失败: {response.status}")
                    
        return True
        
    except Exception as e:
        print(f"❌ 验证端点测试失败: {e}")
        return False


async def test_health_endpoint():
    """测试健康检查端点"""
    print("\n❤️  测试健康检查端点...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(
                "http://localhost:8000/health",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ 健康检查通过")
                    print(f"   状态: {result.get('status', 'unknown')}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False


async def test_basic_v1_endpoint():
    """测试基础V1端点作为对比"""
    print("\n🔄 测试基础V1端点...")
    
    test_request = {
        "messages": [{"role": "user", "content": "Hello"}],
        "model": "gpt-4",
        "temperature": 0.7,
        "max_tokens": 50,
        "stream": False
    }
    
    url = "http://localhost:8000/api/v1/text/generate"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=test_request,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                if response.status == 200:
                    print("✅ V1端点正常工作")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ V1端点失败: {response.status} - {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ V1端点测试异常: {e}")
        return False


def print_troubleshooting_tips():
    """打印故障排除提示"""
    print("\n🔧 故障排除提示:")
    print("1. 确保服务器正在运行: python -m ai_gen_hub.api.main")
    print("2. 检查端口是否正确: 默认8000端口")
    print("3. 查看服务器日志获取详细错误信息")
    print("4. 确保所有依赖已安装: pip install -r requirements.txt")
    print("5. 检查环境变量配置")
    print("6. 验证AI供应商API密钥配置")
    
    print("\n📋 有用的命令:")
    print("- 检查服务状态: curl http://localhost:8000/health")
    print("- 查看API文档: http://localhost:8000/docs")
    print("- 检查路由: curl http://localhost:8000/docs | grep v2")


async def main():
    """主测试函数"""
    print("🚀 开始V2 API修复验证\n")
    
    # 测试序列
    tests = [
        ("健康检查", test_health_endpoint),
        ("基础V1端点", test_basic_v1_endpoint),
        ("V2验证端点", test_v2_validate_endpoint),
        ("V2生成端点", test_v2_api_endpoint),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"测试: {test_name}")
        print(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {e}")
            results.append((test_name, False))
        
        print()
    
    # 总结结果
    print(f"{'='*50}")
    print("📊 测试结果总结")
    print(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！V2 API修复成功！")
        return True
    else:
        print("⚠️  部分测试失败，请检查服务器状态和配置")
        print_troubleshooting_tips()
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试脚本异常: {e}")
        sys.exit(1)
