# AI Gen Hub API授权令牌使用指南

## 📋 概述

AI Gen Hub 支持两种主要的API认证方式：
1. **API Key认证** - 简单的密钥认证
2. **JWT Token认证** - 基于JSON Web Token的认证

本指南将详细介绍如何在调试界面中生成、配置和使用API授权令牌。

## 🔧 认证方式说明

### 1. API Key认证

**特点**：
- 简单易用，适合开发和测试
- 长期有效，无需刷新
- 通过请求头或查询参数传递

**使用方式**：
```bash
# 请求头方式
curl -H "X-API-Key: your-api-key" http://localhost:8001/api/v1/text/models

# 查询参数方式
curl "http://localhost:8001/api/v1/text/models?api_key=your-api-key"
```

### 2. JWT Token认证

**特点**：
- 安全性更高，包含用户信息和权限
- 有过期时间，支持自动失效
- 支持不同的权限范围
- 通过Bearer Token方式传递

**使用方式**：
```bash
curl -H "Authorization: Bearer your-jwt-token" http://localhost:8001/api/v1/text/models
```

## 🎯 在调试仪表板中生成Token

### 1. 访问Token管理页面

1. 打开调试仪表板：http://localhost:8001/debug/
2. 点击左侧导航栏的 "Token管理"
3. 或直接访问：http://localhost:8001/debug/tokens

### 2. 生成新的JWT Token

在Token管理页面中：

1. **设置用户ID**：
   - 默认值：`api_user`
   - 可自定义为任意用户标识

2. **选择过期时间**：
   - 1小时（60分钟）
   - 8小时（480分钟）
   - 24小时（1440分钟）- 默认
   - 7天（10080分钟）
   - 30天（43200分钟）
   - 自定义分钟数

3. **选择权限范围**：
   - `api_access` - API访问权限（默认）
   - `admin_access` - 管理员权限
   - `read_only` - 只读权限

4. **点击"生成Token"按钮**

### 3. 复制和使用Token

生成成功后，页面会显示：
- 完整的JWT Token字符串
- Token信息（用户ID、权限范围、创建时间、过期时间）
- 使用示例（Bearer Token、API Key、查询参数）

点击"复制"按钮可快速复制Token到剪贴板。

## 🧪 在API测试页面中使用Token

### 1. 访问API测试页面

访问：http://localhost:8001/debug/api-test

### 2. 配置认证信息

在API测试页面中，有以下认证选项：

#### 方式1：使用Bearer Token
```javascript
// 在请求头中添加
{
  "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

#### 方式2：使用API Key
```javascript
// 在请求头中添加
{
  "X-API-Key": "your-api-key-here"
}
```

#### 方式3：查询参数
```
http://localhost:8001/api/v1/text/models?api_key=your-api-key-here
```

### 3. 测试API端点

1. 选择要测试的API端点
2. 配置认证信息
3. 设置请求参数
4. 发送请求并查看响应

## ⚙️ 手动配置Token

### 1. 配置文件设置

在 `.env` 文件中配置认证相关参数：

```bash
# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# API Key配置
API_KEY=your-api-key-here

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
```

### 2. 配置说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `JWT_SECRET_KEY` | JWT签名密钥，生产环境必须使用强密钥 | `ai-gen-hub-default-secret-key-change-in-production` |
| `JWT_ALGORITHM` | JWT签名算法 | `HS256` |
| `JWT_EXPIRE_MINUTES` | JWT默认过期时间（分钟） | `1440`（24小时） |
| `API_KEY` | API密钥，用于简单认证 | `dev-api-key` |
| `CORS_ORIGINS` | 允许的跨域源，逗号分隔 | 空 |

### 3. 使用命令行工具生成Token

项目提供了命令行工具来生成和验证Token：

```bash
# 生成Token
python generate_token.py generate --user-id api_user --expire-minutes 1440

# 验证Token
python generate_token.py verify --token "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

## 📝 Token有效期和权限范围

### 1. 有效期管理

**默认有效期**：24小时（1440分钟）

**自定义有效期**：
- 最短：1分钟
- 最长：无限制（建议不超过30天）
- 生产环境建议：1-8小时

**过期处理**：
- Token过期后自动失效
- API调用返回401 Unauthorized
- 需要重新生成新Token

### 2. 权限范围

| 权限范围 | 说明 | 适用场景 |
|----------|------|----------|
| `api_access` | 标准API访问权限 | 一般应用调用 |
| `admin_access` | 管理员权限 | 管理操作、配置修改 |
| `read_only` | 只读权限 | 数据查询、状态检查 |

**权限验证**：
- 在API端点中通过中间件验证
- 不同权限范围访问不同的资源
- 权限不足时返回403 Forbidden

## 🚀 完整的curl命令示例

### 1. 使用JWT Token

```bash
# 获取文本生成模型列表
curl -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhcGlfdXNlciIsImlhdCI6MTcwNTEyMzQ1NiwiZXhwIjoxNzA1MjA5ODU2LCJ0eXBlIjoiYWNjZXNzX3Rva2VuIiwic2NvcGUiOiJhcGlfYWNjZXNzIn0.example" \
     http://localhost:8001/api/v1/text/models

# 生成文本
curl -X POST \
     -H "Authorization: Bearer your-jwt-token" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "gpt-4",
       "messages": [
         {"role": "user", "content": "你好，请介绍一下自己"}
       ],
       "max_tokens": 100
     }' \
     http://localhost:8001/api/v1/text/generate

# 生成图像
curl -X POST \
     -H "Authorization: Bearer your-jwt-token" \
     -H "Content-Type: application/json" \
     -d '{
       "prompt": "一只可爱的小猫",
       "model": "dall-e-3",
       "size": "1024x1024"
     }' \
     http://localhost:8001/api/v1/image/generate
```

### 2. 使用API Key

```bash
# 请求头方式
curl -H "X-API-Key: dev-api-key" \
     http://localhost:8001/api/v1/text/models

# 查询参数方式
curl "http://localhost:8001/api/v1/text/models?api_key=dev-api-key"

# POST请求示例
curl -X POST \
     -H "X-API-Key: dev-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "gpt-4",
       "messages": [
         {"role": "user", "content": "Hello"}
       ]
     }' \
     http://localhost:8001/api/v1/text/generate
```

### 3. 调试端点访问

```bash
# 调试端点在开发环境下无需认证
curl http://localhost:8001/debug/api/system/info

# 获取配置信息
curl http://localhost:8001/debug/api/config

# 生成新Token（调试端点）
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "test_user",
       "expire_minutes": 60,
       "scope": "api_access"
     }' \
     http://localhost:8001/debug/api/generate-token

# 验证Token（调试端点）
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{
       "token": "your-jwt-token-here"
     }' \
     http://localhost:8001/debug/api/verify-token
```

## 🔒 安全最佳实践

### 1. 生产环境配置

```bash
# 使用强密钥
JWT_SECRET_KEY=your-very-strong-secret-key-at-least-32-characters-long

# 设置较短的过期时间
JWT_EXPIRE_MINUTES=480  # 8小时

# 使用复杂的API Key
API_KEY=your-complex-api-key-with-random-characters
```

### 2. Token管理建议

- **定期轮换**：定期更新JWT密钥和API Key
- **最小权限**：只授予必要的权限范围
- **监控使用**：记录和监控Token使用情况
- **及时撤销**：发现异常时及时撤销Token

### 3. 开发环境注意事项

- 调试端点在开发环境下无需认证
- 生产环境中调试端点会被禁用
- 不要在生产环境中使用默认密钥

## 🛠️ 故障排除

### 常见错误

1. **401 Unauthorized**
   - 检查Token是否正确
   - 确认Token未过期
   - 验证认证头格式

2. **403 Forbidden**
   - 检查权限范围是否足够
   - 确认用户有访问权限

3. **Token生成失败**
   - 检查JWT密钥配置
   - 确认JWT库已安装
   - 查看应用日志

### 调试方法

1. 使用Token验证端点检查Token状态
2. 查看应用日志获取详细错误信息
3. 使用调试仪表板的配置页面检查设置

---

**更新时间**：2025-08-13  
**版本**：v1.0.0  
**适用于**：AI Gen Hub v0.1.0+
