# AI Gen Hub 调试仪表板

## 概述

AI Gen Hub 调试仪表板是一个功能强大的开发和调试工具，为开发者提供了全面的系统监控、API测试、日志查看、配置管理等功能。该仪表板仅在开发和测试环境中可用，确保生产环境的安全性。

## 主要功能

### 1. 系统状态监控

实时监控系统资源使用情况，包括：

- **CPU使用率**: 实时显示CPU使用百分比和各核心负载
- **内存使用率**: 显示内存使用情况、可用内存和缓存信息
- **磁盘使用率**: 监控磁盘空间使用和I/O统计
- **网络状态**: 显示网络连接数和流量统计
- **进程监控**: 列出系统进程，支持按CPU/内存排序

#### 访问路径
```
GET /debug/system
```

#### API端点
```
GET /debug/api/system/info          # 获取基础系统信息
GET /debug/api/system/detailed      # 获取详细系统信息
GET /debug/api/system/processes     # 获取进程列表
```

### 2. API接口测试

交互式API测试工具，提供：

- **端点发现**: 自动列出所有可用的API端点
- **参数输入**: 提供友好的表单界面输入请求参数
- **实时测试**: 发送请求并显示详细的响应信息
- **历史记录**: 保存测试历史，支持重复执行
- **响应分析**: 显示状态码、响应时间、响应头等详细信息

#### 访问路径
```
GET /debug/api-test
```

#### API端点
```
GET /debug/api/endpoints            # 获取所有端点列表
GET /debug/api/endpoints/detailed   # 获取详细端点信息
POST /debug/api/test-endpoint       # 执行API测试
```

### 3. 日志查看器

实时日志监控和分析工具：

- **实时日志流**: 实时显示应用程序日志
- **级别过滤**: 支持按DEBUG、INFO、WARNING、ERROR、CRITICAL过滤
- **关键词搜索**: 支持全文搜索日志内容
- **时间范围**: 支持按时间范围筛选日志
- **统计分析**: 提供日志级别分布和时间趋势图表
- **导出功能**: 支持导出日志为CSV格式

#### 访问路径
```
GET /debug/logs
```

#### API端点
```
GET /debug/api/logs                 # 获取日志列表
GET /debug/api/logs/levels          # 获取日志级别信息
GET /debug/api/logs/stats           # 获取日志统计信息
```

### 4. 配置信息展示

安全地展示应用配置信息：

- **应用配置**: 显示基本应用设置
- **数据库配置**: 显示数据库连接信息（脱敏处理）
- **缓存配置**: 显示Redis等缓存配置
- **供应商配置**: 显示AI供应商配置信息
- **环境变量**: 列出相关环境变量
- **运行时信息**: 显示Python和系统信息
- **安全脱敏**: 自动隐藏密码、密钥等敏感信息

#### 访问路径
```
GET /debug/config
```

#### API端点
```
GET /debug/api/config               # 获取应用配置
GET /debug/api/config/environment   # 获取环境变量
GET /debug/api/config/runtime       # 获取运行时信息
```

### 5. 性能指标

应用性能监控和分析：

- **响应时间**: 监控API响应时间趋势
- **请求频率**: 统计每分钟请求数
- **错误率**: 计算和显示错误率
- **状态码分布**: 显示HTTP状态码分布
- **端点性能**: 按端点统计性能指标
- **缓存性能**: 显示缓存命中率和性能
- **数据库性能**: 监控数据库查询性能

#### 访问路径
```
GET /debug/metrics
```

### 6. 开发工具

提供各种开发和维护工具：

- **缓存管理**: 清空缓存、清理过期缓存
- **配置管理**: 重新加载配置、验证配置、导出配置
- **数据库工具**: 测试连接、检查迁移、优化数据库
- **监控工具**: 重置指标、生成报告、导出日志
- **系统维护**: 健康检查、清理临时文件、重启服务
- **操作日志**: 记录所有操作历史

#### 访问路径
```
GET /debug/tools
```

## 安全特性

### 1. 环境控制

调试仪表板仅在以下条件下可用：
- 环境变量 `environment` 不等于 `production`
- 配置项 `debug` 设置为 `True`

```python
def check_debug_access(request: Request) -> bool:
    settings = getattr(request.app.state, "settings", None)
    
    if settings.environment.lower() == "production":
        raise HTTPException(status_code=403, detail="调试页面在生产环境中不可用")
    
    if not settings.debug:
        raise HTTPException(status_code=403, detail="调试页面需要启用调试模式")
    
    return True
```

### 2. 数据脱敏

自动识别并脱敏敏感信息：

```python
def mask_sensitive_value(key: str, value: Any) -> Any:
    sensitive_keys = [
        'password', 'secret', 'key', 'token', 'api_key', 
        'private_key', 'auth', 'credential'
    ]
    
    if any(sensitive in key.lower() for sensitive in sensitive_keys):
        if isinstance(value, str) and value:
            return "***" + value[-4:] if len(value) > 4 else "***"
    
    return value
```

### 3. 访问控制

支持基于依赖注入的访问控制，可以轻松集成认证和授权机制。

## 技术实现

### 后端技术栈

- **FastAPI**: 高性能Web框架，提供自动API文档
- **Pydantic**: 数据验证和序列化
- **psutil**: 系统信息获取和进程监控
- **Jinja2**: 模板引擎，渲染HTML页面
- **structlog**: 结构化日志记录

### 前端技术栈

- **Bootstrap 5**: 响应式UI框架
- **Chart.js**: 数据可视化图表库
- **Font Awesome**: 图标库
- **原生JavaScript**: 无额外框架依赖

### 架构设计

```
src/ai_gen_hub/
├── api/routers/
│   └── debug.py              # 调试API路由
├── templates/debug/
│   ├── base.html            # 基础模板
│   ├── dashboard.html       # 仪表板主页
│   ├── system.html          # 系统监控页面
│   ├── api_test.html        # API测试页面
│   ├── logs.html            # 日志查看页面
│   ├── config.html          # 配置信息页面
│   ├── metrics.html         # 性能指标页面
│   └── tools.html           # 开发工具页面
```

## 部署和配置

### 1. 安装依赖

```bash
pip install jinja2 psutil
```

### 2. 集成到FastAPI应用

```python
from ai_gen_hub.api.routers.debug import router as debug_router

# 仅在非生产环境添加调试路由
if settings.debug or settings.environment.lower() != "production":
    app.include_router(
        debug_router,
        prefix="/debug",
        tags=["调试工具"]
    )
```

### 3. 配置模板目录

```python
from fastapi.templating import Jinja2Templates

templates = Jinja2Templates(directory="src/ai_gen_hub/templates")
```

### 4. 环境变量配置

```bash
# 启用调试模式
DEBUG=true
ENVIRONMENT=development

# 禁用调试模式（生产环境）
DEBUG=false
ENVIRONMENT=production
```

## 使用指南

### 1. 访问调试仪表板

启动应用后，访问以下URL：

```
http://localhost:8000/debug/
```

### 2. 系统监控

- 查看实时系统资源使用情况
- 监控进程列表和资源占用
- 分析系统性能趋势

### 3. API测试

- 从左侧端点列表选择要测试的API
- 填写请求参数和请求体
- 点击"执行测试"查看结果
- 查看测试历史记录

### 4. 日志分析

- 使用过滤器筛选日志级别
- 搜索特定关键词
- 查看日志统计图表
- 导出日志文件

### 5. 配置检查

- 查看当前应用配置
- 检查环境变量设置
- 验证配置有效性

### 6. 性能分析

- 监控API响应时间
- 分析错误率趋势
- 查看端点性能排行

### 7. 开发工具

- 清理缓存提高性能
- 重新加载配置文件
- 执行数据库维护操作
- 生成系统健康报告

## 故障排除

### 1. 无法访问调试页面

检查以下配置：
- 确保 `debug=True`
- 确保 `environment != "production"`
- 检查路由是否正确添加

### 2. 系统信息显示异常

确保安装了 `psutil` 库：
```bash
pip install psutil
```

### 3. 模板加载失败

检查模板目录路径是否正确：
```python
templates = Jinja2Templates(directory="src/ai_gen_hub/templates")
```

## 最佳实践

### 1. 安全建议

- 永远不要在生产环境启用调试页面
- 定期检查敏感信息脱敏是否正确
- 考虑添加额外的认证机制

### 2. 性能建议

- 避免在高负载时频繁刷新系统监控
- 合理设置日志查看的数量限制
- 定期清理操作日志

### 3. 维护建议

- 定期更新依赖库版本
- 监控调试页面的访问日志
- 备份重要的配置和日志文件

## 扩展开发

### 1. 添加新的监控指标

```python
@router.get("/api/custom/metrics")
async def get_custom_metrics():
    # 实现自定义指标收集
    return {"custom_metric": "value"}
```

### 2. 集成外部监控系统

可以将调试页面的数据导出到Prometheus、Grafana等监控系统。

### 3. 添加告警功能

基于阈值设置，当系统指标异常时发送告警通知。

## 许可证

本项目遵循MIT许可证。详见LICENSE文件。
