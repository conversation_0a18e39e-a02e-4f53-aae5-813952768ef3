# 阿里云百炼 DashScope Provider 实现说明

## 概述

DashScope Provider 是基于阿里云百炼 DashScope API 规范的完整适配器实现，支持通义千问系列模型的文本生成功能。本实现严格遵循官方 API 文档，提供了完整的功能支持和错误处理。

## 主要功能

### 1. 模型支持

#### 支持的模型系列

**通义千问最新系列（推荐使用）**
- `qwen-max`: 最强性能模型，适合复杂推理任务
- `qwen-plus`: 平衡性能和成本的最佳选择
- `qwen-turbo`: 快速响应模型，适合实时对话

**Qwen3 系列（最新一代）**
- `qwen3-235b-a22b`: 超大规模模型，顶级性能
- `qwen3-32b`: 大规模模型，高质量输出
- `qwen3-8b`: 中等规模模型，平衡性能

**Qwen2.5 系列（稳定版本）**
- `qwen2.5-72b-instruct`: 大规模指令模型
- `qwen2.5-32b-instruct`: 中大规模指令模型
- `qwen2.5-14b-instruct`: 中等规模指令模型
- `qwen2.5-7b-instruct`: 小规模指令模型
- `qwen2.5-3b-instruct`: 轻量级指令模型

**Qwen2.5-Coder 系列（代码专用）**
- `qwen2.5-coder-32b-instruct`: 大规模代码模型
- `qwen2.5-coder-14b-instruct`: 中等规模代码模型
- `qwen2.5-coder-7b-instruct`: 小规模代码模型

#### 模型映射和别名

实现了智能的模型名称映射，支持向后兼容和便捷别名：

```python
# 旧版本模型自动映射
"qwen-max-1201" → "qwen-max"
"qwen-plus-0919" → "qwen-plus"

# 便捷别名
"qwen-latest" → "qwen-max"
"qwen-fast" → "qwen-turbo"
"qwen-balanced" → "qwen-plus"
"qwen-coder" → "qwen2.5-coder-32b-instruct"
```

### 2. 核心功能

#### 文本生成
- **非流式生成**: 一次性返回完整响应
- **流式生成**: 实时流式输出，支持 SSE 格式
- **多轮对话**: 支持完整的对话历史
- **系统指令**: 支持系统消息设置

#### 参数支持
- **基础参数**: `max_tokens`, `temperature`, `top_p`
- **控制参数**: `frequency_penalty`, `stop`, `seed`
- **DashScope 特有**: `enable_search`, `incremental_output`

### 3. API 认证和配置

#### 认证方式
```python
# 使用 Bearer Token 认证
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}
```

#### API 端点
- **基础 URL**: `https://dashscope.aliyuncs.com/api/v1`
- **文本生成**: `/services/aigc/text-generation/generation`
- **流式响应**: 添加 `X-DashScope-SSE: enable` 头部

## 使用方法

### 基础文本生成

```python
from ai_gen_hub.providers.dashscope_provider import DashScopeProvider
from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole

# 创建请求
request = TextGenerationRequest(
    model="qwen-max",
    messages=[
        Message(role=MessageRole.SYSTEM, content="你是一个有用的助手。"),
        Message(role=MessageRole.USER, content="介绍一下通义千问模型的特点。")
    ],
    max_tokens=1000,
    temperature=0.7
)

# 生成响应
response = await provider.generate_text(request)
print(response.choices[0].message.content)
```

### 流式生成

```python
request = TextGenerationRequest(
    model="qwen-plus",
    messages=[
        Message(role=MessageRole.USER, content="写一个关于人工智能的故事。")
    ],
    stream=True,
    max_tokens=2000
)

async for chunk in await provider.generate_text(request):
    if chunk.choices and chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
```

### 代码生成

```python
request = TextGenerationRequest(
    model="qwen2.5-coder-32b-instruct",
    messages=[
        Message(role=MessageRole.SYSTEM, content="你是一个专业的编程助手。"),
        Message(role=MessageRole.USER, content="用 Python 实现一个快速排序算法。")
    ],
    max_tokens=1500,
    temperature=0.2  # 代码生成使用较低的温度
)

response = await provider.generate_text(request)
```

### 多轮对话

```python
# 初始化对话历史
conversation = [
    Message(role=MessageRole.SYSTEM, content="你是一个友好的助手。"),
    Message(role=MessageRole.USER, content="你好！")
]

# 第一轮对话
request = TextGenerationRequest(
    model="qwen-turbo",
    messages=conversation,
    max_tokens=500
)

response = await provider.generate_text(request)
conversation.append(response.choices[0].message)

# 继续对话
conversation.append(Message(role=MessageRole.USER, content="能帮我解释一下机器学习吗？"))

request = TextGenerationRequest(
    model="qwen-turbo",
    messages=conversation,
    max_tokens=1000
)

response = await provider.generate_text(request)
```

### 高级参数配置

```python
request = TextGenerationRequest(
    model="qwen-max",
    messages=[
        Message(role=MessageRole.USER, content="创作一首诗")
    ],
    max_tokens=800,
    temperature=0.8,      # 创意任务使用较高温度
    top_p=0.9,           # 核采样参数
    frequency_penalty=0.1, # 减少重复
    stop=["。", "！", "？"], # 停止词
    provider_params={
        "enable_search": False  # 禁用搜索功能
    }
)
```

## 错误处理

### 支持的错误类型

1. **认证错误** (InvalidApiKey): API 密钥无效
2. **速率限制** (RateLimitExceeded): 请求过于频繁
3. **配额超限** (QuotaExceeded): 使用量超过限制
4. **模型错误** (ModelNotFound): 模型名称无效
5. **参数错误** (InvalidParameter): 请求参数无效

### 错误处理示例

```python
from ai_gen_hub.core.exceptions import (
    APIError, 
    AuthenticationError, 
    RateLimitError,
    QuotaExceededError
)

try:
    response = await provider.generate_text(request)
except AuthenticationError as e:
    print(f"认证失败: {e}")
except RateLimitError as e:
    print(f"速率限制: {e}")
except QuotaExceededError as e:
    print(f"配额超限: {e}")
except APIError as e:
    print(f"API 错误: {e}")
```

## 配置选项

### 基础配置

```python
from ai_gen_hub.config.settings import ProviderConfig

config = ProviderConfig(
    name="dashscope",
    api_key="sk-your-dashscope-key",
    timeout=30.0,           # 请求超时时间
    max_retries=3,          # 最大重试次数
    retry_delay=1.0         # 重试延迟
)
```

### 环境变量配置

```bash
# 设置 DashScope API 密钥
export DASHSCOPE_API_KEY="sk-your-dashscope-key"

# 可选：设置其他配置
export DASHSCOPE_TIMEOUT=30
export DASHSCOPE_MAX_RETRIES=3
```

## 性能优化

### 模型选择建议

1. **高质量任务**: 使用 `qwen-max` 或 `qwen3-235b-a22b`
2. **平衡性能**: 使用 `qwen-plus`（推荐）
3. **快速响应**: 使用 `qwen-turbo`
4. **代码生成**: 使用 `qwen2.5-coder` 系列
5. **轻量级任务**: 使用 `qwen2.5-7b-instruct` 或更小模型

### 最佳实践

1. **流式输出**: 对于长文本生成，使用流式输出提升用户体验
2. **温度设置**: 创意任务使用 0.7-0.9，事实性任务使用 0.1-0.3
3. **停止词**: 合理设置停止词，控制输出长度和格式
4. **重试机制**: 利用内置的重试机制处理临时网络问题

## 注意事项

1. **API 密钥安全**: 不要在代码中硬编码 API 密钥，使用环境变量
2. **速率限制**: 注意 API 的速率限制，避免过于频繁的请求
3. **模型选择**: 根据任务需求选择合适的模型，平衡性能和成本
4. **错误处理**: 实现完善的错误处理机制，提升应用稳定性
5. **流式响应**: 流式响应需要正确处理 SSE 格式的数据

## 更新日志

- **2025-08-13**: 基于最新 DashScope API 规范完成实现
- 支持通义千问全系列模型
- 实现完整的流式和非流式文本生成
- 完善错误处理和模型映射
- 添加全面的单元测试覆盖

## 技术支持

如遇到问题，请检查：
1. API 密钥是否正确配置
2. 网络连接是否正常
3. 模型名称是否支持
4. 请求参数是否合法
5. 是否超出速率限制或配额
