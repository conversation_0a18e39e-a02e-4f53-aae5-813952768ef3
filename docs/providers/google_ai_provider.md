# Google AI Provider 实现说明

## 概述

Google AI Provider 是基于最新 Gemini API 规范的完整适配器实现，支持 Google 最新的 Gemini 2.5 和 2.0 系列模型。本实现严格遵循官方 API 文档，提供了完整的功能支持和错误处理。

## 主要功能

### 1. 模型支持

#### 支持的模型系列
- **Gemini 2.5 系列**（最新推荐）
  - `gemini-2.5-pro`: 最先进的推理和编码能力
  - `gemini-2.5-flash`: 平衡性能和成本的最佳选择
  - `gemini-2.5-flash-lite`: 成本效益最高的轻量级模型

- **Gemini 2.0 系列**（下一代功能）
  - `gemini-2.0-flash`: 超快速度和原生工具使用
  - `gemini-2.0-flash-lite`: 2.0 系列的轻量级版本

- **Gemini 1.5 系列**（向后兼容，将于 2025 年 9 月弃用）
  - `gemini-1.5-pro`: 复杂推理任务
  - `gemini-1.5-flash`: 快速多用途模型
  - `gemini-1.5-flash-8b`: 高吞吐量低智能任务

#### 模型映射
实现了智能的模型名称映射，自动将旧模型名称映射到最新的对应模型：

```python
# 旧模型名称自动映射
"gemini-pro" → "gemini-2.5-pro"
"gemini-pro-vision" → "gemini-2.5-pro"
"gemini-1.0-pro" → "gemini-2.5-pro"

# 便捷别名
"gemini-latest" → "gemini-2.5-flash"
"gemini-fast" → "gemini-2.5-flash"
"gemini-lite" → "gemini-2.5-flash-lite"
```

### 2. 核心功能

#### 文本生成
- **非流式生成**: 一次性返回完整响应
- **流式生成**: 实时流式输出，支持 SSE 格式
- **多轮对话**: 支持完整的对话历史
- **系统指令**: 正确使用 `system_instruction` 字段

#### 多模态支持
- **文本输入**: 标准文本消息
- **图像输入**: 支持图像理解和分析
- **图像生成**: 基于 Gemini 2.0 Flash 的图像生成能力

#### Thinking 功能（Gemini 2.5 专有）
- **思考过程**: 支持模型的内部思考过程
- **思考预算**: 可配置的思考 token 预算
- **思考输出**: 在响应中包含思考内容

### 3. 高级功能

#### 安全设置
```python
# 默认安全设置
safety_settings = [
    {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    },
    {
        "category": "HARM_CATEGORY_HATE_SPEECH", 
        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    },
    # ... 其他类别
]
```

#### 结构化输出
- **JSON Schema**: 支持 JSON Schema 约束
- **响应格式**: 可指定 JSON、文本或枚举格式
- **格式验证**: 自动验证 Schema 格式

#### 工具集成
- **函数调用**: 支持自定义函数调用
- **Google Search**: 内置 Google 搜索工具
- **代码执行**: 支持代码执行工具
- **智能启用**: 根据消息内容自动启用相关工具

## 使用方法

### 基础文本生成

```python
from ai_gen_hub.providers.google_ai_provider import GoogleAIProvider
from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole

# 创建请求
request = TextGenerationRequest(
    model="gemini-2.5-flash",
    messages=[
        Message(role=MessageRole.SYSTEM, content="你是一个有用的助手。"),
        Message(role=MessageRole.USER, content="介绍一下人工智能的发展历史。")
    ],
    max_tokens=1000,
    temperature=0.7
)

# 生成响应
response = await provider.generate_text(request)
print(response.choices[0].message.content)
```

### 流式生成

```python
request = TextGenerationRequest(
    model="gemini-2.5-flash",
    messages=[
        Message(role=MessageRole.USER, content="写一个关于未来科技的故事。")
    ],
    stream=True
)

async for chunk in await provider.generate_text(request):
    if chunk.choices and chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
```

### Thinking 功能

```python
request = TextGenerationRequest(
    model="gemini-2.5-pro",  # 必须是 2.5 系列模型
    messages=[
        Message(role=MessageRole.USER, content="解决这个复杂的数学问题：...")
    ],
    thinking_budget=2000  # 设置思考预算
)

response = await provider.generate_text(request)
# 响应中会包含模型的思考过程
```

### 结构化输出

```python
# 定义 JSON Schema
schema = {
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "age": {"type": "integer"},
        "skills": {
            "type": "array",
            "items": {"type": "string"}
        }
    },
    "required": ["name", "age"]
}

request = TextGenerationRequest(
    model="gemini-2.5-flash",
    messages=[
        Message(role=MessageRole.USER, content="生成一个程序员的个人信息")
    ],
    response_format={"type": "json_object"},
    response_schema=schema
)
```

### 工具使用

```python
# 自定义函数
functions = [
    {
        "name": "get_weather",
        "description": "获取指定城市的天气信息",
        "parameters": {
            "type": "object",
            "properties": {
                "city": {"type": "string", "description": "城市名称"}
            },
            "required": ["city"]
        }
    }
]

request = TextGenerationRequest(
    model="gemini-2.5-flash",
    messages=[
        Message(role=MessageRole.USER, content="北京今天天气怎么样？")
    ],
    functions=functions,
    function_call="auto"
)
```

### 图像生成

```python
from ai_gen_hub.core.interfaces import ImageGenerationRequest

request = ImageGenerationRequest(
    prompt="一只在花园里玩耍的可爱小猫",
    model="gemini-2.0-flash",
    response_modalities=["TEXT", "IMAGE"]
)

response = await provider.generate_image(request)
```

## 错误处理

### 支持的错误类型

1. **认证错误** (401): API 密钥无效
2. **授权错误** (403): 权限不足
3. **速率限制** (429): 请求过于频繁
4. **配额超限** (429): 使用量超过限制
5. **模型错误** (400): 模型名称无效或不支持
6. **内容过滤** (400): 内容被安全过滤器阻止

### 错误处理示例

```python
from ai_gen_hub.core.exceptions import (
    APIError, 
    AuthenticationError, 
    RateLimitError,
    QuotaExceededError
)

try:
    response = await provider.generate_text(request)
except AuthenticationError as e:
    print(f"认证失败: {e}")
except RateLimitError as e:
    print(f"速率限制: {e}, 重试时间: {e.retry_after}")
except QuotaExceededError as e:
    print(f"配额超限: {e}")
except APIError as e:
    print(f"API 错误: {e}")
```

## 配置选项

### 基础配置

```python
from ai_gen_hub.config.settings import ProviderConfig

config = ProviderConfig(
    name="google_ai",
    api_key="your-api-key",
    timeout=30.0,           # 请求超时时间
    max_retries=3,          # 最大重试次数
    retry_delay=1.0         # 重试延迟
)
```

### 高级配置

```python
# 自定义安全设置
request.safety_settings = [
    {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_ONLY_HIGH"  # 更宽松的过滤
    }
]

# 启用特定工具
request.provider_params = {
    "enable_google_search": True,
    "enable_code_execution": True
}
```

## 性能优化

### 模型选择建议

1. **高质量任务**: 使用 `gemini-2.5-pro`
2. **平衡性能**: 使用 `gemini-2.5-flash`（推荐）
3. **高吞吐量**: 使用 `gemini-2.5-flash-lite`
4. **图像生成**: 使用 `gemini-2.0-flash`

### 最佳实践

1. **流式输出**: 对于长文本生成，使用流式输出提升用户体验
2. **思考预算**: 合理设置 thinking_budget，避免过度消耗
3. **安全设置**: 根据应用场景调整安全过滤级别
4. **工具使用**: 只在需要时启用工具，避免不必要的开销

## 注意事项

1. **API 版本**: 使用 v1beta 版本以支持最新功能
2. **模型弃用**: Gemini 1.5 系列将于 2025 年 9 月弃用
3. **Thinking 功能**: 仅 Gemini 2.5 系列支持
4. **图像生成**: 目前仅 Gemini 2.0 Flash 支持
5. **速率限制**: 注意 API 的速率限制和配额限制

## 更新日志

- **2025-08-13**: 基于最新 Gemini API 规范完成实现
- 支持 Gemini 2.5 和 2.0 系列模型
- 实现 Thinking 功能
- 完善错误处理和流式响应
- 添加图像生成支持
