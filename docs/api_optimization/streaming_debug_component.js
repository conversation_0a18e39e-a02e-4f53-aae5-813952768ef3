/**
 * 流式输出调试组件
 * 
 * 可以集成到现有的调试界面中，提供流式输出的实时显示和测试功能
 */

class StreamingDebugComponent {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            baseUrl: options.baseUrl || '',
            autoScroll: options.autoScroll !== false,
            showRawData: options.showRawData !== false,
            maxChunks: options.maxChunks || 1000,
            ...options
        };
        
        this.eventSource = null;
        this.startTime = null;
        this.stats = {
            chunks: 0,
            characters: 0,
            errors: 0
        };
        this.chunks = [];
        this.isStreaming = false;
        
        this.init();
    }
    
    init() {
        this.render();
        this.bindEvents();
    }
    
    render() {
        this.container.innerHTML = `
            <div class="streaming-debug-component">
                <!-- 控制面板 -->
                <div class="streaming-controls mb-3">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-comment"></i>
                                </span>
                                <input type="text" class="form-control" id="quick-message" 
                                       placeholder="快速测试消息..." 
                                       value="请写一首关于春天的短诗">
                                <button class="btn btn-primary" id="quick-start" type="button">
                                    <i class="fas fa-play"></i> 快速开始
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group w-100" role="group">
                                <button class="btn btn-outline-secondary" id="clear-output">
                                    <i class="fas fa-eraser"></i> 清空
                                </button>
                                <button class="btn btn-outline-info" id="toggle-raw">
                                    <i class="fas fa-code"></i> 原始数据
                                </button>
                                <button class="btn btn-outline-warning" id="export-data">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 状态栏 -->
                <div class="streaming-status mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <span class="status-indicator me-2" id="connection-status"></span>
                                <span id="status-text">就绪</span>
                                <button class="btn btn-sm btn-outline-danger ms-2" id="stop-stream" disabled>
                                    <i class="fas fa-stop"></i> 停止
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="streaming-stats d-flex justify-content-end gap-3">
                                <small>块数: <span id="chunk-count">0</span></small>
                                <small>字符: <span id="char-count">0</span></small>
                                <small>耗时: <span id="elapsed-time">0.00s</span></small>
                                <small>速度: <span id="speed">0</span> 字符/秒</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 输出区域 -->
                <div class="streaming-output-container">
                    <div class="streaming-output" id="streaming-output">
                        <div class="text-muted text-center p-4">
                            <i class="fas fa-info-circle"></i>
                            <p>点击"快速开始"按钮开始流式测试</p>
                        </div>
                    </div>
                </div>
                
                <!-- 原始数据面板（默认隐藏） -->
                <div class="raw-data-panel mt-3" id="raw-data-panel" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-code"></i> 原始数据块
                                <button class="btn btn-sm btn-outline-secondary float-end" id="copy-raw">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </h6>
                        </div>
                        <div class="card-body">
                            <pre id="raw-data-content" class="bg-light p-2 rounded" 
                                 style="max-height: 300px; overflow-y: auto; font-size: 12px;"></pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.addStyles();
    }
    
    addStyles() {
        if (document.getElementById('streaming-debug-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'streaming-debug-styles';
        styles.textContent = `
            .streaming-debug-component {
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                padding: 15px;
                background-color: #f8f9fa;
            }
            
            .streaming-output-container {
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                background-color: white;
            }
            
            .streaming-output {
                height: 300px;
                overflow-y: auto;
                padding: 15px;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                line-height: 1.4;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            
            .streaming-output.dark-theme {
                background-color: #1a1a1a;
                color: #00ff00;
            }
            
            .chunk-marker {
                display: inline-block;
                background-color: #e3f2fd;
                color: #1976d2;
                padding: 1px 4px;
                border-radius: 3px;
                font-size: 10px;
                margin-right: 2px;
                vertical-align: super;
            }
            
            .error-marker {
                background-color: #ffebee;
                color: #d32f2f;
            }
            
            .status-indicator {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                display: inline-block;
            }
            
            .status-ready { background-color: #6c757d; }
            .status-connecting { background-color: #ffc107; }
            .status-streaming { background-color: #28a745; }
            .status-error { background-color: #dc3545; }
            .status-stopped { background-color: #fd7e14; }
            
            .streaming-stats small {
                font-family: monospace;
            }
        `;
        
        document.head.appendChild(styles);
    }
    
    bindEvents() {
        // 快速开始按钮
        document.getElementById('quick-start').addEventListener('click', () => {
            const message = document.getElementById('quick-message').value.trim();
            if (message) {
                this.startQuickTest(message);
            }
        });
        
        // 停止按钮
        document.getElementById('stop-stream').addEventListener('click', () => {
            this.stopStreaming();
        });
        
        // 清空输出
        document.getElementById('clear-output').addEventListener('click', () => {
            this.clearOutput();
        });
        
        // 切换原始数据显示
        document.getElementById('toggle-raw').addEventListener('click', () => {
            this.toggleRawData();
        });
        
        // 导出数据
        document.getElementById('export-data').addEventListener('click', () => {
            this.exportData();
        });
        
        // 复制原始数据
        document.getElementById('copy-raw').addEventListener('click', () => {
            this.copyRawData();
        });
        
        // 快速消息回车键
        document.getElementById('quick-message').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('quick-start').click();
            }
        });
    }
    
    async startQuickTest(message) {
        const requestData = {
            model: 'gpt-4',
            messages: [
                { role: 'user', content: message }
            ],
            stream: { enabled: true },
            generation: {
                max_tokens: 500,
                temperature: 0.7
            }
        };
        
        await this.startStreaming(requestData);
    }
    
    async startStreaming(requestData) {
        if (this.isStreaming) {
            this.stopStreaming();
        }
        
        this.isStreaming = true;
        this.startTime = Date.now();
        this.stats = { chunks: 0, characters: 0, errors: 0 };
        this.chunks = [];
        
        this.updateStatus('connecting', '连接中...');
        this.updateButtons(true);
        
        try {
            const response = await fetch(`${this.options.baseUrl}/api/v1/text/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream'
                },
                body: JSON.stringify(requestData)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            this.updateStatus('streaming', '流式传输中...');
            this.appendOutput(`🚀 开始生成: ${requestData.messages[requestData.messages.length - 1].content}\n\n`, 'info');
            
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            
            while (this.isStreaming) {
                const { done, value } = await reader.read();
                
                if (done) break;
                
                const chunk = decoder.decode(value, { stream: true });
                await this.processChunk(chunk);
            }
            
        } catch (error) {
            console.error('流式请求失败:', error);
            this.updateStatus('error', `错误: ${error.message}`);
            this.appendOutput(`❌ 错误: ${error.message}\n`, 'error');
            this.stats.errors++;
        } finally {
            this.stopStreaming();
        }
    }
    
    async processChunk(chunk) {
        const lines = chunk.split('\n');
        
        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const data = line.slice(6);
                
                if (data.trim() === '[DONE]') {
                    this.appendOutput('\n\n✅ 生成完成', 'done');
                    this.stopStreaming();
                    return;
                }
                
                try {
                    const parsed = JSON.parse(data);
                    this.chunks.push(parsed);
                    
                    if (parsed.choices && parsed.choices[0]) {
                        const choice = parsed.choices[0];
                        const delta = choice.delta || {};
                        
                        if (delta.content) {
                            this.appendOutput(delta.content, 'content');
                            this.stats.chunks++;
                            this.stats.characters += delta.content.length;
                        }
                        
                        if (choice.finish_reason) {
                            this.appendOutput(`\n\n🏁 完成原因: ${choice.finish_reason}`, 'info');
                        }
                    }
                    
                    this.updateStats();
                    this.updateRawData();
                    
                } catch (e) {
                    console.warn('解析数据块失败:', e, data);
                    this.stats.errors++;
                }
            }
        }
    }
    
    appendOutput(content, type = 'content') {
        const output = document.getElementById('streaming-output');
        
        if (type === 'content') {
            // 添加块标记
            if (this.stats.chunks > 0 && this.stats.chunks % 10 === 0) {
                const marker = document.createElement('span');
                marker.className = 'chunk-marker';
                marker.textContent = this.stats.chunks;
                output.appendChild(marker);
            }
            
            const span = document.createElement('span');
            span.textContent = content;
            output.appendChild(span);
        } else {
            const div = document.createElement('div');
            div.className = `output-${type}`;
            div.textContent = content;
            
            if (type === 'error') {
                div.style.color = '#dc3545';
                div.style.fontWeight = 'bold';
            } else if (type === 'info') {
                div.style.color = '#6c757d';
                div.style.fontStyle = 'italic';
            } else if (type === 'done') {
                div.style.color = '#28a745';
                div.style.fontWeight = 'bold';
            }
            
            output.appendChild(div);
        }
        
        if (this.options.autoScroll) {
            output.scrollTop = output.scrollHeight;
        }
    }
    
    updateStatus(status, text) {
        const indicator = document.getElementById('connection-status');
        const statusText = document.getElementById('status-text');
        
        indicator.className = `status-indicator status-${status}`;
        statusText.textContent = text;
    }
    
    updateStats() {
        const elapsed = this.startTime ? (Date.now() - this.startTime) / 1000 : 0;
        const speed = elapsed > 0 ? (this.stats.characters / elapsed).toFixed(1) : 0;
        
        document.getElementById('chunk-count').textContent = this.stats.chunks;
        document.getElementById('char-count').textContent = this.stats.characters;
        document.getElementById('elapsed-time').textContent = elapsed.toFixed(2) + 's';
        document.getElementById('speed').textContent = speed;
    }
    
    updateButtons(streaming) {
        document.getElementById('quick-start').disabled = streaming;
        document.getElementById('stop-stream').disabled = !streaming;
    }
    
    updateRawData() {
        const rawDataContent = document.getElementById('raw-data-content');
        if (rawDataContent && this.chunks.length > 0) {
            const lastChunks = this.chunks.slice(-5); // 只显示最后5个块
            rawDataContent.textContent = JSON.stringify(lastChunks, null, 2);
        }
    }
    
    stopStreaming() {
        this.isStreaming = false;
        this.updateStatus('stopped', '已停止');
        this.updateButtons(false);
    }
    
    clearOutput() {
        document.getElementById('streaming-output').innerHTML = `
            <div class="text-muted text-center p-4">
                <i class="fas fa-info-circle"></i>
                <p>点击"快速开始"按钮开始流式测试</p>
            </div>
        `;
        
        this.stats = { chunks: 0, characters: 0, errors: 0 };
        this.chunks = [];
        this.updateStats();
        this.updateRawData();
    }
    
    toggleRawData() {
        const panel = document.getElementById('raw-data-panel');
        const button = document.getElementById('toggle-raw');
        
        if (panel.style.display === 'none') {
            panel.style.display = 'block';
            button.innerHTML = '<i class="fas fa-eye-slash"></i> 隐藏原始数据';
            this.updateRawData();
        } else {
            panel.style.display = 'none';
            button.innerHTML = '<i class="fas fa-code"></i> 原始数据';
        }
    }
    
    exportData() {
        const data = {
            timestamp: new Date().toISOString(),
            stats: this.stats,
            chunks: this.chunks,
            output: document.getElementById('streaming-output').textContent
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `streaming-debug-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
    }
    
    copyRawData() {
        const content = document.getElementById('raw-data-content').textContent;
        navigator.clipboard.writeText(content).then(() => {
            const button = document.getElementById('copy-raw');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> 已复制';
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        });
    }
    
    // 公共方法：自定义流式测试
    async customStream(requestData) {
        await this.startStreaming(requestData);
    }
    
    // 公共方法：获取统计信息
    getStats() {
        return { ...this.stats };
    }
    
    // 公共方法：获取生成的内容
    getGeneratedContent() {
        const output = document.getElementById('streaming-output');
        return output.textContent.replace(/^\s*点击.*$/, '').trim();
    }
}

// 使用示例
/*
// 在页面中创建流式调试组件
const streamingDebug = new StreamingDebugComponent('streaming-container', {
    baseUrl: 'http://localhost:8000',
    autoScroll: true,
    showRawData: true
});

// 自定义测试
streamingDebug.customStream({
    model: 'gpt-4',
    messages: [
        { role: 'system', content: '你是一个有用的助手。' },
        { role: 'user', content: '请解释量子计算的基本原理。' }
    ],
    stream: { enabled: true },
    generation: {
        max_tokens: 800,
        temperature: 0.8
    }
});
*/
