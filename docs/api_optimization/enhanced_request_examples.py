"""
优化版本文本生成请求的使用示例和测试代码

本文件展示了如何使用增强后的OptimizedTextGenerationRequest类，
包括渐进式迁移、供应商兼容性检查和参数映射等功能。
"""

import json
import logging
from typing import Dict, Any

# 导入优化版本的请求类
from optimized_text_generation_request import (
    OptimizedTextGenerationRequest,
    SimpleTextGenerationRequest,
    Message,
    MessageRole,
    GenerationConfig,
    StreamConfig,
    SafetyConfig
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_legacy_migration():
    """示例：从传统格式迁移到优化版本"""
    print("=== 传统格式迁移示例 ===")
    
    # 模拟传统的请求数据
    legacy_request_data = {
        "messages": [
            {"role": "system", "content": "你是一个有用的AI助手。"},
            {"role": "user", "content": "请介绍一下人工智能的发展历史。"}
        ],
        "model": "gpt-4",
        "temperature": 0.8,
        "max_tokens": 1000,
        "stream": True,
        "top_p": 0.9,
        "frequency_penalty": 0.1,
        "functions": [
            {
                "name": "search_web",
                "description": "搜索网络信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "搜索查询"}
                    }
                }
            }
        ],
        "safety_settings": [{"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"}]
    }
    
    # 转换为优化版本
    try:
        optimized_request = OptimizedTextGenerationRequest.from_legacy_request(legacy_request_data)
        print("✅ 成功转换为优化版本")
        print(f"生成配置: temperature={optimized_request.generation.temperature}, max_tokens={optimized_request.generation.max_tokens}")
        print(f"流式配置: enabled={optimized_request.stream.enabled}")
        print(f"安全配置: {optimized_request.safety is not None}")
        
        # 转换回传统格式验证兼容性
        legacy_format = optimized_request.to_legacy_format()
        print("✅ 成功转换回传统格式")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")


def example_provider_compatibility():
    """示例：供应商兼容性检查和参数映射"""
    print("\n=== 供应商兼容性示例 ===")
    
    # 创建一个包含多种功能的请求
    request = OptimizedTextGenerationRequest(
        messages=[
            Message(role=MessageRole.SYSTEM, content="你是一个专业的AI助手。"),
            Message(role=MessageRole.USER, content="请帮我分析一下当前的市场趋势。")
        ],
        model="gpt-4",
        generation=GenerationConfig(
            temperature=0.7,
            max_tokens=2000,
            top_p=0.9,
            frequency_penalty=0.1
        ),
        stream=StreamConfig(enabled=True, chunk_size=100),
        safety=SafetyConfig(
            content_filter=True,
            safety_level="medium"
        ),
        functions=[
            {
                "name": "get_market_data",
                "description": "获取市场数据",
                "parameters": {"type": "object", "properties": {"symbol": {"type": "string"}}}
            }
        ],
        response_format={"type": "json_object"},
        provider_params={
            "thinking_budget": 1000,
            "custom_param": "test_value"
        }
    )
    
    # 测试不同供应商的兼容性
    providers = ["openai", "anthropic", "google", "dashscope"]
    
    for provider in providers:
        print(f"\n--- {provider.upper()} 供应商 ---")
        
        # 兼容性验证
        validation_result = request.validate_for_provider(provider)
        
        if validation_result["errors"]:
            print("❌ 错误:")
            for error in validation_result["errors"]:
                print(f"   - {error}")
        
        if validation_result["warnings"]:
            print("⚠️  警告:")
            for warning in validation_result["warnings"]:
                print(f"   - {warning}")
        
        if validation_result["info"]:
            print("ℹ️  信息:")
            for info in validation_result["info"]:
                print(f"   - {info}")
        
        # 获取供应商特定参数
        try:
            provider_params = request.get_provider_specific_params(provider)
            print("✅ 参数映射成功")
            
            # 显示关键参数
            if provider == "openai":
                print(f"   模型: {provider_params.get('model')}")
                print(f"   温度: {provider_params.get('temperature')}")
                print(f"   流式: {provider_params.get('stream')}")
            elif provider == "anthropic":
                print(f"   模型: {provider_params.get('model')}")
                print(f"   最大token: {provider_params.get('max_tokens')}")
                print(f"   系统消息: {'system' in provider_params}")
            elif provider == "google":
                print(f"   内容数量: {len(provider_params.get('contents', []))}")
                print(f"   生成配置: {'generationConfig' in provider_params}")
                print(f"   安全设置: {'safetySettings' in provider_params}")
            elif provider == "dashscope":
                print(f"   模型: {provider_params.get('model')}")
                print(f"   输入消息: {len(provider_params.get('input', {}).get('messages', []))}")
                print(f"   参数配置: {'parameters' in provider_params}")
                
        except Exception as e:
            print(f"❌ 参数映射失败: {e}")


def example_simple_request_enhancement():
    """示例：简化版本的增强功能"""
    print("\n=== 简化版本增强功能示例 ===")
    
    # 创建简化版本请求
    simple_request = SimpleTextGenerationRequest(
        messages=[
            Message(role=MessageRole.USER, content="你好，请介绍一下自己。")
        ],
        model="claude-3-sonnet",
        temperature=0.8,
        max_tokens=500,
        stream=True
    )
    
    print("简化版本请求创建成功")
    
    # 测试供应商兼容性
    validation_result = simple_request.validate_for_provider("anthropic")
    print(f"Anthropic兼容性检查: {len(validation_result['errors'])} 错误, {len(validation_result['warnings'])} 警告")
    
    # 获取供应商参数
    try:
        anthropic_params = simple_request.get_provider_params("anthropic")
        print("✅ 成功获取Anthropic参数")
        print(f"   必需的max_tokens: {anthropic_params.get('max_tokens')}")
        print(f"   消息数量: {len(anthropic_params.get('messages', []))}")
    except Exception as e:
        print(f"❌ 获取参数失败: {e}")
    
    # 转换为优化版本
    optimized = simple_request.to_optimized()
    print("✅ 成功转换为优化版本")
    print(f"   生成配置类型: {type(optimized.generation).__name__}")
    print(f"   流式配置类型: {type(optimized.stream).__name__}")


def example_edge_cases():
    """示例：边界情况处理"""
    print("\n=== 边界情况处理示例 ===")
    
    # 测试空消息列表
    try:
        empty_request = OptimizedTextGenerationRequest(
            messages=[],
            model="gpt-4"
        )
        print("❌ 应该拒绝空消息列表")
    except Exception as e:
        print(f"✅ 正确拒绝空消息列表: {e}")
    
    # 测试超大token限制 - 创建一个绕过验证的请求
    try:
        large_token_request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="测试")],
            model="gpt-4",
            generation=GenerationConfig(max_tokens=30000)  # 使用一个在限制内但对某些供应商来说过大的值
        )
    except Exception as e:
        print(f"✅ 正确检测到token限制: {e}")
        # 创建一个有效的请求用于后续测试
        large_token_request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="测试")],
            model="gpt-4",
            generation=GenerationConfig(max_tokens=30000)
        )
    
    # 检查不同供应商的token限制
    for provider in ["anthropic", "google"]:
        validation = large_token_request.validate_for_provider(provider)
        if validation["warnings"]:
            print(f"✅ {provider} 正确检测到token限制超出: {validation['warnings'][0]}")
    
    # 测试不支持的功能组合
    complex_request = OptimizedTextGenerationRequest(
        messages=[Message(role=MessageRole.USER, content="测试")],
        model="claude-3-sonnet",
        functions=[{"name": "test", "description": "测试函数"}],
        response_format={"type": "json_object"}
    )
    
    anthropic_validation = complex_request.validate_for_provider("anthropic")
    if anthropic_validation["warnings"]:
        print(f"✅ Anthropic 正确检测到不支持的功能: {len(anthropic_validation['warnings'])} 个警告")


if __name__ == "__main__":
    """运行所有示例"""
    print("🚀 开始运行优化版本文本生成请求示例\n")
    
    try:
        example_legacy_migration()
        example_provider_compatibility()
        example_simple_request_enhancement()
        example_edge_cases()
        
        print("\n✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
