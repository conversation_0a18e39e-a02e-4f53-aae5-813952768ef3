<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式输出调试界面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .streaming-container {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            background-color: #f8f9fa;
        }
        
        .streaming-output {
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background-color: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .streaming-controls {
            padding: 10px;
            background-color: #e9ecef;
            border-top: 1px solid #dee2e6;
        }
        
        .chunk-info {
            background-color: #1a1a1a;
            color: #888;
            font-size: 12px;
            padding: 2px 5px;
            margin: 2px 0;
            border-left: 3px solid #007bff;
        }
        
        .error-chunk {
            background-color: #2d1b1b;
            color: #ff6b6b;
            border-left-color: #dc3545;
        }
        
        .done-chunk {
            background-color: #1b2d1b;
            color: #51cf66;
            border-left-color: #28a745;
        }
        
        .streaming-stats {
            display: flex;
            gap: 20px;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-connecting { background-color: #ffc107; }
        .status-connected { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-disconnected { background-color: #6c757d; }
        
        .request-form {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .json-editor {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .btn-stream {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
        }
        
        .btn-stream:hover {
            background: linear-gradient(45deg, #0056b3, #004085);
            color: white;
        }
        
        .btn-stream:disabled {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-stream"></i> 流式输出调试界面</h2>
                <p class="text-muted">测试和调试 AI Gen Hub 的流式文本生成功能</p>
            </div>
        </div>
        
        <div class="row">
            <!-- 请求配置 -->
            <div class="col-md-4">
                <div class="request-form">
                    <h5><i class="fas fa-cog"></i> 请求配置</h5>
                    
                    <div class="mb-3">
                        <label for="endpoint-url" class="form-label">API 端点</label>
                        <input type="text" class="form-control" id="endpoint-url" 
                               value="/api/v1/text/generate" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="model-select" class="form-label">模型</label>
                        <select class="form-select" id="model-select">
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                            <option value="qwen-max">通义千问 Max</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="user-message" class="form-label">用户消息</label>
                        <textarea class="form-control" id="user-message" rows="3" 
                                  placeholder="请输入您的问题...">请写一首关于人工智能的诗</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="system-message" class="form-label">系统消息（可选）</label>
                        <textarea class="form-control" id="system-message" rows="2" 
                                  placeholder="系统指令...">你是一个有用的AI助手。</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <label for="max-tokens" class="form-label">最大Token数</label>
                            <input type="number" class="form-control" id="max-tokens" 
                                   value="500" min="1" max="4000">
                        </div>
                        <div class="col-6">
                            <label for="temperature" class="form-label">Temperature</label>
                            <input type="number" class="form-control" id="temperature" 
                                   value="0.7" min="0" max="2" step="0.1">
                        </div>
                    </div>
                    
                    <div class="mb-3 mt-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include-usage" checked>
                            <label class="form-check-label" for="include-usage">
                                包含使用量统计
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-stream" id="start-stream" onclick="startStreaming()">
                            <i class="fas fa-play"></i> 开始流式生成
                        </button>
                        <button class="btn btn-danger" id="stop-stream" onclick="stopStreaming()" disabled>
                            <i class="fas fa-stop"></i> 停止生成
                        </button>
                        <button class="btn btn-outline-secondary" onclick="clearOutput()">
                            <i class="fas fa-eraser"></i> 清空输出
                        </button>
                    </div>
                </div>
                
                <!-- 预设测试用例 -->
                <div class="request-form">
                    <h6><i class="fas fa-flask"></i> 预设测试用例</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="loadTestCase('short')">
                            短文本生成
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="loadTestCase('long')">
                            长文本生成
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="loadTestCase('code')">
                            代码生成
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="loadTestCase('conversation')">
                            对话测试
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 流式输出显示 -->
            <div class="col-md-8">
                <div class="streaming-container">
                    <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                        <h5 class="mb-0"><i class="fas fa-terminal"></i> 流式输出</h5>
                        <div class="d-flex align-items-center gap-3">
                            <div class="stat-item">
                                <span class="status-indicator" id="connection-status"></span>
                                <span id="status-text">未连接</span>
                            </div>
                            <button class="btn btn-outline-secondary btn-sm" onclick="toggleAutoScroll()">
                                <i class="fas fa-arrow-down" id="autoscroll-icon"></i>
                                <span id="autoscroll-text">自动滚动</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="streaming-output" id="streaming-output">
                        <div class="text-muted text-center mt-5">
                            <i class="fas fa-info-circle"></i> 点击"开始流式生成"按钮开始测试
                        </div>
                    </div>
                    
                    <div class="streaming-controls">
                        <div class="streaming-stats">
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>耗时: <span id="elapsed-time">0.00s</span></span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-cubes"></i>
                                <span>数据块: <span id="chunk-count">0</span></span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-font"></i>
                                <span>字符数: <span id="char-count">0</span></span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>速度: <span id="generation-speed">0</span> 字符/秒</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细信息面板 -->
                <div class="mt-3">
                    <div class="accordion" id="detailsAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingDetails">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#collapseDetails">
                                    <i class="fas fa-info-circle me-2"></i> 详细信息
                                </button>
                            </h2>
                            <div id="collapseDetails" class="accordion-collapse collapse" 
                                 data-bs-parent="#detailsAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>请求信息</h6>
                                            <pre id="request-details" class="bg-light p-2 rounded"></pre>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>响应头信息</h6>
                                            <pre id="response-headers" class="bg-light p-2 rounded"></pre>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <h6>原始数据块</h6>
                                            <div id="raw-chunks" class="bg-light p-2 rounded" 
                                                 style="max-height: 300px; overflow-y: auto;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let eventSource = null;
        let startTime = null;
        let chunkCount = 0;
        let charCount = 0;
        let autoScroll = true;
        let generatedContent = '';
        let rawChunks = [];

        // 状态管理
        function updateStatus(status, text) {
            const indicator = document.getElementById('connection-status');
            const statusText = document.getElementById('status-text');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        // 更新统计信息
        function updateStats() {
            const elapsed = startTime ? (Date.now() - startTime) / 1000 : 0;
            const speed = elapsed > 0 ? (charCount / elapsed).toFixed(1) : 0;
            
            document.getElementById('elapsed-time').textContent = elapsed.toFixed(2) + 's';
            document.getElementById('chunk-count').textContent = chunkCount;
            document.getElementById('char-count').textContent = charCount;
            document.getElementById('generation-speed').textContent = speed;
        }

        // 添加输出内容
        function appendOutput(content, type = 'content') {
            const output = document.getElementById('streaming-output');
            
            if (type === 'content') {
                // 添加生成的内容
                const span = document.createElement('span');
                span.textContent = content;
                output.appendChild(span);
                
                generatedContent += content;
                charCount += content.length;
            } else if (type === 'info') {
                // 添加信息块
                const div = document.createElement('div');
                div.className = 'chunk-info';
                div.textContent = content;
                output.appendChild(div);
            } else if (type === 'error') {
                // 添加错误信息
                const div = document.createElement('div');
                div.className = 'chunk-info error-chunk';
                div.textContent = content;
                output.appendChild(div);
            } else if (type === 'done') {
                // 添加完成信息
                const div = document.createElement('div');
                div.className = 'chunk-info done-chunk';
                div.textContent = content;
                output.appendChild(div);
            }
            
            // 自动滚动
            if (autoScroll) {
                output.scrollTop = output.scrollHeight;
            }
            
            updateStats();
        }

        // 开始流式生成
        async function startStreaming() {
            if (eventSource) {
                stopStreaming();
            }
            
            // 重置状态
            chunkCount = 0;
            charCount = 0;
            generatedContent = '';
            rawChunks = [];
            startTime = Date.now();
            
            // 清空输出
            document.getElementById('streaming-output').innerHTML = '';
            
            // 构建请求数据
            const requestData = {
                model: document.getElementById('model-select').value,
                messages: [],
                stream: { enabled: true },
                generation: {
                    max_tokens: parseInt(document.getElementById('max-tokens').value),
                    temperature: parseFloat(document.getElementById('temperature').value)
                }
            };
            
            // 添加系统消息
            const systemMessage = document.getElementById('system-message').value.trim();
            if (systemMessage) {
                requestData.messages.push({
                    role: 'system',
                    content: systemMessage
                });
            }
            
            // 添加用户消息
            const userMessage = document.getElementById('user-message').value.trim();
            if (!userMessage) {
                alert('请输入用户消息');
                return;
            }
            
            requestData.messages.push({
                role: 'user',
                content: userMessage
            });
            
            // 更新详细信息
            document.getElementById('request-details').textContent = JSON.stringify(requestData, null, 2);
            
            // 更新按钮状态
            document.getElementById('start-stream').disabled = true;
            document.getElementById('stop-stream').disabled = false;
            
            updateStatus('connecting', '连接中...');
            appendOutput('🔄 开始连接流式API...', 'info');
            
            try {
                // 发送POST请求获取流式响应
                const response = await fetch('/api/v1/text/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                // 更新响应头信息
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                document.getElementById('response-headers').textContent = JSON.stringify(headers, null, 2);
                
                updateStatus('connected', '已连接');
                appendOutput('✅ 连接成功，开始接收数据...', 'info');
                
                // 读取流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        break;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            
                            if (data.trim() === '[DONE]') {
                                appendOutput('🎉 生成完成', 'done');
                                stopStreaming();
                                return;
                            }
                            
                            try {
                                const parsed = JSON.parse(data);
                                rawChunks.push(parsed);
                                
                                if (parsed.choices && parsed.choices[0]) {
                                    const choice = parsed.choices[0];
                                    const delta = choice.delta || {};
                                    
                                    if (delta.content) {
                                        appendOutput(delta.content, 'content');
                                        chunkCount++;
                                    }
                                    
                                    if (choice.finish_reason) {
                                        appendOutput(`🏁 完成原因: ${choice.finish_reason}`, 'info');
                                    }
                                }
                            } catch (e) {
                                console.warn('解析数据块失败:', e, data);
                            }
                        }
                    }
                }
                
            } catch (error) {
                console.error('流式请求失败:', error);
                appendOutput(`❌ 错误: ${error.message}`, 'error');
                updateStatus('error', '连接错误');
                stopStreaming();
            }
        }

        // 停止流式生成
        function stopStreaming() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            
            document.getElementById('start-stream').disabled = false;
            document.getElementById('stop-stream').disabled = true;
            
            updateStatus('disconnected', '已断开');
            
            // 更新原始数据块显示
            const rawChunksDiv = document.getElementById('raw-chunks');
            rawChunksDiv.innerHTML = rawChunks.map((chunk, index) => 
                `<div><strong>块 ${index + 1}:</strong><br><pre>${JSON.stringify(chunk, null, 2)}</pre></div>`
            ).join('<hr>');
        }

        // 清空输出
        function clearOutput() {
            document.getElementById('streaming-output').innerHTML = '';
            chunkCount = 0;
            charCount = 0;
            generatedContent = '';
            rawChunks = [];
            updateStats();
        }

        // 切换自动滚动
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            const icon = document.getElementById('autoscroll-icon');
            const text = document.getElementById('autoscroll-text');
            
            if (autoScroll) {
                icon.className = 'fas fa-arrow-down';
                text.textContent = '自动滚动';
            } else {
                icon.className = 'fas fa-pause';
                text.textContent = '已暂停';
            }
        }

        // 加载测试用例
        function loadTestCase(type) {
            const testCases = {
                'short': {
                    system: '你是一个简洁的助手。',
                    user: '用一句话解释什么是人工智能。',
                    maxTokens: 100
                },
                'long': {
                    system: '你是一个详细的助手。',
                    user: '请详细介绍人工智能的发展历史、现状和未来趋势。',
                    maxTokens: 1000
                },
                'code': {
                    system: '你是一个编程助手。',
                    user: '请用Python写一个快速排序算法，并添加详细注释。',
                    maxTokens: 800
                },
                'conversation': {
                    system: '你是一个友好的聊天伙伴。',
                    user: '你好！今天天气怎么样？我们聊聊你最喜欢的话题吧。',
                    maxTokens: 500
                }
            };
            
            const testCase = testCases[type];
            if (testCase) {
                document.getElementById('system-message').value = testCase.system;
                document.getElementById('user-message').value = testCase.user;
                document.getElementById('max-tokens').value = testCase.maxTokens;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('disconnected', '未连接');
            updateStats();
        });
    </script>
</body>
</html>
