"""
流式输出客户端示例

提供多种语言和框架的客户端实现示例，展示如何正确接收和处理流式数据
"""

import asyncio
import json
import time
from typing import AsyncIterator, Iterator
import httpx
import requests
from sse_starlette.sse import EventSourceResponse


# === Python 异步客户端示例 ===
class AsyncStreamingClient:
    """Python 异步流式客户端"""
    
    def __init__(self, base_url: str, api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
    
    def _get_headers(self) -> dict:
        """获取请求头"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"
        }
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        return headers
    
    async def generate_stream(
        self, 
        messages: list, 
        model: str = "gpt-4",
        **kwargs
    ) -> AsyncIterator[dict]:
        """生成流式响应"""
        request_data = {
            "model": model,
            "messages": messages,
            "stream": {"enabled": True},
            **kwargs
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            async with client.stream(
                "POST",
                f"{self.base_url}/api/v1/text/generate",
                json=request_data,
                headers=self._get_headers()
            ) as response:
                if response.status_code != 200:
                    error_text = await response.aread()
                    raise Exception(f"API错误 {response.status_code}: {error_text.decode()}")
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # 移除 "data: " 前缀
                        if data.strip() == "[DONE]":
                            break
                        
                        try:
                            chunk = json.loads(data)
                            yield chunk
                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {e}, 数据: {data}")
                            continue
    
    async def chat_completion(self, messages: list, **kwargs) -> str:
        """完整的聊天补全（收集所有流式输出）"""
        full_content = ""
        
        async for chunk in self.generate_stream(messages, **kwargs):
            if chunk.get("choices"):
                choice = chunk["choices"][0]
                delta = choice.get("delta", {})
                content = delta.get("content", "")
                if content:
                    full_content += content
        
        return full_content
    
    async def chat_completion_with_callback(
        self, 
        messages: list, 
        on_chunk=None,
        on_complete=None,
        **kwargs
    ):
        """带回调的聊天补全"""
        full_content = ""
        
        try:
            async for chunk in self.generate_stream(messages, **kwargs):
                if chunk.get("choices"):
                    choice = chunk["choices"][0]
                    delta = choice.get("delta", {})
                    content = delta.get("content", "")
                    
                    if content:
                        full_content += content
                        if on_chunk:
                            await on_chunk(content, chunk)
                    
                    # 检查是否完成
                    if choice.get("finish_reason"):
                        break
            
            if on_complete:
                await on_complete(full_content)
                
        except Exception as e:
            print(f"流式生成错误: {e}")
            raise


# === Python 同步客户端示例 ===
class SyncStreamingClient:
    """Python 同步流式客户端"""
    
    def __init__(self, base_url: str, api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        
        if self.api_key:
            self.session.headers.update({
                "Authorization": f"Bearer {self.api_key}"
            })
    
    def generate_stream(
        self, 
        messages: list, 
        model: str = "gpt-4",
        **kwargs
    ) -> Iterator[dict]:
        """生成流式响应"""
        request_data = {
            "model": model,
            "messages": messages,
            "stream": {"enabled": True},
            **kwargs
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"
        }
        
        with self.session.post(
            f"{self.base_url}/api/v1/text/generate",
            json=request_data,
            headers=headers,
            stream=True
        ) as response:
            if response.status_code != 200:
                raise Exception(f"API错误 {response.status_code}: {response.text}")
            
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith("data: "):
                    data = line[6:]  # 移除 "data: " 前缀
                    if data.strip() == "[DONE]":
                        break
                    
                    try:
                        chunk = json.loads(data)
                        yield chunk
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {e}, 数据: {data}")
                        continue


# === JavaScript/TypeScript 客户端示例 ===
JAVASCRIPT_CLIENT_EXAMPLE = '''
/**
 * JavaScript/TypeScript 流式客户端示例
 */

class StreamingClient {
    constructor(baseUrl, apiKey = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.apiKey = apiKey;
    }

    async *generateStream(messages, model = 'gpt-4', options = {}) {
        const requestData = {
            model,
            messages,
            stream: { enabled: true },
            ...options
        };

        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
        };

        if (this.apiKey) {
            headers['Authorization'] = `Bearer ${this.apiKey}`;
        }

        const response = await fetch(`${this.baseUrl}/api/v1/text/generate`, {
            method: 'POST',
            headers,
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`API错误 ${response.status}: ${await response.text()}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data.trim() === '[DONE]') {
                            return;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            yield parsed;
                        } catch (e) {
                            console.warn('JSON解析错误:', e, '数据:', data);
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }
    }

    async chatCompletion(messages, options = {}) {
        let fullContent = '';
        
        for await (const chunk of this.generateStream(messages, options.model, options)) {
            if (chunk.choices && chunk.choices[0]) {
                const delta = chunk.choices[0].delta || {};
                const content = delta.content || '';
                if (content) {
                    fullContent += content;
                }
            }
        }
        
        return fullContent;
    }

    async chatCompletionWithCallback(messages, onChunk, onComplete, options = {}) {
        let fullContent = '';
        
        try {
            for await (const chunk of this.generateStream(messages, options.model, options)) {
                if (chunk.choices && chunk.choices[0]) {
                    const choice = chunk.choices[0];
                    const delta = choice.delta || {};
                    const content = delta.content || '';
                    
                    if (content) {
                        fullContent += content;
                        if (onChunk) {
                            await onChunk(content, chunk);
                        }
                    }
                    
                    if (choice.finish_reason) {
                        break;
                    }
                }
            }
            
            if (onComplete) {
                await onComplete(fullContent);
            }
        } catch (error) {
            console.error('流式生成错误:', error);
            throw error;
        }
    }
}

// 使用示例
async function example() {
    const client = new StreamingClient('http://localhost:8000');
    
    const messages = [
        { role: 'user', content: '请写一首关于AI的诗' }
    ];
    
    console.log('开始生成...');
    
    // 方式1: 使用异步迭代器
    for await (const chunk of client.generateStream(messages)) {
        if (chunk.choices && chunk.choices[0]) {
            const content = chunk.choices[0].delta?.content || '';
            if (content) {
                process.stdout.write(content);
            }
        }
    }
    
    console.log('\\n\\n生成完成');
    
    // 方式2: 使用回调
    await client.chatCompletionWithCallback(
        messages,
        (content, chunk) => {
            process.stdout.write(content);
        },
        (fullContent) => {
            console.log(`\\n\\n完整内容长度: ${fullContent.length}`);
        }
    );
}
'''


# === 使用示例和测试 ===
async def example_usage():
    """使用示例"""
    print("🚀 流式客户端使用示例")
    
    # 创建客户端
    client = AsyncStreamingClient("http://localhost:8000")
    
    messages = [
        {"role": "system", "content": "你是一个有用的助手。"},
        {"role": "user", "content": "请简单介绍一下人工智能的发展历程。"}
    ]
    
    print("\n1. 基础流式输出示例:")
    print("-" * 50)
    
    start_time = time.time()
    full_content = ""
    
    async for chunk in client.generate_stream(messages, max_tokens=200):
        if chunk.get("choices"):
            choice = chunk["choices"][0]
            delta = choice.get("delta", {})
            content = delta.get("content", "")
            
            if content:
                print(content, end="", flush=True)
                full_content += content
            
            # 显示完成原因
            if choice.get("finish_reason"):
                print(f"\n\n[完成原因: {choice['finish_reason']}]")
                break
    
    end_time = time.time()
    
    print(f"\n生成完成!")
    print(f"总时间: {end_time - start_time:.2f}秒")
    print(f"内容长度: {len(full_content)} 字符")
    
    print("\n2. 带回调的流式输出示例:")
    print("-" * 50)
    
    chunk_count = 0
    
    async def on_chunk(content, chunk):
        nonlocal chunk_count
        chunk_count += 1
        print(f"[块{chunk_count}] {content}", end="", flush=True)
    
    async def on_complete(full_content):
        print(f"\n\n✅ 生成完成，共 {chunk_count} 个数据块，{len(full_content)} 字符")
    
    await client.chat_completion_with_callback(
        messages=[{"role": "user", "content": "用一句话总结机器学习的核心思想。"}],
        on_chunk=on_chunk,
        on_complete=on_complete
    )


def sync_example():
    """同步客户端示例"""
    print("\n3. 同步客户端示例:")
    print("-" * 50)
    
    client = SyncStreamingClient("http://localhost:8000")
    
    messages = [
        {"role": "user", "content": "请用三个词描述春天。"}
    ]
    
    for chunk in client.generate_stream(messages):
        if chunk.get("choices"):
            choice = chunk["choices"][0]
            delta = choice.get("delta", {})
            content = delta.get("content", "")
            
            if content:
                print(content, end="", flush=True)
            
            if choice.get("finish_reason"):
                print(f"\n[完成: {choice['finish_reason']}]")
                break


if __name__ == "__main__":
    print("📝 JavaScript客户端示例代码:")
    print(JAVASCRIPT_CLIENT_EXAMPLE)
    
    print("\n" + "="*80)
    print("🐍 Python客户端示例:")
    
    # 运行异步示例
    asyncio.run(example_usage())
    
    # 运行同步示例
    sync_example()
