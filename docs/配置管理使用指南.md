# AI Gen Hub 配置管理使用指南

## 概述

AI Gen Hub 提供了增强的配置管理系统，支持多种配置源，优先从系统环境变量获取配置，回退到 .env 文件，确保配置的安全性和灵活性。

## 配置优先级

配置系统按以下优先级获取配置：

1. **系统环境变量** (最高优先级)
2. **项目根目录 .env 文件**
3. **当前目录 .env 文件** (备用)
4. **默认值** (最低优先级)

## 核心组件

### 1. 增强配置管理器 (ConfigManager)

位置：`src/ai_gen_hub/config/manager.py`

#### 主要功能：
- 优先从系统环境变量获取配置
- 自动回退到 .env 文件
- 提供详细的配置获取调试日志
- 安全的配置验证和错误处理
- 支持配置热重载
- 配置来源追踪

#### 使用示例：

```python
from ai_gen_hub.config import get_config_manager, get_enhanced_settings

# 获取配置管理器（推荐方式）
config_manager = get_config_manager(debug_logging=True)
settings = config_manager.get_config()

# 或者直接获取配置（简化方式）
settings = get_enhanced_settings(debug_logging=True)

# 获取配置来源信息
sources_summary = config_manager.get_config_sources_summary()
print(f"环境变量配置数量: {sources_summary['from_environment']}")
print(f"文件配置数量: {sources_summary['from_file_or_default']}")
```

### 2. AI 供应商配置优化

#### 环境变量配置格式：

```bash
# OpenAI 配置
OPENAI_API_KEYS=sk-key1,sk-key2,sk-key3
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_TIMEOUT=60
OPENAI_MAX_RETRIES=3
OPENAI_ENABLED=true

# Google AI 配置
GOOGLE_AI_API_KEYS=AIza-key1,AIza-key2
GOOGLE_AI_BASE_URL=https://generativelanguage.googleapis.com/v1
GOOGLE_AI_TIMEOUT=60
GOOGLE_AI_MAX_RETRIES=3
GOOGLE_AI_ENABLED=true

# Anthropic 配置
ANTHROPIC_API_KEYS=sk-ant-key1,sk-ant-key2
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_TIMEOUT=60
ANTHROPIC_MAX_RETRIES=3
ANTHROPIC_ENABLED=true

# DashScope 配置
DASHSCOPE_API_KEYS=dash-key1,dash-key2
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/api/v1
DASHSCOPE_TIMEOUT=60
DASHSCOPE_MAX_RETRIES=3
DASHSCOPE_ENABLED=true

# Azure 配置
AZURE_API_KEYS=azure-key1,azure-key2
AZURE_BASE_URL=https://your-resource.openai.azure.com
AZURE_TIMEOUT=60
AZURE_MAX_RETRIES=3
AZURE_ENABLED=true
```

#### 配置验证功能：

```python
# 验证所有配置
validation_result = settings.validate_all_configs()

if validation_result['valid']:
    print("✅ 所有配置验证通过")
    print(f"启用的供应商: {validation_result['summary']['enabled_providers']}")
else:
    print("❌ 配置验证失败")
    for error in validation_result['errors']:
        print(f"错误: {error}")
    for warning in validation_result['warnings']:
        print(f"警告: {warning}")
```

#### 获取供应商配置：

```python
# 获取特定供应商配置
openai_config = settings.get_provider_config('openai')
if openai_config and openai_config.enabled:
    print(f"OpenAI API密钥数量: {len(openai_config.api_keys)}")
    print(f"超时时间: {openai_config.timeout}秒")

# 获取所有启用的供应商
enabled_providers = settings.get_enabled_providers()
print(f"启用的供应商: {enabled_providers}")
```

## 安全特性

### 1. 敏感信息脱敏

配置系统自动对敏感信息进行脱敏处理：

```python
# 获取安全摘要（敏感信息已脱敏）
security_summary = settings.get_config_security_summary()
print(security_summary)
# 输出示例：
# {
#   "security": {
#     "jwt_secret_key": "ai-g****tion",
#     "api_key": "dev-****key"
#   },
#   "providers": {
#     "openai": {
#       "enabled": true,
#       "api_keys_count": 2,
#       "base_url": "https://api.openai.com/v1"
#     }
#   }
# }
```

### 2. API 密钥格式验证

系统会自动验证 API 密钥格式：

- **OpenAI**: 必须以 `sk-` 开头，长度大于 20
- **Google AI**: 必须以 `AIza` 开头，长度大于 30
- **Anthropic**: 必须以 `sk-ant-` 开头，长度大于 20
- **DashScope**: 长度大于 20（格式较灵活）
- **Azure**: 长度大于 20（格式较灵活）

### 3. 生产环境安全检查

```python
# 检查生产环境安全配置
if settings.environment == "production":
    validation = settings.validate_all_configs()
    security_issues = [error for error in validation['errors'] if 'production' in error.lower()]
    
    if security_issues:
        print("🚨 生产环境安全问题:")
        for issue in security_issues:
            print(f"  - {issue}")
```

## 配置调试

### 1. 启用调试日志

```python
# 启用详细的配置调试日志
settings = get_enhanced_settings(debug_logging=True)

# 输出示例：
# 🔧 开始加载配置...
#    Python版本: 3.11.0
#    工作目录: /path/to/project
#    🌍 OPENAI_API_KEYS=sk-1****key1 (来源: 环境变量)
#    🌍 GOOGLE_AI_API_KEYS=AIza****key2 (来源: 环境变量)
# ✅ 配置加载完成:
#    应用名称: AI Gen Hub
#    环境: development
#    启用的AI供应商: ['openai', 'google_ai']
```

### 2. 配置来源追踪

```python
# 获取配置来源详情
config_manager = get_config_manager(debug_logging=True)
sources = config_manager.get_config_sources_summary()

print(f"配置来源统计:")
print(f"  环境变量: {sources['from_environment']} 项")
print(f"  文件/默认: {sources['from_file_or_default']} 项")
print(f"  优先级说明: {sources['environment_priority']}")
```

## 最佳实践

### 1. 开发环境配置

创建 `.env` 文件：

```bash
# 基础配置
ENVIRONMENT=development
DEBUG=true
API_HOST=0.0.0.0
API_PORT=8000

# 安全配置
JWT_SECRET_KEY=dev-secret-key-change-in-production
API_KEY=dev-api-key

# AI供应商配置（填入真实的API密钥）
OPENAI_API_KEYS=sk-your-openai-key
GOOGLE_AI_API_KEYS=AIza-your-google-ai-key
# ANTHROPIC_API_KEYS=sk-ant-your-anthropic-key
# DASHSCOPE_API_KEYS=your-dashscope-key
# AZURE_API_KEYS=your-azure-key

# 功能开关
ENABLE_TEXT_GENERATION=true
ENABLE_IMAGE_GENERATION=true
ENABLE_CACHING=true
```

### 2. 生产环境配置

使用系统环境变量：

```bash
# 通过系统环境变量设置（推荐）
export ENVIRONMENT=production
export DEBUG=false
export JWT_SECRET_KEY=your-super-secure-jwt-key
export API_KEY=your-production-api-key
export OPENAI_API_KEYS=sk-prod-key1,sk-prod-key2
export GOOGLE_AI_API_KEYS=AIza-prod-key1,AIza-prod-key2
```

### 3. 配置验证脚本

```python
#!/usr/bin/env python3
"""配置验证脚本"""

from ai_gen_hub.config import get_enhanced_settings

def validate_production_config():
    settings = get_enhanced_settings()
    
    if settings.environment != "production":
        print("⚠️ 当前不是生产环境")
        return
    
    validation = settings.validate_all_configs()
    
    if validation['valid']:
        print("✅ 生产环境配置验证通过")
        print(f"启用的供应商: {validation['summary']['enabled_providers']}")
    else:
        print("❌ 生产环境配置验证失败")
        for error in validation['errors']:
            print(f"错误: {error}")
        exit(1)

if __name__ == "__main__":
    validate_production_config()
```

## 故障排除

### 1. 配置未生效

检查配置优先级和来源：

```python
config_manager = get_config_manager(debug_logging=True)
settings = config_manager.get_config()

# 检查特定配置的来源
sources = config_manager.get_config_sources_summary()
print("配置来源:", sources['sources'])
```

### 2. API 密钥无效

```python
# 检查 API 密钥配置
for provider in ['openai', 'google_ai', 'anthropic']:
    config = settings.get_provider_config(provider)
    if config:
        print(f"{provider}: 启用={config.enabled}, 密钥数量={len(config.api_keys)}")
```

### 3. 环境变量未读取

确保环境变量名称正确，并重启应用：

```bash
# 检查环境变量
env | grep -E "(OPENAI|GOOGLE_AI|ANTHROPIC|DASHSCOPE|AZURE)_"

# 重新加载配置
python -c "
from ai_gen_hub.config import get_config_manager
manager = get_config_manager(debug_logging=True)
settings = manager.reload_config()
print('配置重新加载完成')
"
```

## 相关文档

- [测试脚本使用指南](./测试脚本使用指南.md)
- [API 文档](./api.md)
- [部署指南](./deployment.md)
