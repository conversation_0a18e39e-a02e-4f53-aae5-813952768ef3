# AI Gen Hub API 文档

## 概述

AI Gen Hub 提供统一的 RESTful API 接口，支持多种 AI 服务的调用，包括文本生成和图像生成。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: `v1`
- **认证方式**: API Key 或 JWT Token
- **数据格式**: JSON

## 认证

### API Key 认证

在请求头中添加 API Key：

```http
X-API-Key: your-api-key-here
```

或在查询参数中添加：

```http
GET /api/v1/text/models?api_key=your-api-key-here
```

### JWT Token 认证

在请求头中添加 Bearer Token：

```http
Authorization: Bearer your-jwt-token-here
```

## 文本生成 API

### 生成文本

**端点**: `POST /api/v1/text/generate`

**描述**: 根据输入消息生成文本响应

**请求体**:
```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下自己"
    }
  ],
  "max_tokens": 100,
  "temperature": 0.7,
  "stream": false
}
```

**响应**:
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "你好！我是AI助手，很高兴为您服务..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 20,
    "total_tokens": 30
  },
  "provider": "openai",
  "request_id": "req-123",
  "processing_time": 1.5
}
```

### 流式文本生成

设置 `"stream": true` 启用流式输出，响应将以 Server-Sent Events 格式返回：

```
data: {"id":"chatcmpl-123","choices":[{"delta":{"content":"你好"}}]}

data: {"id":"chatcmpl-123","choices":[{"delta":{"content":"！"}}]}

data: [DONE]
```

### 获取支持的模型

**端点**: `GET /api/v1/text/models`

**响应**:
```json
{
  "openai": ["gpt-4", "gpt-3.5-turbo"],
  "anthropic": ["claude-3-sonnet", "claude-3-haiku"],
  "google_ai": ["gemini-pro"]
}
```

### OpenAI 兼容端点

**端点**: `POST /api/v1/text/chat/completions`

提供与 OpenAI Chat Completions API 完全兼容的接口。

## 图像生成 API

### 生成图像

**端点**: `POST /api/v1/image/generate`

**请求体**:
```json
{
  "prompt": "一只可爱的小猫坐在花园里",
  "model": "dall-e-3",
  "n": 1,
  "size": "1024x1024",
  "quality": "standard",
  "response_format": "url"
}
```

**响应**:
```json
{
  "created": **********,
  "data": [
    {
      "url": "https://example.com/generated-image.png",
      "revised_prompt": "一只可爱的橘色小猫坐在色彩斑斓的花园里"
    }
  ],
  "provider": "openai",
  "request_id": "img-123",
  "processing_time": 8.2
}
```

### 批量图像生成

**端点**: `POST /api/v1/image/generate/batch`

**请求体**:
```json
[
  {
    "prompt": "一只猫",
    "model": "dall-e-3",
    "n": 1
  },
  {
    "prompt": "一只狗",
    "model": "dall-e-3", 
    "n": 1
  }
]
```

### 获取支持的模型

**端点**: `GET /api/v1/image/models`

## 健康检查 API

### 存活检查

**端点**: `GET /health/live`

**响应**:
```json
{
  "status": "alive",
  "timestamp": **********
}
```

### 就绪检查

**端点**: `GET /health/ready`

**响应**:
```json
{
  "status": "ready",
  "overall_status": "healthy",
  "timestamp": **********
}
```

### 详细健康检查

**端点**: `GET /health/`

**响应**:
```json
{
  "overall_status": "healthy",
  "timestamp": **********,
  "duration": 0.15,
  "summary": {
    "total": 3,
    "healthy": 3,
    "degraded": 0,
    "unhealthy": 0
  },
  "checks": [
    {
      "name": "system",
      "status": "healthy",
      "message": "系统状态正常",
      "duration": 0.05
    }
  ]
}
```

## 监控指标 API

### Prometheus 指标

**端点**: `GET /metrics/prometheus`

返回 Prometheus 格式的指标数据。

### 系统统计

**端点**: `GET /metrics/stats`

**响应**:
```json
{
  "timestamp": **********,
  "providers": {
    "total_providers": 3,
    "healthy_providers": 3
  },
  "cache": {
    "hit_rate": 0.85,
    "total_size": 1048576
  }
}
```

## WebSocket API

### 实时文本生成

**端点**: `ws://localhost:8000/ws/text/generate`

**连接消息**:
```json
{
  "type": "connection",
  "status": "connected",
  "client_id": "ws_12345"
}
```

**生成请求**:
```json
{
  "type": "generate",
  "request_id": "req-123",
  "data": {
    "model": "gpt-4",
    "messages": [
      {"role": "user", "content": "你好"}
    ],
    "stream": true
  }
}
```

**响应消息**:
```json
{
  "type": "generation_chunk",
  "request_id": "req-123",
  "data": {
    "choices": [{"delta": {"content": "你好"}}]
  }
}
```

## 错误处理

### 错误响应格式

```json
{
  "error": {
    "error_code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": "temperature 必须在 0.0-2.0 之间",
    "retryable": false
  },
  "request_id": "req-123"
}
```

### 常见错误码

- `INVALID_REQUEST`: 请求参数无效
- `AUTHENTICATION_ERROR`: 认证失败
- `AUTHORIZATION_ERROR`: 授权失败
- `MODEL_NOT_SUPPORTED`: 模型不支持
- `PROVIDER_UNAVAILABLE`: 供应商不可用
- `RATE_LIMIT_EXCEEDED`: 速率限制超限
- `QUOTA_EXCEEDED`: 配额超限
- `TIMEOUT_ERROR`: 请求超时
- `INTERNAL_SERVER_ERROR`: 内部服务器错误

## 速率限制

API 实施速率限制以确保服务稳定性：

- **默认限制**: 每分钟 60 次请求
- **响应头**: 
  - `X-RateLimit-Limit`: 限制数量
  - `X-RateLimit-Remaining`: 剩余次数
  - `Retry-After`: 重试等待时间（秒）

## 请求追踪

每个请求都会分配唯一的请求ID，用于追踪和调试：

- **请求头**: `X-Request-ID` (可选，客户端提供)
- **响应头**: `X-Request-ID` (服务器返回)
- **响应体**: `request_id` 字段

## SDK 和示例

### Python SDK

```python
from ai_gen_hub_client import AIGenHubClient

client = AIGenHubClient(
    base_url="http://localhost:8000",
    api_key="your-api-key"
)

# 文本生成
response = client.text.generate(
    model="gpt-4",
    messages=[
        {"role": "user", "content": "你好"}
    ]
)

# 图像生成
response = client.image.generate(
    prompt="一只可爱的小猫",
    model="dall-e-3"
)
```

### JavaScript SDK

```javascript
import { AIGenHubClient } from 'ai-gen-hub-client';

const client = new AIGenHubClient({
  baseURL: 'http://localhost:8000',
  apiKey: 'your-api-key'
});

// 文本生成
const response = await client.text.generate({
  model: 'gpt-4',
  messages: [
    { role: 'user', content: '你好' }
  ]
});

// 流式文本生成
const stream = await client.text.generateStream({
  model: 'gpt-4',
  messages: [
    { role: 'user', content: '请写一首诗' }
  ]
});

for await (const chunk of stream) {
  console.log(chunk.choices[0].delta.content);
}
```

### cURL 示例

```bash
# 文本生成
curl -X POST http://localhost:8000/api/v1/text/generate \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {"role": "user", "content": "你好"}
    ]
  }'

# 图像生成
curl -X POST http://localhost:8000/api/v1/image/generate \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "prompt": "一只可爱的小猫",
    "model": "dall-e-3"
  }'
```
