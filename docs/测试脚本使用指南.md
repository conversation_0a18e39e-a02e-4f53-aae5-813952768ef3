# AI Gen Hub 测试脚本使用指南

## 概述

AI Gen Hub 提供了统一的测试工具和脚本，消除了重复代码，提供了一致的测试体验。所有测试脚本都使用增强的配置管理器和通用测试工具。

## 测试脚本架构

### 1. 通用测试工具模块

位置：`src/ai_gen_hub/utils/test_utils.py`

#### 核心组件：

- **TestRunner**: 测试运行器，提供统一的测试执行和结果报告
- **TestResult**: 测试结果类，标准化测试结果格式
- **ConfigTestUtils**: 配置测试工具类
- **AppTestUtils**: 应用测试工具类

#### 主要功能：

- 自动设置测试环境
- 统一的错误处理和日志记录
- 标准化的测试结果格式
- 支持同步和异步测试
- 配置和应用创建的通用方法

## 可用的测试脚本

### 1. 统一测试脚本 (unified_test.py)

**推荐使用的主要测试脚本**

```bash
# 运行综合测试（默认）
python unified_test.py

# 运行快速测试
python unified_test.py quick

# 只测试配置系统
python unified_test.py config

# 只测试端点功能
python unified_test.py endpoints

# 启用调试模式
python unified_test.py comprehensive --debug
```

#### 测试内容：

- **配置系统测试**: 增强配置管理器、AI供应商配置验证
- **应用创建测试**: 同步/异步应用创建和初始化
- **端点功能测试**: 关键API端点的功能验证
- **集成测试**: 完整的系统集成验证

### 2. 快速测试脚本 (quick_test.py)

**快速验证核心功能**

```bash
python quick_test.py
```

#### 测试内容：

- 配置加载验证
- `get_system_info` 函数性能测试
- `system/info` 端点响应时间测试
- `test-endpoint` 接口超时保护测试

### 3. 诊断测试脚本 (test_diagnostic.py)

**诊断系统功能和路由注册**

```bash
python test_diagnostic.py
```

#### 测试内容：

- 诊断端点功能验证
- 调试路由注册检查
- 应用状态管理验证
- 异步应用初始化测试

### 4. 简单测试脚本 (simple_test.py)

**基础功能验证**

```bash
python simple_test.py
```

#### 测试内容：

- 调试路由加载验证
- 基础端点响应测试
- 应用创建基础检查

### 5. 正式测试套件 (tests/run_tests.py)

**完整的测试套件**

```bash
# 运行所有测试
python tests/run_tests.py all

# 运行单元测试
python tests/run_tests.py unit

# 运行集成测试
python tests/run_tests.py integration

# 生成测试报告
python tests/run_tests.py report

# 运行代码检查
python tests/run_tests.py lint
```

## 使用通用测试工具

### 1. 基础使用

```python
from ai_gen_hub.utils.test_utils import (
    setup_test_environment, TestRunner, AppTestUtils, ConfigTestUtils
)

# 设置测试环境
setup_test_environment(debug=True, load_env=True)

# 创建测试运行器
runner = TestRunner("我的测试")
runner.start()

# 运行测试
runner.run_test("配置测试", ConfigTestUtils.test_config_loading, True)
runner.run_test("应用测试", AppTestUtils.create_test_app)

# 完成测试
success = runner.finish()
```

### 2. 配置测试工具

```python
# 测试配置加载
result = ConfigTestUtils.test_config_loading(debug_logging=True)
if result['success']:
    print(f"环境: {result['environment']}")
    print(f"启用的供应商: {result['enabled_providers']}")

# 测试供应商配置
result = ConfigTestUtils.test_provider_configs()
if result['success']:
    print(f"验证结果: {result['validation_result']['valid']}")
    print(f"警告数量: {result['warnings_count']}")
```

### 3. 应用测试工具

```python
# 创建测试应用
app = AppTestUtils.create_test_app()

# 测试端点
result = AppTestUtils.test_endpoint(app, "/health", "GET")
if result['success']:
    print(f"状态码: {result['status_code']}")
    print(f"响应时间: {result['duration']:.3f}秒")

# 异步应用测试
async def test_async_app():
    app, app_instance = await AppTestUtils.create_test_app_async()
    # 进行测试...
    await app_instance.cleanup()
```

### 4. 自定义测试函数

```python
def my_custom_test():
    """自定义测试函数
    
    Returns:
        测试结果字典，必须包含 'success' 字段
    """
    try:
        # 执行测试逻辑
        result = some_test_operation()
        
        return {
            'success': True,
            'result': result,
            'additional_info': 'test passed'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

# 使用测试运行器运行自定义测试
runner.run_test("自定义测试", my_custom_test)
```

## 测试结果格式

### 1. 标准测试结果

```python
{
    'success': True,          # 测试是否成功
    'error': None,           # 错误信息（如果失败）
    'duration': 0.123,       # 执行时间（秒）
    'details': {             # 额外的测试详情
        'key': 'value'
    }
}
```

### 2. 配置测试结果

```python
{
    'success': True,
    'environment': 'development',
    'debug': True,
    'enabled_providers': ['openai', 'google_ai'],
    'validation_valid': True,
    'warnings_count': 0,
    'errors_count': 0
}
```

### 3. 端点测试结果

```python
{
    'success': True,
    'status_code': 200,
    'duration': 0.045,
    'response_data': {...},
    'headers': {...}
}
```

## 环境设置

### 1. 自动环境设置

```python
# setup_test_environment 会自动：
# 1. 设置项目路径
# 2. 加载 .env 文件
# 3. 设置基础环境变量

project_root = setup_test_environment(
    debug=True,      # 启用调试模式
    load_env=True    # 加载 .env 文件
)
```

### 2. 手动环境设置

```python
import os
import sys
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# 设置环境变量
os.environ.setdefault('ENVIRONMENT', 'development')
os.environ.setdefault('DEBUG', 'true')
```

## 调试和故障排除

### 1. 启用详细日志

```python
# 在测试中启用详细日志
setup_test_environment(debug=True, load_env=True)

# 或者在配置测试中启用
result = ConfigTestUtils.test_config_loading(debug_logging=True)
```

### 2. 检查测试失败原因

```python
runner = TestRunner("调试测试")
runner.start()

result = runner.run_test("问题测试", problematic_test_function)

if not result.success:
    print(f"测试失败: {result.error}")
    print(f"执行时间: {result.duration:.3f}秒")
    print(f"详细信息: {result.details}")
```

### 3. 常见问题解决

#### 导入错误

```bash
# 确保在项目根目录运行测试
cd /path/to/ai-gen-hub
python unified_test.py

# 或者使用绝对路径
python /path/to/ai-gen-hub/unified_test.py
```

#### 配置问题

```python
# 检查配置加载
from ai_gen_hub.utils.test_utils import ConfigTestUtils

result = ConfigTestUtils.test_config_loading(debug_logging=True)
if not result['success']:
    print(f"配置加载失败: {result['error']}")
```

#### 端点测试失败

```python
# 检查应用创建
from ai_gen_hub.utils.test_utils import AppTestUtils

try:
    app = AppTestUtils.create_test_app()
    print("✅ 应用创建成功")
except Exception as e:
    print(f"❌ 应用创建失败: {e}")
```

## 编写新的测试脚本

### 1. 基础模板

```python
#!/usr/bin/env python3
"""
新测试脚本模板
"""

import sys
from pathlib import Path

# 使用通用测试工具
sys.path.insert(0, str(Path(__file__).parent / "src"))
from ai_gen_hub.utils.test_utils import (
    setup_test_environment, TestRunner, AppTestUtils, ConfigTestUtils
)

def my_test_function():
    """自定义测试函数"""
    try:
        # 测试逻辑
        return {'success': True, 'message': '测试通过'}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    # 设置环境
    setup_test_environment(debug=True, load_env=True)
    
    # 创建测试运行器
    runner = TestRunner("新测试脚本")
    runner.start()
    
    # 运行测试
    runner.run_test("自定义测试", my_test_function)
    
    # 完成测试
    return runner.finish()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

### 2. 最佳实践

1. **使用通用测试工具**: 避免重复代码，使用 `test_utils` 模块
2. **标准化结果格式**: 测试函数返回包含 `success` 字段的字典
3. **详细的错误信息**: 在失败时提供有用的错误信息
4. **适当的日志记录**: 使用调试模式获取详细信息
5. **资源清理**: 在异步测试中正确清理资源

## 相关文档

- [配置管理使用指南](./配置管理使用指南.md)
- [API 文档](./api.md)
- [开发环境配置建议](../开发环境配置建议.md)
