# 优化版本文本生成请求 - 集成指南

本指南详细说明了如何在现有项目中集成和使用优化版本的文本生成请求功能。

## 🚀 快速开始

### 1. 基础使用

#### 使用优化版本API（推荐）

```python
from ai_gen_hub.core.interfaces import (
    OptimizedTextGenerationRequest,
    Message,
    MessageRole,
    GenerationConfig,
    StreamConfig
)
from ai_gen_hub.services import TextGenerationService

# 创建优化版本请求
request = OptimizedTextGenerationRequest(
    messages=[
        Message(role=MessageRole.SYSTEM, content="你是一个专业的AI助手。"),
        Message(role=MessageRole.USER, content="请介绍一下机器学习的基本概念。")
    ],
    model="gpt-4",
    generation=GenerationConfig(
        temperature=0.8,
        max_tokens=1500,
        top_p=0.9
    ),
    stream=StreamConfig(enabled=False)
)

# 使用文本生成服务
service = TextGenerationService()
response = await service.generate_text_optimized(request)
print(response.choices[0].message.content)
```

#### 从传统格式迁移

```python
# 传统格式请求
legacy_data = {
    "messages": [{"role": "user", "content": "Hello"}],
    "model": "gpt-4",
    "temperature": 0.7,
    "max_tokens": 1000,
    "stream": False
}

# 自动转换为优化版本
optimized_request = OptimizedTextGenerationRequest.from_legacy_request(legacy_data)

# 使用优化版本服务
response = await service.generate_text_optimized(optimized_request)
```

### 2. API端点使用

#### V2 API端点（优化版本）

```bash
# 使用优化版本API
curl -X POST "http://localhost:8000/api/v2/text/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ],
    "model": "gpt-4",
    "generation": {
      "temperature": 0.8,
      "max_tokens": 1000
    },
    "stream": {
      "enabled": false
    }
  }'
```

#### 兼容性验证端点

```bash
# 验证请求与供应商的兼容性
curl -X POST "http://localhost:8000/api/v2/text/validate?provider_name=anthropic" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Test"}],
    "model": "claude-3-sonnet",
    "generation": {"temperature": 0.7}
  }'
```

## 🔧 高级功能

### 1. 供应商兼容性检查

```python
# 检查请求与不同供应商的兼容性
providers = ["openai", "anthropic", "google", "dashscope"]

for provider in providers:
    validation = request.validate_for_provider(provider)
    
    print(f"\n=== {provider.upper()} 兼容性 ===")
    if validation["errors"]:
        print("❌ 错误:", validation["errors"])
    if validation["warnings"]:
        print("⚠️  警告:", validation["warnings"])
    if validation["info"]:
        print("ℹ️  建议:", validation["info"])
```

### 2. 供应商特定参数

```python
# 获取不同供应商的API参数
openai_params = request.get_provider_specific_params("openai")
anthropic_params = request.get_provider_specific_params("anthropic")
google_params = request.get_provider_specific_params("google")

print("OpenAI参数:", openai_params)
print("Anthropic参数:", anthropic_params)
print("Google参数:", google_params)
```

### 3. 流式输出

```python
# 配置流式输出
stream_request = OptimizedTextGenerationRequest(
    messages=[Message(role=MessageRole.USER, content="写一个长故事")],
    model="gpt-4",
    generation=GenerationConfig(temperature=0.8, max_tokens=2000),
    stream=StreamConfig(
        enabled=True,
        chunk_size=100,
        include_usage=True
    )
)

# 处理流式响应
async for chunk in await service.generate_text_optimized(stream_request):
    if chunk.choices and chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="", flush=True)
```

### 4. 安全配置

```python
from ai_gen_hub.core.interfaces import SafetyConfig

# 配置安全设置
safe_request = OptimizedTextGenerationRequest(
    messages=[Message(role=MessageRole.USER, content="敏感内容测试")],
    model="gpt-4",
    safety=SafetyConfig(
        content_filter=True,
        safety_level="high",
        custom_filters=["violence", "hate_speech"]
    )
)
```

## 🔄 迁移策略

### 阶段1：评估现有代码

```python
# 1. 识别所有文本生成调用
# 搜索项目中的 TextGenerationRequest 使用

# 2. 分析参数使用模式
def analyze_request_patterns():
    """分析现有请求的参数使用模式"""
    # 统计最常用的参数组合
    # 识别复杂的配置需求
    pass
```

### 阶段2：渐进式迁移

```python
# 方案1：使用适配器包装现有代码
class LegacyTextService:
    def __init__(self):
        self.optimized_service = TextGenerationService()
    
    async def generate_text(self, legacy_request):
        """保持现有接口，内部使用优化版本"""
        optimized_request = OptimizedTextGenerationRequest.from_legacy_request(
            legacy_request.dict()
        )
        return await self.optimized_service.generate_text_optimized(optimized_request)

# 方案2：在新功能中使用优化版本
async def new_feature_with_optimized():
    """新功能直接使用优化版本"""
    request = OptimizedTextGenerationRequest(...)
    return await service.generate_text_optimized(request)
```

### 阶段3：完全迁移

```python
# 替换所有传统调用
# 旧代码：
# request = TextGenerationRequest(messages=..., model=..., temperature=...)
# response = await service.generate_text(request)

# 新代码：
request = OptimizedTextGenerationRequest(
    messages=...,
    model=...,
    generation=GenerationConfig(temperature=...)
)
response = await service.generate_text_optimized(request)
```

## 🧪 测试和验证

### 1. 单元测试

```python
import pytest
from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest

def test_optimized_request_creation():
    """测试优化版本请求创建"""
    request = OptimizedTextGenerationRequest(
        messages=[Message(role=MessageRole.USER, content="test")],
        model="gpt-4"
    )
    assert request.model == "gpt-4"
    assert request.generation.temperature == 0.7  # 默认值

def test_legacy_conversion():
    """测试传统格式转换"""
    legacy_data = {"messages": [...], "model": "gpt-4", "temperature": 0.8}
    optimized = OptimizedTextGenerationRequest.from_legacy_request(legacy_data)
    assert optimized.generation.temperature == 0.8
```

### 2. 集成测试

```python
@pytest.mark.asyncio
async def test_end_to_end_optimized():
    """端到端测试优化版本功能"""
    request = OptimizedTextGenerationRequest(...)
    
    # 测试兼容性验证
    validation = request.validate_for_provider("openai")
    assert len(validation["errors"]) == 0
    
    # 测试参数映射
    params = request.get_provider_specific_params("openai")
    assert "model" in params
    
    # 测试实际调用（需要模拟）
    with patch('ai_gen_hub.providers.openai_provider.OpenAIProvider') as mock:
        response = await service.generate_text_optimized(request)
        assert response is not None
```

## 📊 性能监控

### 1. 添加监控指标

```python
import time
from ai_gen_hub.utils.metrics import metrics_collector

async def monitored_text_generation(request):
    """带监控的文本生成"""
    start_time = time.time()
    
    try:
        # 记录请求类型
        request_type = "optimized" if isinstance(request, OptimizedTextGenerationRequest) else "legacy"
        metrics_collector.increment("text_generation.requests", tags={"type": request_type})
        
        # 执行生成
        response = await service.generate_text_optimized(request)
        
        # 记录成功
        processing_time = time.time() - start_time
        metrics_collector.histogram("text_generation.duration", processing_time)
        metrics_collector.increment("text_generation.success")
        
        return response
        
    except Exception as e:
        # 记录失败
        metrics_collector.increment("text_generation.errors", tags={"error": type(e).__name__})
        raise
```

### 2. 性能对比

```python
async def performance_comparison():
    """对比传统版本和优化版本的性能"""
    legacy_request = TextGenerationRequest(...)
    optimized_request = OptimizedTextGenerationRequest.from_legacy_request(legacy_request.dict())
    
    # 测试传统版本
    start = time.time()
    legacy_response = await service.generate_text(legacy_request)
    legacy_time = time.time() - start
    
    # 测试优化版本
    start = time.time()
    optimized_response = await service.generate_text_optimized(optimized_request)
    optimized_time = time.time() - start
    
    print(f"传统版本耗时: {legacy_time:.3f}s")
    print(f"优化版本耗时: {optimized_time:.3f}s")
    print(f"性能提升: {((legacy_time - optimized_time) / legacy_time * 100):.1f}%")
```

## ⚠️ 注意事项

### 1. 兼容性考虑

- **向后兼容**：现有的 `TextGenerationRequest` 仍然完全支持
- **API版本**：V1 API 保持不变，V2 API 提供优化功能
- **供应商差异**：不同供应商的功能支持度不同，需要检查兼容性

### 2. 最佳实践

- **渐进迁移**：不要一次性替换所有代码，逐步迁移
- **兼容性检查**：在生产环境使用前进行充分的兼容性测试
- **监控告警**：设置适当的监控和告警机制
- **文档更新**：及时更新相关文档和API说明

### 3. 故障排除

```python
# 常见问题诊断
def diagnose_request_issues(request):
    """诊断请求问题"""
    if isinstance(request, OptimizedTextGenerationRequest):
        # 检查供应商兼容性
        for provider in ["openai", "anthropic", "google", "dashscope"]:
            validation = request.validate_for_provider(provider)
            if validation["errors"]:
                print(f"{provider} 兼容性错误: {validation['errors']}")
    
    # 检查参数合理性
    if request.generation.max_tokens and request.generation.max_tokens > 32000:
        print("警告: max_tokens 可能超出某些供应商限制")
    
    # 检查消息格式
    if not request.messages:
        print("错误: 消息列表为空")
```

## 📚 更多资源

- [API参考文档](api_reference.md)
- [供应商兼容性矩阵](provider_compatibility.md)
- [性能优化指南](performance_optimization.md)
- [故障排除手册](troubleshooting.md)
