# AI Gen Hub 调试端点使用说明

## 📋 概述

AI Gen Hub 提供了丰富的调试端点，帮助开发者监控应用状态、查看系统信息和进行故障排除。

## 🔧 可用的调试端点

### 系统信息类

| 端点 | 方法 | 描述 |
|------|------|------|
| `/debug/api/system/info` | GET | 获取基础系统信息 |
| `/debug/api/system/detailed` | GET | 获取详细系统信息 |
| `/debug/api/system/processes` | GET | 获取系统进程信息 |
| `/debug/system` | GET | 系统信息页面 |

### 配置管理类

| 端点 | 方法 | 描述 |
|------|------|------|
| `/debug/config` | GET | 配置信息页面 |
| `/debug/api/config` | GET | 获取应用配置 |
| `/debug/api/config/environment` | GET | 获取环境变量 |
| `/debug/api/config/runtime` | GET | 获取运行时配置 |

### 日志管理类

| 端点 | 方法 | 描述 |
|------|------|------|
| `/debug/logs` | GET | 日志查看页面 |
| `/debug/api/logs` | GET | 获取应用日志 |
| `/debug/api/logs/levels` | GET | 获取日志级别统计 |
| `/debug/api/logs/stats` | GET | 获取日志统计信息 |

### 监控指标类

| 端点 | 方法 | 描述 |
|------|------|------|
| `/debug/metrics` | GET | 监控指标页面 |
| `/debug/api/cache/status` | GET | 获取缓存状态 |
| `/debug/api/database/status` | GET | 获取数据库状态 |

### 开发工具类

| 端点 | 方法 | 描述 |
|------|------|------|
| `/debug/` | GET | 调试主页 |
| `/debug/tools` | GET | 开发工具页面 |
| `/debug/api-test` | GET | API 测试页面 |
| `/debug/api/test-endpoint` | POST | 测试端点 |
| `/debug/api/endpoints` | GET | 获取端点列表 |
| `/debug/api/endpoints/detailed` | GET | 获取详细端点信息 |
| `/debug/api/openapi-spec` | GET | 获取 OpenAPI 规范 |

### 诊断工具

| 端点 | 方法 | 描述 |
|------|------|------|
| `/diagnostic` | GET | 应用诊断信息（所有环境可用） |

## 🚀 使用示例

### 1. 检查系统状态

```bash
# 获取基础系统信息
curl http://localhost:8000/debug/api/system/info

# 获取详细系统信息
curl http://localhost:8000/debug/api/system/detailed
```

### 2. 查看配置信息

```bash
# 获取应用配置
curl http://localhost:8000/debug/api/config

# 获取环境变量
curl http://localhost:8000/debug/api/config/environment
```

### 3. 监控应用状态

```bash
# 检查缓存状态
curl http://localhost:8000/debug/api/cache/status

# 检查数据库状态
curl http://localhost:8000/debug/api/database/status
```

### 4. 查看日志信息

```bash
# 获取应用日志
curl http://localhost:8000/debug/api/logs

# 获取日志统计
curl http://localhost:8000/debug/api/logs/stats
```

### 5. 诊断应用问题

```bash
# 获取诊断信息（推荐首先使用）
curl http://localhost:8000/diagnostic | jq

# 获取所有端点信息
curl http://localhost:8000/debug/api/endpoints/detailed
```

## 🔒 访问权限

### 开发环境
- **调试端点**：无需认证，直接访问
- **诊断端点**：无需认证，直接访问

### 生产环境
- **调试端点**：不可用（未注册）
- **诊断端点**：无需认证，可用于基础诊断

## 🛠️ 故障排除

### 调试端点返回 404

1. **检查环境配置**：
   ```bash
   curl http://localhost:8000/diagnostic
   ```

2. **确认调试模式**：
   - 检查 `DEBUG=true` 或 `ENVIRONMENT=development`
   - 查看诊断端点中的 `debug_route_condition`

3. **检查应用日志**：
   - 查找 "注册调试路由" 相关日志
   - 确认路由注册成功

### 调试端点返回 401

1. **检查认证中间件**：
   - 确认在开发环境下调试路径是公开的
   - 检查环境变量设置

2. **使用诊断端点**：
   - 诊断端点在所有环境下都是公开的
   - 可以通过诊断端点检查应用状态

### 调试端点返回 500

1. **检查应用状态**：
   - 确认应用正确初始化
   - 检查 `request.app.state.settings` 是否存在

2. **重启应用**：
   - 停止应用
   - 确认虚拟环境激活
   - 重新启动应用

## 📊 响应格式

### 成功响应

```json
{
  "status": "success",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2025-08-13T14:54:47.123456"
}
```

### 错误响应

```json
{
  "status": "error",
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2025-08-13T14:54:47.123456"
}
```

## 🎯 最佳实践

1. **优先使用诊断端点**：遇到问题时首先访问 `/diagnostic` 获取应用状态
2. **检查日志**：使用日志端点查看详细的错误信息
3. **监控系统资源**：定期检查系统信息和进程状态
4. **配置验证**：使用配置端点确认应用配置正确
5. **端点测试**：使用 API 测试端点验证功能

## 🔗 相关文档

- [路由配置修复指南](./路由配置修复指南.md)
- [API 文档](http://localhost:8000/docs)（开发环境）
- [应用配置说明](../README.md)

---

**更新时间**：2025-08-13  
**版本**：v1.0.0
