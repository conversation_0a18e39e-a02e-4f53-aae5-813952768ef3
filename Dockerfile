# AI Gen Hub Docker镜像
# 基于Python 3.11的多阶段构建，优化镜像大小和安全性

# =============================================================================
# 构建阶段
# =============================================================================
FROM python:3.11-slim as builder

# 设置构建参数
ARG POETRY_VERSION=1.7.1

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry==$POETRY_VERSION

# 配置Poetry
ENV POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY pyproject.toml poetry.lock ./

# 安装依赖
RUN poetry install --only=main --no-root && rm -rf $POETRY_CACHE_DIR

# =============================================================================
# 运行阶段
# =============================================================================
FROM python:3.11-slim as runtime

# 创建非root用户
RUN groupadd -r aiuser && useradd -r -g aiuser aiuser

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/app/.venv/bin:$PATH"

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 从构建阶段复制虚拟环境
COPY --from=builder /app/.venv /app/.venv

# 复制应用代码
COPY src/ ./src/
COPY README.md ./

# 创建必要的目录
RUN mkdir -p /app/storage /app/logs && \
    chown -R aiuser:aiuser /app

# 切换到非root用户
USER aiuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000 9090

# 设置入口点
ENTRYPOINT ["python", "-m", "ai_gen_hub.main"]
CMD ["serve", "--host", "0.0.0.0", "--port", "8000"]

# =============================================================================
# 开发阶段（可选）
# =============================================================================
FROM builder as development

# 安装开发依赖
RUN poetry install --no-root

# 复制应用代码
COPY . .

# 切换到非root用户
USER aiuser

# 开发模式入口点
CMD ["poetry", "run", "python", "-m", "ai_gen_hub.main", "serve", "--reload"]
