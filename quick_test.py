#!/usr/bin/env python3
"""
快速测试修复效果

使用增强的配置管理器和通用测试工具进行测试。
"""

import sys
import time
from pathlib import Path

# 使用通用测试工具设置环境
sys.path.insert(0, str(Path(__file__).parent / "src"))
from ai_gen_hub.utils.test_utils import (
    setup_test_environment, TestRunner, AppTestUtils, ConfigTestUtils
)

def test_system_info_function():
    """测试 get_system_info 函数

    Returns:
        测试结果字典
    """
    try:
        from ai_gen_hub.api.routers.debug import get_system_info

        # 测试多次调用性能
        execution_times = []
        for i in range(3):
            start_time = time.time()
            system_info = get_system_info()
            execution_time = time.time() - start_time
            execution_times.append(execution_time)

            if execution_time > 0.5:
                return {
                    'success': False,
                    'error': f'第{i+1}次调用执行时间过长: {execution_time:.3f}秒',
                    'execution_times': execution_times
                }

        avg_time = sum(execution_times) / len(execution_times)

        return {
            'success': True,
            'avg_execution_time': avg_time,
            'max_execution_time': max(execution_times),
            'cpu_percent': system_info.cpu_percent,
            'memory_percent': system_info.memory_percent
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def test_system_info_endpoint():
    """测试 system/info 端点

    Returns:
        测试结果字典
    """
    try:
        app = AppTestUtils.create_test_app()
        result = AppTestUtils.test_endpoint(app, "/debug/api/system/info", "GET")

        # 检查响应时间
        if result['success'] and result['duration'] > 1.0:
            result['success'] = False
            result['error'] = f"响应时间过长: {result['duration']:.3f}秒"

        return result

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def test_test_endpoint_simple():
    """简单测试 test-endpoint 接口

    Returns:
        测试结果字典
    """
    try:
        app = AppTestUtils.create_test_app()

        # 简单的测试数据
        test_data = {
            "url": "/health",
            "method": "GET",
            "headers": {},
            "body": "",
            "params": {}
        }

        result = AppTestUtils.test_endpoint(
            app,
            "/debug/api/test-endpoint",
            "POST",
            json=test_data,
            timeout=10
        )

        # 检查响应内容
        if result['success'] and result['status_code'] == 200:
            response_data = result.get('response_data', {})
            if isinstance(response_data, dict):
                endpoint_success = response_data.get('success', False)
                if not endpoint_success:
                    result['success'] = False
                    result['error'] = f"端点测试失败: {response_data.get('error', 'Unknown')}"
                else:
                    result['endpoint_test_success'] = True

        # 检查超时
        if result.get('duration', 0) >= 9:
            result['success'] = False
            result['error'] = f"请求超时: {result['duration']:.3f}秒"

        return result

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """主函数"""
    # 设置测试环境
    setup_test_environment(debug=True, load_env=True)

    # 创建测试运行器
    runner = TestRunner("快速测试修复效果")
    runner.start()

    # 运行配置测试
    runner.run_test("配置加载测试", ConfigTestUtils.test_config_loading, True)

    # 运行功能测试
    runner.run_test("get_system_info函数测试", test_system_info_function)
    runner.run_test("system/info端点测试", test_system_info_endpoint)
    runner.run_test("test-endpoint接口测试", test_test_endpoint_simple)

    # 完成测试
    all_passed = runner.finish()

    if all_passed:
        print("\n💡 修复总结:")
        print("1. ✅ 配置获取逻辑优化 - 优先从环境变量获取配置")
        print("2. ✅ system/info 接口响应时间优化")
        print("3. ✅ test-endpoint 接口超时保护")
        print("4. ✅ 统一测试工具使用")

    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
