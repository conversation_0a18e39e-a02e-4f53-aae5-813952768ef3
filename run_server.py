#!/usr/bin/env python3
"""
AI Gen Hub 服务器启动脚本
直接运行方式，无需安装模块
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 设置环境变量
os.environ.setdefault('ENVIRONMENT', 'development')
os.environ.setdefault('DEBUG', 'true')

def check_dependencies():
    """检查必要的依赖"""
    required_modules = [
        'fastapi', 'uvicorn', 'pydantic', 'click', 'httpx'
    ]

    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        print("❌ 缺少必要的依赖模块:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n💡 请安装依赖:")
        print("   pip install -r requirements.txt")
        print("   或者:")
        print("   pip install fastapi uvicorn pydantic click httpx")
        return False

    return True

def main():
    """主函数"""
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    try:
        # 导入AI Gen Hub模块
        from ai_gen_hub.main import cli

        # 运行CLI，传递serve命令和参数
        import sys
        # 设置命令行参数为serve命令
        sys.argv = ['ai_gen_hub', 'serve', '--host', '0.0.0.0', '--port', '8001']
        cli()

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已安装所有依赖:")
        print("   pip install -r requirements.txt")
        print("\n🔧 或者尝试修复脚本:")
        print("   chmod +x fix_module_import.sh")
        print("   ./fix_module_import.sh")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
