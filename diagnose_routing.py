#!/usr/bin/env python3
"""
AI Gen Hub 路由诊断工具

用于诊断和检查路由配置问题，特别是 /debug 路径下的端点访问问题
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def load_env_file():
    """手动加载 .env 文件"""
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        print(f"✅ 已加载 .env 文件: {env_file}")
    else:
        print(f"❌ .env 文件不存在: {env_file}")

# 在导入其他模块之前加载环境变量
load_env_file()

def check_env_file():
    """检查 .env 文件状态"""
    print("🔍 检查 .env 文件状态...")
    
    env_file = project_root / ".env"
    if env_file.exists():
        print(f"✅ .env 文件存在: {env_file}")
        print("📄 .env 文件内容:")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip() and not line.startswith('#'):
                    print(f"   {line_num:2d}: {line.rstrip()}")
    else:
        print(f"❌ .env 文件不存在: {env_file}")
    
    print()

def check_environment_variables():
    """检查关键环境变量"""
    print("🔍 检查关键环境变量...")
    
    key_vars = [
        'ENVIRONMENT', 'DEBUG', 'APP_NAME', 'API_HOST', 'API_PORT',
        'JWT_SECRET_KEY', 'API_KEY'
    ]
    
    for var in key_vars:
        value = os.environ.get(var)
        if value:
            # 对敏感信息进行脱敏
            if 'key' in var.lower() or 'secret' in var.lower():
                display_value = f"***{value[-4:]}" if len(value) > 4 else "***"
            else:
                display_value = value
            print(f"   ✅ {var} = {display_value}")
        else:
            print(f"   ❌ {var} = (未设置)")
    
    print()

def check_settings_loading():
    """检查配置加载"""
    print("🔍 检查配置加载...")
    
    try:
        from ai_gen_hub.config import get_settings
        
        settings = get_settings()
        print("✅ 配置加载成功")
        print(f"   应用名称: {settings.app_name}")
        print(f"   应用版本: {settings.app_version}")
        print(f"   运行环境: {settings.environment}")
        print(f"   调试模式: {settings.debug}")
        print(f"   API主机: {settings.api_host}")
        print(f"   API端口: {settings.api_port}")
        
        # 检查调试路由注册条件
        should_register_debug = settings.debug or settings.environment.lower() != "production"
        print(f"   应该注册调试路由: {should_register_debug}")
        
        return settings
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    print()

def check_app_creation():
    """检查应用创建和路由注册"""
    print("🔍 检查应用创建和路由注册...")
    
    try:
        from ai_gen_hub.api.app import create_app
        
        app = create_app()
        print("✅ FastAPI应用创建成功")
        
        # 检查路由
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                for method in route.methods:
                    if method != 'HEAD':
                        routes.append(f"{method} {route.path}")
        
        print(f"   总路由数量: {len(routes)}")
        
        # 检查调试路由
        debug_routes = [route for route in routes if '/debug' in route]
        print(f"   调试路由数量: {len(debug_routes)}")
        
        if debug_routes:
            print("   调试路由列表:")
            for route in sorted(debug_routes):
                print(f"     {route}")
        else:
            print("   ❌ 未找到调试路由!")
        
        return app, routes
        
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None, []
    
    print()

def check_debug_router():
    """检查调试路由模块"""
    print("🔍 检查调试路由模块...")
    
    try:
        from ai_gen_hub.api.routers.debug import router as debug_router
        
        print("✅ 调试路由模块导入成功")
        
        # 检查路由定义
        routes = []
        for route in debug_router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                for method in route.methods:
                    if method != 'HEAD':
                        routes.append(f"{method} {route.path}")
        
        print(f"   调试路由模块中的路由数量: {len(routes)}")
        print("   调试路由定义:")
        for route in sorted(routes):
            print(f"     {route}")
        
        return debug_router
        
    except Exception as e:
        print(f"❌ 调试路由模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    print()

def main():
    """主诊断函数"""
    print("🚀 AI Gen Hub 路由诊断工具")
    print("=" * 50)
    print()
    
    # 检查 .env 文件
    check_env_file()
    
    # 检查环境变量
    check_environment_variables()
    
    # 检查配置加载
    settings = check_settings_loading()
    
    # 检查调试路由模块
    debug_router = check_debug_router()
    
    # 检查应用创建
    app, routes = check_app_creation()
    
    print("=" * 50)
    print("📊 诊断总结:")
    
    if settings:
        print("✅ 配置加载正常")
    else:
        print("❌ 配置加载异常")
    
    if debug_router:
        print("✅ 调试路由模块正常")
    else:
        print("❌ 调试路由模块异常")
    
    if app:
        debug_routes = [route for route in routes if '/debug' in route]
        if debug_routes:
            print("✅ 调试路由注册正常")
        else:
            print("❌ 调试路由未注册")
    else:
        print("❌ 应用创建异常")
    
    print()
    print("🔧 建议的修复步骤:")
    print("1. 确保 .env 文件存在且配置正确")
    print("2. 检查环境变量 DEBUG=true 和 ENVIRONMENT=development")
    print("3. 验证配置加载逻辑")
    print("4. 检查路由注册条件")

if __name__ == "__main__":
    main()
